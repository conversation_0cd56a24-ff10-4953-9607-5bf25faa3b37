FROM debian:10.5-slim

RUN apt-get update && \
	apt-get -y install curl unzip php7.3 php7.3-common php7.3-curl php7.3-cli php7.3-bcmath php7.3-ctype \
		php7.3-json php7.3-mbstring php7.3-mysql php7.3-tokenizer php7.3-xml php7.3-zip && \
	apt-get clean && \
	rm -rf /var/lib/apt/lists/*

RUN curl -sS https://getcomposer.org/installer | php && \
	mv composer.phar /usr/local/bin/composer && \
	chmod 755 /usr/local/bin/composer

WORKDIR /laravel-app

CMD composer install && php artisan serve --host 0.0.0.0
