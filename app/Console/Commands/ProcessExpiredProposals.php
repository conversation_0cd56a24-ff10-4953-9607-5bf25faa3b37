<?php

namespace App\Console\Commands;

use App\Models\Proposal;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Mail;

class ProcessExpiredProposals extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cron:process_expired_proposals';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Checks to see if any currently open proposals have passed their deadline';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        Proposal::with('issue.discussion.group.active_members', 'users_voted')
            ->where('voting_closed', '=', false)
            ->where('deadline', '<', date('Y-m-d H:i:s'))
            ->get()->each(function ($proposal) {
                $proposal->test_approved();
                $proposal->voting_closed = true;
                if ($proposal->vote_passed) {
                    $proposal->payment_status = 'under_review';
                }
                $proposal->save();

                $group = $proposal->issue->discussion->group;

                // Figure out which members we want to email:
                if (! $proposal->vote_passed || $group->is_network) {
                    // If the vote failed, or the group is the Common Change Network, only send to the user who created the issue.
                    $members_to_email = $proposal->creator()->get();
                } else {
                    // Otherwise, we only want to email active members who want email notifications and voted on this proposal.
                    $members_to_email = $group->active_members->filter(function ($user) use ($proposal) {
                        return ($user->pivot->email_pref != 'none')
                                || ($user->id == $proposal->creator->id);
                    });
                    $members_to_email = $members_to_email->intersect($proposal->users_voted);
                }

                $subject = ($proposal->vote_passed)
                                ? $group->name.': Request "'.$proposal->issue->title.'" has been approved!'
                                : $group->name.': Request "'.$proposal->issue->title.'" has expired without approval.';

                // Send the email(s)!
                foreach ($members_to_email as $member) {
                    Mail::send(['emails.voting_result', 'emails.voting_result-text'],
                        ['proposal' => $proposal, 'member' => $member],
                        function ($message) use ($member, $subject) {
                            $message->to($member->email, $member->firstname.' '.$member->lastname);
                            $message->subject($subject);
                        }
                    );
                }
            });

        return Command::SUCCESS;
    }
}
