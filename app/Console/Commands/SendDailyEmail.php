<?php

namespace App\Console\Commands;

use App\Models\Discussion;
use App\Models\Proposal;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Mail;

class GroupDiscussions
{
    public $group;

    public $discussions = [];   // array of Discussion objects

    public $rendered_activity;

    public $rendered_activity_text;

    public function __construct($group)
    {
        $this->group = $group;
    }

    public function add_discussion($discussion)
    {
        $this->discussions[] = $discussion;
    }

    public function render_activity()
    {
        $this->rendered_activity = view('emails.daily.group_activity')
            ->withDiscussions($this->discussions)
            ->render();

        $this->rendered_activity_text = view('emails.daily.group_activity-text')
            ->withDiscussions($this->discussions)
            ->render();
    }
}

class GroupActivity
{
    public $group;

    public $new_discussions;

    public $unvoted_proposals = [];

    public function __construct($group)
    {
        $this->group = $group;
    }

    public function set_discussions($new_discussions)
    {
        $this->new_discussions = $new_discussions;
    }

    public function add_proposal($proposal)
    {
        $this->unvoted_proposals[] = $proposal;
    }
}

class UserGroupNotifications
{
    public $user;   // points to User object

    public $groups = []; // array of GroupActivity objects;

    public function __construct($user)
    {
        $this->user = $user;
    }

    private function get_or_create_activity($group)
    {
        $group_id = $group->id;
        if (isset($this->groups[$group_id])) {
            return $this->groups[$group_id];
        } else {
            $group_activity = new GroupActivity($group);
            $this->groups[$group_id] = $group_activity;

            return $group_activity;
        }
    }

    public function set_discussions($group_discussions)
    {
        $group_activity = $this->get_or_create_activity($group_discussions->group);
        $group_activity->set_discussions($group_discussions);
    }

    public function add_proposal($proposal)
    {
        $group_activity = $this->get_or_create_activity($proposal->issue->discussion->group);
        $group_activity->add_proposal($proposal);
    }
}

class SendDailyEmail extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cron:send_daily_email';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Updates users regarding activity in their groups';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        // First, we'll look for discussions that have been updated in the last 24 hours
        //  (discussions are updated anytime someone posts to or modifies the discussion).
        //  We'll group those discussions by Group.
        $group_discussions_cache = [];

        $recent_discussions = Discussion::with('recent_posts', 'issue', 'group.active_members')
            ->hasRecentPosts()
            ->get();

        foreach ($recent_discussions as $discussion) {
            $group = $discussion->group;
            if ($group->is_network) {
                continue;
            }  // Don't email activity for the Common Change Network
            $group_id = $group->id;
            if (! isset($group_discussions_cache[$group_id])) {
                $group_discussions_cache[$group_id] = new GroupDiscussions($group);
            }
            $group_discussions_cache[$group_id]->add_discussion($discussion);
        }

        $user_cache = [];  // a cache for each user that we'll notify.

        // Next, we'll iterate through the groups, render the discussions for the email,
        // and cache that info for each group member who receives the daily digest.
        foreach ($group_discussions_cache as $group_discussions) {
            $group_discussions->render_activity();
            $members_to_email = $group_discussions->group->active_members->filter(function ($user) {
                return $user->pivot->email_pref == 'daily_digest';
            });
            foreach ($members_to_email as $user) {
                $user_id = $user->id;
                if (! isset($user_cache[$user_id])) {
                    $user_cache[$user_id] = new UserGroupNotifications($user);
                }
                $user_cache[$user_id]->set_discussions($group_discussions);
            }
        }

        // Next, get all open proposals:
        $open_proposals = Proposal::with('issue.discussion.group.active_members', 'issue.creator', 'users_voted')
            ->isOpenForVoting()
            ->get();

        foreach ($open_proposals as $proposal) {
            $group = $proposal->issue->discussion->group;
            if ($group->is_network) {
                continue;
            }  // Don't email activity for the Common Change Network

            // Only notify users who haven't voted on this proposal:
            $members_to_email = $group->active_members->diff($proposal->users_voted)->filter(function ($user) {
                return $user->pivot->email_pref == 'daily_digest';
            });
            foreach ($members_to_email as $user) {
                $user_id = $user->id;
                if (! isset($user_cache[$user_id])) {
                    $user_cache[$user_id] = new UserGroupNotifications($user);
                }
                $user_cache[$user_id]->add_proposal($proposal);
            }
        }

        // Finally, send an email to each user in the user_cache:
        foreach ($user_cache as $user_data) {
            $user = $user_data->user;
            Mail::send(['emails.daily.all_activity', 'emails.daily.all_activity-text'], ['user_data' => $user_data], function ($message) use ($user) {
                $message->to($user->email, $user->firstname.' '.$user->lastname);
                $message->subject('Common Change: Group activity in the last 24 hours');
            });
        }

        return Command::SUCCESS;
    }
}
