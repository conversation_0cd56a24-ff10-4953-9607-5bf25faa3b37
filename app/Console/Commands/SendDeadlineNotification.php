<?php

namespace App\Console\Commands;

use App\Models\OnesignalApiInterface;
use App\Models\Proposal;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\URL;

class SendDeadlineNotification extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cron:send_deadline_notification';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "Sends notification if proposal hasn't passed and is within 1 day of its deadline";

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        Proposal::with('issue.discussion.group.active_members', 'users_voted')
            ->where('deadline_notification', '=', false)
            ->where('deadline', '<', date('Y-m-d H:i:s', strtotime('now +1 day')))
            ->where('deadline', '>=', date('Y-m-d H:i:s'))
            ->get()->each(function ($proposal) {
                $proposal->test_approved();
                $proposal->deadline_notification = true;
                $proposal->save();

                $group = $proposal->issue->discussion->group;

                // Determine who to email:
                if ($group->is_network) {
                    $members_to_email = $proposal->creator()->get();
                } else {
                    // email all active members who haven't voted
                    $members_to_email = $group->active_members->diff($proposal->users_voted);
                }

                $subject = $group->name.': Voting deadline approaching for '.$proposal->issue->title;
                foreach ($members_to_email as $member) {
                    Mail::send(['emails.deadline_notification', 'emails.deadline_notification-text'],
                        ['proposal' => $proposal, 'member' => $member],
                        function ($message) use ($member, $subject) {
                            $message->to($member->email, $member->firstname.' '.$member->lastname);
                            $message->subject($subject);
                        });
                }

                // Finally, send app notification to group members that have a mobile app associated w/ their account:
                $onesignal_ids = $group->getOneSignalIDs();
                $issue = $proposal->issue;
                if (count($onesignal_ids)) {
                    $subject = 'Request deadline approaching for '.$group->name;
                    $message = $issue->title.': Please take some time to review the conversation and share any additional thoughts you may have.';
                    $url = URL::route('issue.show', ['id' => $issue->id]);
                    $mobile = new OnesignalApiInterface;
                    $mobile->postToUsers($onesignal_ids, $subject, $message, $url);
                }

            });

        return Command::SUCCESS;
    }
}
