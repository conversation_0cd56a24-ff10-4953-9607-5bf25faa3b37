<?php

namespace App\Console\Commands;

use App\Models\Discussion;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Mail;

// We'll potentially have multiple discussions for one group,
// but we only want to track down the appropriate members once:
class GroupCache
{
    private $group_members = [];

    public function add($group)
    {
        $id = $group->id;
        if (! isset($this->group_members[$id])) {
            $this->group_members[$id] = $group->active_members->filter(function ($user) {
                return $user->pivot->email_pref == 'daily_per_discussion';
            });
        }
    }

    public function get_members($group)
    {
        return $this->group_members[$group->id];
    }
}

class SendDailyByDiscussion extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cron:send_daily_by_discussion';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Daily update for users w/ daily_per_discussion email pref';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $group_cache = new GroupCache;

        $recent_discussions = Discussion::with('recent_posts', 'issue', 'group.active_members')->hasRecentPosts()->get();

        // Then, we'll look for discussions that have been updated in the last 24 hours
        //  (discussions are updated anytime someone posts to or modifies the discussion)
        foreach ($recent_discussions as $discussion) {
            $group = $discussion->group;
            if ($group->is_network) {
                continue;
            }  // Don't email activity for the Common Change Network
            $group_cache->add($group);

            $prerendered = view('emails.daily.discussion_detail')
                ->withGroup($group)
                ->withDiscussion($discussion)
                ->render();
            $prerendered_text = view('emails.daily.discussion_detail-text')
                ->withGroup($group)
                ->withDiscussion($discussion)
                ->render();
            $subject = $group->name.': '.$discussion->title;

            // Finally, send an email to each user in the list:
            foreach ($group_cache->get_members($group) as $member) {
                Mail::send(['emails.daily.by_discussion', 'emails.daily.by_discussion-text'], [
                    'prerendered' => $prerendered,
                    'prerendered_text' => $prerendered_text,
                ], function ($message) use ($member, $subject) {
                    $message->to($member->email, $member->firstname.' '.$member->lastname);
                    $message->subject($subject);
                });
            }
        }

        return Command::SUCCESS;
    }
}
