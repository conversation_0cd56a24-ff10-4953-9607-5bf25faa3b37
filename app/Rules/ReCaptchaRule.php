<?php

namespace App\Rules;

use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Validation\ValidationException;
use ReCaptcha\ReCaptcha;

class ReCaptchaRule implements ValidationRule
{
    private $error_msg = '';

    /**
     * Create a new rule instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Run the validation rule.
     *
     * @param string $attribute
     * @param mixed $value
     * @param \Closure $fail
     */
    public function validate(string $attribute, mixed $value, \Closure $fail): void
    {
        if (empty($value)) {
            $fail(':attribute field is required.');
            return;
        }

        $recaptcha = new ReCaptcha(env('GOOGLE_CAPTCHA_PRIVATE_KEY'));
        $resp = $recaptcha->setExpectedHostname($_SERVER['SERVER_NAME'])
            ->setScoreThreshold(0.5)
            ->verify($value, $_SERVER['REMOTE_ADDR']);
        
        if (! $resp->isSuccess()) {
            $fail('ReCaptcha request failed.');
            return;
        }
        
        if ($resp->getScore() < 0.5) {
            $fail('Failed to validate captcha.');
            return;
        }
    }
}
