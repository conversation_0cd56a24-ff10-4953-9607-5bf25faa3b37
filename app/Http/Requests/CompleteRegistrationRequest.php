<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class CompleteRegistrationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $rules = [
            'email' => 'required|email:rfc,dns|unique:users,email',
            'firstname' => 'required|string|max:255|regex:/^[\pL\pN \'\-.]+$/',
            'lastname' => 'required|string|max:255|regex:/^[\pL\pN \'\-.]+$/',
            'address.addr1' => 'required|string|max:255|not_regex:/<script>/',
            'address.addr2' => 'nullable|string|max:255|not_regex:/<script>/',
            'address.city' => 'required|string|max:255|regex:/^[\pL\pN \'\-.]+$/',
            'address.state' => 'required|string|max:255',
            'address.postcode' => 'required|string|max:20|regex:/^[a-zA-Z0-9\-\s]+$/',
            'address.country' => 'required|string|size:2|exists:countries,code',
            'profile_pic' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048|dimensions:min_width=250,min_height=250',
        ];

        // Only require password if it's not already set in the session
        if (!session('verified_password')) {
            $rules['password'] = 'required|string|min:8|confirmed|regex:/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/';
        }

        return $rules;
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'email.unique' => 'This email address is already registered. Please use a different email or try logging in.',
            'email.email' => 'Please enter a valid email address.',
            'email.required' => 'Email address is required.',

            'firstname.required' => 'Please enter your first name.',
            'firstname.regex' => 'First name can only contain letters, numbers, spaces, and basic punctuation.',
            'firstname.max' => 'First name cannot exceed 255 characters.',

            'lastname.required' => 'Please enter your last name.',
            'lastname.regex' => 'Last name can only contain letters, numbers, spaces, and basic punctuation.',
            'lastname.max' => 'Last name cannot exceed 255 characters.',

            'address.addr1.required' => 'Please enter your address.',
            'address.addr1.not_regex' => 'Address contains invalid characters.',
            'address.addr2.not_regex' => 'Address line 2 contains invalid characters.',

            'address.city.required' => 'Please enter your city.',
            'address.city.regex' => 'City name can only contain letters, numbers, spaces, and basic punctuation.',

            'address.state.required' => 'Please select your state.',

            'address.postcode.required' => 'Please enter your postal code.',
            'address.postcode.regex' => 'Postal code can only contain letters, numbers, spaces, and hyphens.',

            'address.country.required' => 'Please select your country.',
            'address.country.exists' => 'Please select a valid country.',

            'profile_pic.image' => 'The profile picture must be an image file.',
            'profile_pic.mimes' => 'The profile picture must be a JPEG, PNG, JPG, or GIF file.',
            'profile_pic.max' => 'The profile picture must be less than 2MB.',
            'profile_pic.dimensions' => 'The profile picture must be at least 250x250 pixels.',

            'password.required' => 'Please enter a password.',
            'password.min' => 'Password must be at least 8 characters.',
            'password.confirmed' => 'Password confirmation does not match.',
            'password.regex' => 'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character.',
        ];
    }
}
