<?php

namespace App\Http\Requests;

use App\Models\User;
use App\Rules\ReCaptchaRule;
use Illuminate\Foundation\Http\FormRequest;

class RegisterUserRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $rules = User::$rules;

        if (! empty($_ENV['GOOGLE_CAPTCHA_PUBLIC_KEY'])) {
            $rules['recaptcha_token'] = ['required', new ReCaptchaRule($this->recaptcha_token)];
        }

        return $rules;
    }
}
