<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class UpdateUserActivity
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        // To help track last user activity, update the user's timestamp,
        // but only if at least 1 hour has passed
        // (Really, this should be done with logging, not here)
        $user = Auth::user();
        $next_update = $user->updated_at->addhours(1);
        if (time() > $next_update->timestamp) {
            $user->touch();
        }

        return $next($request);
    }
}
