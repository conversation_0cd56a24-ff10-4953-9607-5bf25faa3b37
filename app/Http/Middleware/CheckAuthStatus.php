<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use App\Models\User;
use Symfony\Component\HttpFoundation\Response;

class CheckAuthStatus
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Log authentication status for debugging
        Log::info('Auth status middleware', [
            'path' => $request->path(),
            'authenticated' => Auth::check(),
            'user_id' => Auth::check() ? Auth::id() : null,
            'session_id' => session()->getId(),
            'auth_checked' => session('auth_checked'),
            'session_user_id' => session('user_id')
        ]);

        // Log request information
        Log::info('CheckAuthStatus middleware', [
            'path' => $request->path(),
            'authenticated' => Auth::check(),
            'user_id' => Auth::check() ? Auth::id() : null,
            'session_id' => session()->getId(),
            'auth_checked' => session('auth_checked'),
            'session_user_id' => session('user_id'),
            'secure' => $request->secure(),
            'https' => isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] == 'on',
            'host' => $request->getHost()
        ]);

        // Check if we have session data indicating the user should be logged in
        if (!Auth::check()) {
            $userId = null;

            // Try to get user ID from session
            if (session('auth_checked') && session('user_id')) {
                $userId = session('user_id');
                Log::info('Found user ID in session', ['user_id' => $userId]);
            }
            // Try to get user ID from cookie
            else if ($request->cookie('auth_user_id')) {
                $userId = $request->cookie('auth_user_id');
                Log::info('Found user ID in cookie', ['user_id' => $userId]);
            }

            if ($userId) {
                $user = User::find($userId);

                if ($user) {
                    Log::info('Attempting to re-authenticate user', [
                        'user_id' => $userId,
                        'email' => $user->email,
                        'secure' => $request->secure(),
                        'https' => isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] == 'on'
                    ]);

                    // Try to log the user in again
                    Auth::login($user);

                    // Store in session
                    session()->put('auth_checked', true);
                    session()->put('user_id', $user->id);
                    session()->save();

                    // Regenerate session to ensure it's properly set
                    session()->regenerate();

                    if (Auth::check()) {
                        Log::info('Re-authentication successful');

                        // Set a secure cookie with improved settings
                        $isSecure = $request->secure() || (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] == 'on');
                        $cookie = cookie(
                            'auth_user_id',
                            $user->id,
                            60, // 60 minutes
                            '/',
                            null,
                            $isSecure,
                            true, // httpOnly
                            false,
                            'Lax' // SameSite
                        );

                        // Continue with the request and add the cookie later
                    } else {
                        Log::error('Re-authentication failed');
                    }
                } else {
                    Log::error('User not found', ['user_id' => $userId]);
                }
            }
        }

        // Get the response
        $response = $next($request);

        // If user is authenticated, add the auth cookie to the response
        if (Auth::check()) {
            $isSecure = $request->secure() || (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] == 'on');
            $cookie = cookie(
                'auth_user_id',
                Auth::id(),
                60, // 60 minutes
                '/',
                null,
                $isSecure,
                true, // httpOnly
                false,
                'Lax' // SameSite
            );
            $response->withCookie($cookie);
        }

        return $response;
    }
}
