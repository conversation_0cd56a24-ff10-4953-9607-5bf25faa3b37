<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\ViewErrorBag;
use Symfony\Component\HttpFoundation\Response;

class SweetAlertMessages
{
    public function handle(Request $request, Closure $next): Response
    {
        // Get the response
        $response = $next($request);

        // If there are validation errors, display them with SweetAlert
        if ($request->session()->has('errors')) {
            $errors = $request->session()->get('errors');

            if ($errors instanceof ViewErrorBag) {
                $errorMessages = [];

                foreach ($errors->all() as $error) {
                    $errorMessages[] = $error;
                }

                // Format errors as HTML list
                $errorHtml = '<ul style="text-align: left; margin-top: 0;">';
                foreach ($errorMessages as $error) {
                    $errorHtml .= '<li>'.$error.'</li>';
                }
                $errorHtml .= '</ul>';

                // Show SweetAlert with all validation errors
                alert()->html(
                    'Validation Error',
                    $errorHtml,
                    'error'
                );
            }
        }

        // Check for success alerts from session
        if ($request->session()->has('alerts')) {
            $alerts = $request->session()->get('alerts');

            if (isset($alerts['success'])) {
                alert()->success('Success', $alerts['success']);
            }

            if (isset($alerts['warning'])) {
                alert()->warning('Warning', $alerts['warning']);
            }

            if (isset($alerts['danger'])) {
                alert()->error('Error', $alerts['danger']);
            }

            if (isset($alerts['info'])) {
                alert()->info('Information', $alerts['info']);
            }
        }

        return $response;
    }
}
