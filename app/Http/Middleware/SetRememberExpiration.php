<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class SetRememberExpiration
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        $response = $next($request);

        if ($request->session()->has('set_remember_expir') && Auth::check()) {
            $cookie_val = $request->cookie(Auth::getRecallerName());
            if ($cookie_val) {
                Session::forget('set_remember_expir');   // We're gonna do it!  so we can forget the seesion variable.

                return $response->cookie($cname, $cookie_val, 86400);  // change the expiration time to 60 days (86400 minutes)
            }
        }

        return $response;
    }
}
