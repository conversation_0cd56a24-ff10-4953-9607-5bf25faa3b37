<?php

namespace App\Http\Controllers;

use App\Models\Discussion;
use App\Models\Group;
use App\Models\Issue;
use App\Models\OnesignalApiInterface;
use App\Models\Post;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\MessageBag;
use Illuminate\Support\Str;

class DiscussionController extends Controller
{
    protected $discussion;

    public function __construct(Discussion $discussion)
    {
        $this->discussion = $discussion;
    }

    /**
     * Display a listing of the resource.
     */
    public function index(): RedirectResponse
    {
        $group = User::get_current_group();

        return (isset($group))
            ? redirect()->route('group.discussions', ['id' => User::get_current_group()->id])
            : redirect()->route('group.index')->withAlerts("You don't belong to any groups.");
    }

    // Create a discussion in the user's default group - just redirect to the group's create discussion page
    public function default_create()
    {
        $group = User::get_current_group();

        return (isset($group))
            ? redirect()->route('group.create_discussion', ['id' => $group->id])
            : redirect()->route('group.index')->withAlerts("You don't belong to any groups.");
    }

    public function forGroup(Request $request, $group_id)
    {
        $user = Auth::User();
        $group = Group::find($group_id);

        // Make sure this group exists, and the user is a member.
        if (! isset($group) || ! $group->members()->find($user->id)) {
            return redirect()->route('home')->withAlerts(['danger' => 'Invalid group.']);
        }

        // Make this group our default for this session:
        User::set_current_group($group);

        $query = $group->discussions();

        // Did they use the filter/sort form?
        $input = $request->all();
        if (! empty($input['from_date'])) {
            $query = $query->where('discussions.created_at', '>=', Carbon::parse($input['from_date']));
        }
        if (! empty($input['to_date'])) {
            $query = $query->where('discussions.created_at', '<=', Carbon::parse($input['to_date']));
        }
        if (! empty($input['search'])) {
            $query = $query->where('discussions.title', 'like', '%'.$input['search'].'%');
        }

        switch ($request->input('sort', 'newest')) {
            case 'newest':
                $query = $query->orderBy('updated_at', 'desc');
                break;
            case 'oldest':
                $query = $query->orderBy('updated_at', 'asc');
                break;
            case 'alpha':
                $query = $query->orderBy('title', 'asc');
                break;
            case 'revalpha':
                $query = $query->orderBy('title', 'desc');
                break;
        }

        $paged_discussions = $query->paginate(15)->appends($request->except('page'));

        return view('v2.discussions.index')->withGroup($group)->withDiscussions($paged_discussions)->withInput($request->all());
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create($group_id)
    {
        $group = Group::find($group_id);
        if (! isset($group) || ! $group->isMember(Auth::User())) {
            return redirect('home')->withAlerts(['danger' => 'Invalid group.']);
        }

        return view('v2.discussions.create')->withGroup($group);
    }

    /**
     * Show the legacy form for creating a new resource.
     */
    public function create_legacy($group_id)
    {
        $group = Group::find($group_id);
        if (! isset($group) || ! $group->isMember(Auth::User())) {
            return redirect('home')->withAlerts(['danger' => 'Invalid group.']);
        }

        return view('discussions.create')->withGroup($group);
    }

    public function wizard_create(): \Illuminate\View\View
    {
        $list = Group::myGroupsSelectArray();
        $list = [0 => 'Please Select...'] + $list;

        return view('wizard.intro')->withUser(Auth::User())->with('group_select', $list);
    }

    /**
     * Store a newly created resource in storage.
     */
    private function do_store(Request $request)
    {
        $user = Auth::User();
        $group = Group::find($request->input('group_id'));
        if (! isset($group) || ! $group->isMember($user)) {
            return redirect('home')->withAlerts(['danger' => 'Invalid group.']);
        }

        $discussion = new Discussion;
        $discussion->fill($request->only('title'));

        $comment = new Post;
        $comment->fill($request->only('comment'));

        $errors = new MessageBag;
        if (! $discussion->isValid()) {
            $errors->merge($discussion->errors);
        }
        if (! $comment->isValid()) {
            $errors->merge($comment->errors);
        }

        if ($errors->count()) {
            return back()->withInput()->withErrors($errors);
        }

        $discussion->creator()->associate($user);
        $comment->author()->associate($user);
        $group->discussions()->save($discussion);
        $discussion->posts()->save($comment);
        $discussion->mark_read($user);

        // Deal with notifications via queue:
        $discussion_id = $discussion->id;
        $comment_id = $comment->id;
        dispatch(function () use ($discussion_id, $comment_id) {

            // First, end email notification to group members that have per_comment notification preference:
            $discussion = Discussion::with('group.active_members', 'creator')->find($discussion_id);
            $comment = Post::find($comment_id);
            $group = $discussion->group;

            if (! $group->is_network) {
                $template_data = [
                    'discussion' => $discussion,
                    'comment' => $comment,
                ];
                $subject = $group->name.': '.$discussion->title;
                $email_per_comment_members = $group->active_members->filter(function ($user) {
                    return $user->pivot->email_pref == 'per_comment';
                });
                foreach ($email_per_comment_members as $member) {
                    Mail::send(['emails.new_discuss', 'emails.new_discuss-text'], $template_data, function ($message) use ($member, $subject) {
                        $message->to($member->email, $member->firstname.' '.$member->lastname);
                        $message->subject($subject);
                    });
                }
            }

            // Finally, send app notification to group members that have a mobile app associated w/ their account:
            $onesignal_ids = $group->getOneSignalIDs();
            if (count($onesignal_ids)) {
                $subject = 'New discussion in '.$group->name;
                $message = $discussion->title;
                $url = URL::route('discussion.show', ['discussion' => $discussion->id]);
                $mobile = new OnesignalApiInterface;
                $mobile->postToUsers($onesignal_ids, $subject, $message, $url);
            }
        })->afterResponse();

        return null;

    }

    public function store(Request $request)
    {
        $action = $this->do_store($request);

        return (isset($action)) ? $action : redirect()->route('discussion.index');
    }

    public function wizard_store(Request $request)
    {
        $action = $this->do_store($request);
        $user = Auth::User();
        $user->finishedWizardSection('intro');
        $user->save();

        return (isset($action)) ? $action : redirect()->route($user->currentWizardRoute());
    }

    /**
     * Display the specified resource.
     */
    public function show(int $id)
    {
        $user = Auth::User();
        $discussion = Discussion::with(['posts.author', 'issue', 'group', 'creator'])->find($id);

        if (! (isset($discussion) && $discussion->canView($user))) {
            abort(404);
        }
        if (isset($discussion->issue)) {
            return redirect()->route('issue.show', ['issue' => $discussion->issue->id]);
        }

        $discussion->mark_read($user);

        return view('v2.discussions.show')->withDiscussion($discussion);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(int $id)
    {
        $discussion = Discussion::find($id);
        if (! $discussion->canEdit(Auth::User())) {
            return redirect()->route('discussion.show', ['discussion' => $id]);
        }

        return view('v2.discussions.edit')->withDiscussion($discussion)->withGroup($discussion->group()->first())->with('group_select', Group::myGroupsSelectArray());
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, int $id)
    {
        $discussion = Discussion::find($id);

        // If we can't find the discussion:
        if (! isset($discussion)) {
            return 'Discussion not found';
        }

        // If the User is not the discussion owner:
        if (! $discussion->canEdit(Auth::User())) {
            return redirect()->route('discussion.show', ['discussion' => $id])->withError("You don't have permission to edit this discussion");
        }

        $discussion->fill($request->only('title', 'status'));

        // If the form contains errors:
        if (! $discussion->isValid()) {
            return back()->withInput()->withErrors($discussion->errors);
        }

        // If we made it this far, save it!
        $discussion->save();

        return redirect()->route('discussion.show', ['discussion' => $id])->withSuccess('Discussion Updated');
    }

    public function new_issue($discussion_id)
    {
        $discussion = Discussion::findOrFail($discussion_id);

        // If the User is not the discussion owner:
        if (! $discussion->canEdit(Auth::User())) {
            return redirect()->route('discussion.show', ['discussion' => $discussion_id])->withError("You don't have permission to edit this discussion");
        }

        $group = $discussion->group()->first();
        if (! $group->canCreateRequest(Auth::User())) {
            return redirect()->route('discussion.edit', ['discussion' => $discussion_id])
                ->withAlerts(['danger' => 'Unable to create a request for this group.']);
        }

        $issue = $discussion->issue;

        if (isset($issue)) {
            return redirect()->route('issue.edit', ['issue' => $issue->id]);
        } else {
            return view(
                'issues.create',
                [
                    'discussion_id' => $discussion->id,
                    'group' => $group,
                    'deadline_choices' => Issue::deadline_choices($group),
                ]
            );
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(int $id)
    {
        $discussion = Discussion::find($id);

        // If we can't find the discussion:
        if (! isset($discussion)) {
            abort(404);
        }

        // If the User is not the discussion creator or an admin:
        if (! $discussion->canDelete(Auth::User())) {
            return back()->withAlerts(['danger' => 'Invalid action.']);
        }

        foreach ($discussion->posts as $post) {
            $post->users_read()->detach();
            $post->delete();
        }
        $discussion->users_read()->detach();
        $discussion->delete();

        return redirect()->route('discussion.index');
    }

    public function post(Request $request, $discussion_id)
    {
        $me = Auth::User();
        $my_id = $me->id;
        $discussion = Discussion::findOrFail($discussion_id);

        if (! $discussion->canComment($me)) {
            return back()->withAlerts(['danger' => 'Invalid action.']);
        }

        $comment = $request->input('comment');
        $post = $discussion->post_comment($me, $comment);

        $post_id = $post->id;
        dispatch(function () use ($discussion_id, $post_id, $my_id) {
            // Send email notification to group members that have per_comment notification preference:
            $post = Post::find($post_id);
            $me = User::find($my_id);
            $discussion = Discussion::with('group.active_members', 'creator.mobile_apps_notify')->find($discussion_id);

            $group = $discussion->group;
            if (! $group->is_network) {
                $template_data = [
                    'discussion' => $discussion,
                    'post' => $post,
                ];
                $subject = 'RE: '.$group->name.': '.$discussion->title;
                $email_per_comment_members = $discussion->group->active_members->filter(function ($user) {
                    return $user->pivot->email_pref == 'per_comment';
                });
                foreach ($email_per_comment_members as $member) {
                    Mail::send(['emails.comment', 'emails.comment-text'], $template_data, function ($message) use ($member, $subject) {
                        $message->to($member->email, $member->firstname.' '.$member->lastname);
                        $message->subject($subject);
                    });
                }
            }

            // Finally, send app notification to group members that have a mobile app associated w/ their account:
            if (count($discussion->creator->mobile_apps_notify)) {
                $onesignal_ids = [];
                foreach ($discussion->creator->mobile_apps_notify as $mobile_app) {
                    $onesignal_ids[] = $mobile_app->one_signal_id;
                }
                $subject = "New comment on '".$discussion->title."'";
                $message = $me->firstname.' '.$me->lastname.' says: '.Str::limit($post->niceComment, $limit = 150, '...');
                $url = URL::route('discussion.show', ['discussion' => $discussion->id]);
                $mobile = new OnesignalApiInterface;
                $mobile->postToUsers($onesignal_ids, $subject, $message, $url);
            }
        })->afterResponse();

        return back();
    }
}
