<?php

namespace App\Http\Controllers;

use App\Models\Post;
use Illuminate\Support\Facades\Auth;

class PostController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(): Response
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(): Response
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(): Response
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(int $id): Response
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(int $id): Response
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(int $id): Response
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(int $id): Response
    {
        // We should delete the post and the post_user entries for that post.
        // FOR LATER: if the discussion no longer has any posts, we should delete the discussion as well

        $post = Post::find($id);

        // If we can't find the discussion:
        if (! isset($post)) {
            return 'Post not found';
        }

        // If the User is not the discussion owner:
        if (! $post->canDelete(Auth::User())) {
            return back()->withAlerts(['danger' => 'Invalid action.']);
        }

        $post->users_read()->detach();
        $post->delete();

        return back()->withAlerts(['success' => 'Comment deleted.']);
    }
}
