<?php

namespace App\Http\Controllers;

use App\Models\Group;
use App\Models\GroupInvite;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Auth;

class InvitationController extends Controller
{
    public function redeem_token($token): RedirectResponse
    {
        // We have no idea if the person clicking the link from the email is the one who is logged in.
        // Eventually, maybe we can bring up a confirmation page saying "This is who is logged in.  Are you this person?"
        //  That way, we don't annoy them by unnecessarily logging them out.
        // For the meantime, log the existing session out first.
        Auth::logout();
        $group_invite = GroupInvite::where('token', $token)->first();
        if (! isset($group_invite)) {
            return redirect()->route('auth.login')->withAlerts(['warning' => 'Invalid or expired invite token']);
        }

        session()->put([
            'group_invite_token' => $token,
            'group_invite_email' => $group_invite->email,
        ]);

        $group_id = $group_invite->group_id;
        $group = Group::find($group_id);

        if (isset($group) && $group->isPublicViewable()) {
            return redirect()->route('group.public_show_id', ['id' => $group_id]);
        } else {
            return redirect()->route('auth.login');
        }
    }

    public function decline_invite($group_id): RedirectResponse
    {
        GroupInvite::where('group_id', $group_id)->where('user_id', Auth::User()->id)->delete();

        return redirect('mygroups')->withAlerts(['info' => 'Invitation declined.']);
    }
}
