<?php

namespace App\Http\Controllers\V2;

use App\Http\Controllers\Controller;
use App\Models\UserItem;
use App\Models\UserSkill;
use App\Services\AmazonProductService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class ItemsController extends Controller
{
    protected $amazonService;

    public function __construct(AmazonProductService $amazonService)
    {
        $this->amazonService = $amazonService;
    }

    /**
     * Display the user's items and skills
     */
    public function index()
    {
        $user = Auth::user();
        
        $items = UserItem::where('user_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->get();
            
        $skills = UserSkill::where('user_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->get();

        return view('v2.items.index', compact('items', 'skills'));
    }

    /**
     * Show the form for creating a new item
     */
    public function createItem()
    {
        return view('v2.items.create');
    }

    /**
     * Show the form for creating a new skill
     */
    public function createSkill()
    {
        return view('v2.items.skills');
    }

    /**
     * Store a new item
     */
    public function storeItem(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'amazon_asin' => 'nullable|string|max:20',
            'amazon_url' => 'nullable|url',
            'image_url' => 'nullable|url',
            'price' => 'nullable|numeric|min:0',
            'currency' => 'nullable|string|size:3',
            'condition' => 'required|in:new,like_new,good,fair,poor',
            'notes' => 'nullable|string'
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $item = UserItem::create([
            'user_id' => Auth::id(),
            'title' => $request->title,
            'description' => $request->description,
            'amazon_asin' => $request->amazon_asin,
            'amazon_url' => $request->amazon_url,
            'image_url' => $request->image_url,
            'price' => $request->price,
            'currency' => $request->currency ?? 'USD',
            'condition' => $request->condition,
            'notes' => $request->notes,
            'is_available' => true
        ]);

        return redirect()->route('v2.items.index')
            ->with('success', 'Item added successfully!');
    }

    /**
     * Store a new skill
     */
    public function storeSkill(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'category' => 'required|in:technical,creative,manual,professional,educational,health,domestic,other',
            'experience_level' => 'required|in:beginner,intermediate,advanced,expert',
            'hourly_rate' => 'nullable|numeric|min:0',
            'currency' => 'nullable|string|size:3',
            'is_free' => 'boolean',
            'requirements' => 'nullable|string'
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $skill = UserSkill::create([
            'user_id' => Auth::id(),
            'title' => $request->title,
            'description' => $request->description,
            'category' => $request->category,
            'experience_level' => $request->experience_level,
            'hourly_rate' => $request->is_free ? null : $request->hourly_rate,
            'currency' => $request->currency ?? 'USD',
            'is_free' => $request->boolean('is_free', true),
            'requirements' => $request->requirements,
            'is_available' => true
        ]);

        return redirect()->route('v2.items.index')
            ->with('success', 'Skill added successfully!');
    }

    /**
     * Search Amazon products via AJAX
     */
    public function searchAmazonProducts(Request $request)
    {
        $query = $request->get('q', '');
        
        if (empty($query)) {
            return response()->json(['results' => []]);
        }

        $products = $this->amazonService->searchProducts($query, 'All', 10);
        
        return response()->json(['results' => $products]);
    }

    /**
     * Toggle item availability
     */
    public function toggleItemAvailability(UserItem $item)
    {
        if ($item->user_id !== Auth::id()) {
            abort(403);
        }

        $item->update(['is_available' => !$item->is_available]);
        
        $status = $item->is_available ? 'available' : 'unavailable';
        return back()->with('success', "Item marked as {$status}!");
    }

    /**
     * Toggle skill availability
     */
    public function toggleSkillAvailability(UserSkill $skill)
    {
        if ($skill->user_id !== Auth::id()) {
            abort(403);
        }

        $skill->update(['is_available' => !$skill->is_available]);
        
        $status = $skill->is_available ? 'available' : 'unavailable';
        return back()->with('success', "Skill marked as {$status}!");
    }

    /**
     * Delete an item
     */
    public function deleteItem(UserItem $item)
    {
        if ($item->user_id !== Auth::id()) {
            abort(403);
        }

        $item->delete();
        return back()->with('success', 'Item deleted successfully!');
    }

    /**
     * Delete a skill
     */
    public function deleteSkill(UserSkill $skill)
    {
        if ($skill->user_id !== Auth::id()) {
            abort(403);
        }

        $skill->delete();
        return back()->with('success', 'Skill deleted successfully!');
    }
}
