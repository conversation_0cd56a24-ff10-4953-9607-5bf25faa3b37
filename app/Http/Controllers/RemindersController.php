<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\Password;
use Illuminate\Support\Facades\View;

class RemindersController extends Controller
{
    /**
     * Display the password reminder view.
     */
    public function getRemind(): \Illuminate\View\View
    {
        return view('password.remind');
    }

    /**
     * Handle a POST request to remind a user of their password.
     */
    public function postRemind(Request $request)
    {
        $request->validate(['email' => 'required|email']);

        $status = Password::sendResetLink($request->only('email'));

        return $status === Password::RESET_LINK_SENT
            ? back()->with(['status' => __($status)])
            : back()->withErrors(['email' => __($status)]);
    }

    /**
     * Display the password reset view for the given token.
     */
    public function getReset(?string $token = null): \Illuminate\View\View
    {
        if (is_null($token)) {
            abort(404);
        }

        return view('password.reset')->with('token', $token);
    }

    /**
     * Handle a POST request to reset a user's password.
     */
    public function postReset(Request $request)
    {
        $request->validate([
            'token' => 'required',
            'email' => 'required|email',
            'password' => 'required|min:8|confirmed',
        ]);

        $status = Password::reset(
            $request->only('email', 'password', 'password_confirmation', 'token'),
            function ($user, $password) {
                $user->password = $password;

                $user->save();
            }
        );

        return $status === Password::PASSWORD_RESET
            ? redirect('/')
            : back()->with('error', Lang::get($status));
    }

    public function resetLink($user_id)
    {
        $user = User::find($user_id);
        if (! isset($user)) {
            abort(404);
        }

        $row = DB::table('password_resets')->where('email', $user->email)->first();
        if (empty($row)) {
            return back()->withAlerts(['danger' => "Email address hasn't requested a password reset"]);
        }

        return '<pre>'
                .'To: '.$user->email."\n\n"
                .view('emails.auth.reminder-text')->withToken($row->token).'</pre>';
    }
}
