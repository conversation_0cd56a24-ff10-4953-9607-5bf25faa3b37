<?php

namespace App\Http\Controllers;

use App\Models\StripeMetadata;
use App\Models\Subscription;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class FinancesController extends Controller
{
    private $stripe;

    public function __construct()
    {
        $stripeSecretKey = env('STRIPE_SECRET_KEY');

        // Check if the Stripe key is set before initializing the client
        if ($stripeSecretKey) {
            $this->stripe = new \Stripe\StripeClient($stripeSecretKey);
        } else {
            // Log the missing key
            \Log::error('Stripe secret key is not set in environment variables');
            // Initialize with empty config to avoid errors when listing routes
            $this->stripe = null;
        }
    }

    public function edit(): \Illuminate\View\View
    {
        $address = Auth::User()->address;
        $country_code = $address ? $address->country : null;
        $data = $this->donation_panel_ui_control($country_code);

        return view('finances.edit', $data);
    }

    public function wizard_edit(): \Illuminate\View\View
    {

        $address = Auth::User()->address;
        $country_code = $address ? $address->country : null;
        $data = $this->donation_panel_ui_control($country_code);

        return view('wizard.donation', $data);
    }

    private function donation_panel_ui_control($country_code)
    {
        $data['panel_active_us_and_others'] = '';
        $data['panel_active_uk'] = '';
        $data['panel_active_sa'] = '';

        $data['panel_active_us_and_others_pill'] = 'discution';
        $data['panel_active_uk_pill'] = 'discution';
        $data['panel_active_sa_pill'] = 'discution';

        switch ($country_code) {
            case 'ZA':
            case 'za':
                $data['panel_active_sa'] = 'active';
                $data['panel_active_sa_pill'] = 'btn-success2';
                break;

            case 'GB':
            case 'gb':
                $data['panel_active_uk'] = 'active';
                $data['panel_active_uk_pill'] = 'btn-success2';
                break;

            case 'US':
            case 'us':
            default:
                $data['panel_active_us_and_others'] = 'active';
                $data['panel_active_us_and_others_pill'] = 'btn-success2';
                break;

        }

        return $data;
    }

    public function uk_paypal_common_change(): \Illuminate\View\View
    {
        return view('finances.donation_uk_common_change_paypal');
    }

    public function uk_paypal_group(): \Illuminate\View\View
    {
        return view('finances.donation_uk_group_paypal');
    }

    private function do_donate(Request $request, $onsuccess = null)
    {
        $rules = [
            'donation_type' => 'required|in:charge,subscription',
            'subscription_period' => 'required_if:donation_type,subscription|in:weekly,monthly',
            'subscription_end_date' => 'date',
            'donation_date' => 'date',
        ];

        $validator = Validator::make($request->all(), $rules);
        if ($validator->fails()) {
            return back()->withInput()->withErrors($validator->messages());
        }

        $user = Auth::User();

        $token = $request->input('stripeToken');
        if (isset($token)) {

            $group = $user->default_group()->first();

            try {
                $customer = Subscription::get_or_create_customer($user, $token);
                $amount_commonchange = $request->input('amount_commonchange');
                $amount_group = $request->input('amount_group');

                if ($request->input('donation_type') == 'subscription') {
                    $period = $request->input('subscription_period');
                    if ($amount_commonchange > 0) {
                        Subscription::set_subscription($user, 'dollar_'.$period.'_cc', $amount_commonchange, StripeMetadata::create()->setCCOps()->toArray());
                    } else {
                        Subscription::cancel_subscription($user, 'dollar_'.$period.'_cc');
                    }

                    if ($amount_group > 0) {
                        Subscription::set_subscription($user, 'dollar_'.$period.'_group', $amount_group, StripeMetadata::create()->setGroup($group)->toArray());
                    } else {
                        Subscription::cancel_subscription($user, 'dollar_'.$period.'_group');
                    }
                } else {
                    // Create the charge on Stripe's servers - this will charge the user's card
                    if ($amount_commonchange > 0) {
                        $charge1 = $this->stripe->charges->create([
                            'amount' => $amount_commonchange * 100, // amount in cents
                            'currency' => 'usd',
                            'customer' => $customer->id,
                            'metadata' => StripeMetadata::create()->setUser($user)->setCCOps()->toArray(),
                        ]
                        );
                    }
                    if ($amount_group > 0) {
                        $charge2 = $this->stripe->charges->create([
                            'amount' => $amount_group * 100, // amount in cents
                            'currency' => 'usd',
                            'customer' => $customer->id,
                            'metadata' => StripeMetadata::create()->setUser($user)->setGroup($group)->toArray(),
                        ]
                        );
                    }
                }
            } catch (\Stripe\Exception\CardException $e) {
                return back()->withInput()->withAlerts(['danger' => 'Your card has been declined: '.$e->getMessage()]);
            }

            $action = (isset($onsuccess)) ? $onsuccess($user) : back();

            return $action->withAlerts(['success' => 'Your '.$request->input('donation_type').' was successful!']);

        } else {
            return back()->withInput()->withAlerts(['info' => 'The payment portal will open shortly'])->withDonation(true);
        }

    }

    public function donate(Request $request)
    {
        return $this->do_donate($request);
    }

    public function wizard_donate(Request $request)
    {
        return $this->do_donate($request, function (User $user) {
            $user->finishedWizardSection('donation');
            $user->save();

            return redirect()->route($user->currentWizardRoute());
        });
    }
}
