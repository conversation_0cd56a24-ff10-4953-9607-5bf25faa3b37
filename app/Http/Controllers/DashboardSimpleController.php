<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class DashboardSimpleController extends Controller
{
    /**
     * Constructor to apply middleware
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Display a simple dashboard.
     *
     * @param  int  $userId
     * @return \Illuminate\View\View
     */
    public function show($userId)
    {
        // Debug information
        \Log::info('Simple Dashboard show method called with user ID: ' . $userId);
        \Log::info('Current authenticated user ID: ' . (Auth::check() ? Auth::id() : 'Not authenticated'));

        // Ensure the user is authenticated (this is a backup check, as we already have the auth middleware)
        if (!Auth::check()) {
            \Log::error('Unauthenticated user attempting to access simple dashboard');
            return redirect()->route('auth.login');
        }

        return view('dashboard.simple');
    }
}
