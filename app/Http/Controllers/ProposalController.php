<?php

namespace App\Http\Controllers;

use App\Models\Proposal;
use Illuminate\Support\Facades\Auth;

class ProposalController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(): Response
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(): Response
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(): Response
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(int $id): Response
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(int $id): Response
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(int $id): Response
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(int $id): Response
    {
        //
    }

    public function vote($id, $vote)
    {
        $proposal = Proposal::with('issue.discussion.group.members')->findOrFail($id);

        if ($proposal->voting_closed) {
            return back()->withAlerts(['danger' => 'Voting has closed.']);
        }
        // given changes to allowing proposal creation, should we do this check?:
        if ($proposal->amount > $proposal->issue->discussion->group->current_funds) {
            return back()->withAlerts(['danger' => 'Group has insufficient funds: '.$proposal->issue->discussion->group->current_funds]);
        }

        $user = Auth::User();

        $member = $proposal->issue->discussion->group->members->find($user);

        if (! isset($member)) {
            return back()->withAlerts(['danger' => "You don't have permission to vote."]);
        }
        if ($member->pivot->is_absent) {
            return back()->withAlerts(['danger' => 'Your group status is set to Friend.  You must change this before you can vote.']);
        }

        $proposal->vote($user, $vote);

        return redirect()->route('issue.show', ['issue' => $id])->withAlerts(['success' => 'Vote cast!']);
    }

    public function payment_info($id) {}
}
