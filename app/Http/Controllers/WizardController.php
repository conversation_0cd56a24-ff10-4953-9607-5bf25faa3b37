<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class WizardController extends Controller
{
    /**
     * Show the account setup wizard page.
     */
    public function showAccountSetup(Request $request)
    {
        $user = Auth::user();

        // Mark this section as the current wizard bookmark
        if (! $user->wizard_bookmark) {
            $user->wizard_bookmark = 'account';
            $user->save();
        }

        return view('wizard.account', ['user' => $user]);
    }
}
