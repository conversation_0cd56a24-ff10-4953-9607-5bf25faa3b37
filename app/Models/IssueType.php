<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class IssueType extends Model
{
    use SoftDeletes;

    public $timestamps = false;

    protected $table = 'issue_types';

    public function issues(): HasMany
    {
        return $this->hasMany(Issue::class, 'issue_type_id');
    }

    public static function select_options($unselected_text = null, $column = 'description')
    {
        $list = self::all()->pluck($column, 'id')->toArray();
        if (isset($unselected_text)) {
            $list = array_merge(['0' => $unselected_text], $list);
        }

        return $list;
    }
}
