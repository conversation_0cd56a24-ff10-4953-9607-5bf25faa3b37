<?php

namespace App\Models;

class Wizard
{
    private static $wizard_order = [
        'account',
        'group',
        'donation',
        'intro',
    ];

    private static $wizard_classes = [];

    public static function setupClasses($current_page)
    {
        foreach (self::$wizard_order as $page) {
            self::$wizard_classes[$page] = ($page == $current_page);
        }
    }

    /*
        public static function setupClasses ($current_page) {
            $prior = true;
            foreach (self::$wizard_order as $page) {
                self::$wizard_classes[$page] = ($prior) ? true : false;
                if ($page == $current_page) $prior = false;
            }
        }
    */

    public static function printIfActive($page, $class)
    {
        return (self::$wizard_classes[$page]) ? $class : '';
    }
}
