<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class MobileAppAccount extends Model
{
    public function user()
    {
        $this->belongsTo(User::class);
    }

    public function getDataAttribute($json_string)
    {
        return json_decode($json_string);
    }

    public function setDataAttribute($data)
    {
        $this->attributes['data'] = json_encode($data);
    }
}
