<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class IssueCategory extends Model
{
    use SoftDeletes;

    protected $table = 'issue_categories';

    public $timestamps = false;

    public function issues(): HasMany
    {
        return $this->hasMany(Issue::class, 'issue_category_id');
    }

    public static function select_options($unselected_text = null, $column = 'description')
    {
        $list = self::all()->pluck($column, 'id')->toArray();
        if (isset($unselected_text)) {
            $list = array_merge(['0' => $unselected_text], $list);
        }

        return $list;
    }
}
