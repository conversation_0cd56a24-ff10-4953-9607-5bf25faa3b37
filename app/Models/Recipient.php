<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Validator;

class Recipient extends Model
{
    use SoftDeletes;

    public function address()
    {
        return $this->morphOne(Address::class, 'addressable');
    }

    public function issues(): HasMany
    {
        return $this->hasMany(Issue::class);
    }

    protected $with = ['address'];  // automagically loads the address object when we load a recipient.

    protected $fillable = ['firstname', 'lastname'];

    public function setPasswordAttribute($cleartext)
    {
        $this->attributes['password'] = Hash::make($cleartext);
    }

    // /////////// Validation //////////////

    public $errors;

    public static $rules = [
        'firstname' => 'required|regex:/^[\pL\pN \'\-.]+$/',
        'lastname' => 'required|regex:/^[\pL\pN \'\-.]+$/',
        // 'email'    => 'email|unique:recipients',  //this is causing problems for updates
        'email' => 'email',
        'phone' => 'min:10',
    ];

    public function isValid()
    {
        $validation = Validator::make($this->attributes, static::$rules);

        if ($validation->passes()) {
            return true;
        }

        $this->errors = $validation->messages();

        return false;
    }
}
