<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\Pivot;

class GroupUserPivot extends Pivot
{
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function group(): BelongsTo
    {
        return $this->belongsTo(Group::class);
    }

    // A custom attribute that returns the user's group_user.email_notification email pref for that group,
    // unless it's set to 'use_default', in which case, it returns that user's users.default_email_notification.
    public function getEmailPrefAttribute()
    {
        return ($this->email_notification == 'use_default') ? $this->user->default_email_notification : $this->email_notification;
    }
}
