<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class Issue extends Model
{
    use SoftDeletes;

    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'creator_id');
    }

    public function discussion(): BelongsTo
    {
        return $this->belongsTo(Discussion::class)->withTrashed();
    }

    public function recipient(): BelongsTo
    {
        return $this->belongsTo(Recipient::class);
    }

    public function issue_category(): BelongsTo
    {
        return $this->belongsTo(IssueCategory::class, 'issue_category_id');
    }

    public function issue_type(): BelongsTo
    {
        return $this->belongsTo(IssueType::class, 'issue_type_id');
    }

    public function users_read(): BelongsToMany
    {
        return $this->belongsToMany(User::class);
    }

    public function i_read(): BelongsToMany
    {
        return $this->belongsToMany(User::class)->where('users.id', '=', Auth::User()->id);
    }

    public function proposal(): HasOne
    {
        return $this->hasOne(Proposal::class)->withTrashed();
    }

    // public function issueable() { return $this->morphTo(); }
    // public function proposals()      { return $this->hasMany(Proposal::class); }

    public function scopeUnread($query)
    {
        return $query->doesntHave('i_read');
    }

    public function getIsUnreadAttribute()
    {
        return $this->i_read()->count() == 0;
    }

    protected $touches = ['discussion'];  // Update the timestamp for the parent of this post.

    protected $with = ['creator', 'proposal', 'recipient', 'issue_category', 'issue_type'];

    protected $fillable = ['title', 'description', 'status', 'issue_category_id', 'issue_type_id'];

    public static $enum_status = [
        'open' => 'Open',
        'resolved' => 'Resolved',
        'closed' => 'Closed',
    ];

    public static function deadline_choices($group, $editing = false)
    {
        $choices = [];
        if ($editing) {
            $choices[] = 1;
        }
        if ($group->country == 'GB') {
            $choices[] = 2;
        }

        $choices = array_merge($choices, [3, 5, 7, 10, 14]);

        $reducer = function ($result, $choice) {
            $key = strval($choice);
            $result[strval($choice)] = "$choice ".(($choice == 1) ? 'day' : 'days');

            return $result;
        };

        return array_reduce($choices, $reducer, ['' => 'Please Select']);
    }

    public $errors;

    public static $rules = [
        'title' => 'required|min:1',
        'description' => 'required|min:1',
        'issue_category_id' => 'required|exists:issue_categories,id',
        'issue_type_id' => 'required|exists:issue_types,id',

    ];

    public static function validateInput($input)
    {
        $errors = new MessageBag;
        $validation = Validator::make($input, static::$rules);
        if ($validation->fails()) {
            $errors->merge($validation->messages());
        }

        return $errors;
    }

    public function isValid($input)
    {
        $validation = Validator::make($input, static::$rules);

        if ($validation->passes()) {
            return true;
        }

        $this->errors = $validation->messages();

        return false;
    }

    public function mark_read(User $user)
    {
        if (! $this->users_read()->find($user->id)) {
            $this->users_read()->attach($user);
            $this->discussion()->first()->mark_read($user);
        }
    }

    public function close()
    {
        $this->status = 'closed';
    }

    public function canEdit(User $user)
    {
        return ($this->creator_id == $user->id) || $user->is_admin;
    }

    public function canView(User $user)
    {
        // Current user is a member of the group
        return (! $this->trashed() && $this->discussion->group->members->contains($user->id)) || $user->is_admin;
    }

    public function niceDescription()
    {
        // $result = preg_replace('/\r?\n(\r?\n)+/','<br /><br />', e($this->description));
        // return preg_replace('/\r?\n/','<br />',$result);
        return \Illuminate\Mail\Markdown::parse(strip_tags($this->description, '<br><br />'));
    }
}
