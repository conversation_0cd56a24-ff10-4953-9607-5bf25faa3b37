<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class Discussion extends Model
{
    use SoftDeletes;

    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'creator_id');
    }

    public function posts(): MorphMany
    {
        return $this->morphMany(Post::class, 'postable');
    }

    public function my_unread_posts(): MorphMany
    {
        return $this->morphMany(Post::class, 'postable')->unread();
    }

    public function recent_posts(): MorphMany
    {
        return $this->morphMany(Post::class, 'postable')->recent();
    }

    public function users_read(): BelongsToMany
    {
        return $this->belongsToMany(User::class);
    }

    public function i_read(): BelongsToMany
    {
        return $this->belongsToMany(User::class)->where('users.id', '=', Auth::User()->id);
    }

    public function issue(): HasOne
    {
        return $this->hasOne(Issue::class)->withTrashed();
    }

    public function group(): BelongsTo
    {
        return $this->belongsTo(Group::class);
    }

    public function scopeNonIssue($query)
    {
        return $query->doesntHave('issue');
    }

    public function scopeUnread($query)
    {
        return $query->doesntHave('i_read');
    }

    public function scopeHasUnreadPosts($query)
    {
        return $query->has('my_unread_posts');
    }

    public function scopeHasRecentPosts($query)
    {
        return $query->has('recent_posts');
    }

    public function getIsUnreadAttribute()
    {
        return $this->i_read()->count() == 0;
    }

    public function getHasNewPostsAttribute()
    {
        return $this->my_unread_posts()->count() > 0;
    }

    protected $with = ['creator'];

    protected $fillable = ['title', 'deadline', 'status'];

    public static $status_enum = [
        'open' => 'Open',
        'closed' => 'Closed',
    ];

    public $errors;

    public static $rules = [
        'title' => 'required|min:1',
        'deadline' => 'date|after:today',
    ];

    public function isValid()
    {
        $validation = Validator::make($this->attributes, static::$rules);

        if ($validation->passes()) {
            return true;
        }

        $this->errors = $validation->messages();

        return false;
    }

    public function hasIssue()
    {
        $issue = $this->issue;

        return isset($issue);
    }

    public function canDelete($user)
    {
        return ($this->creator_id == $user->id) || $user->is_admin;
    }

    public function canEdit(User $user)
    {
        return $this->creator_id == $user->id || $user->is_admin;
    }

    public function canView(User $user)
    {
        // Current user is a member of the group
        return $this->group->members->contains($user->id) || $user->is_admin;
    }

    public function canComment(User $user)
    {
        // Current user is a member of the group
        return $this->group->members->contains($user->id) || $user->is_admin;
    }

    public function setDeadlineAttribute($value)
    {
        $this->attributes['deadline'] = empty($value) ? null : Carbon::parse($value)->toDateString();
    }

    public function getDeadlineAttribute($value)
    {
        return isset($value) ? Carbon::parse($value) : $value;
    }

    public function getCreatedLocalAttribute()
    {
        $user_tz = User::get_local_tz();

        return (isset($user_tz)) ? $this->created_at->setTimezone($user_tz) : $this->created_at;
    }

    public function getUpdatedLocalAttribute()
    {
        $user_tz = User::get_local_tz();

        return (isset($user_tz)) ? $this->updated_at->setTimezone($user_tz) : $this->updated_at;
    }

    public function mark_read(User $user)
    {
        $this->load('users_read', 'posts.users_read');
        if (! $this->users_read->contains($user)) {
            $this->users_read()->attach($user);
        }
        foreach ($this->posts as $post) {
            $post->mark_read($user);
        }
    }

    public function newer_than($date) {}

    public function post_comment(User $user, $comment)
    {
        $post = new Post;
        $post->comment = $comment;
        $post->author()->associate($user);
        $this->posts()->save($post);
        $post->mark_read($user);

        return $post;
    }
}
