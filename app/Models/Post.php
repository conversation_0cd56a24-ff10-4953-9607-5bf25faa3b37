<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class Post extends Model
{
    public function postable(): MorphTo
    {
        return $this->morphTo();
    }

    public function author(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function users_read(): BelongsToMany
    {
        return $this->belongsToMany(User::class);
    }

    public function i_read(): BelongsToMany
    {
        return $this->belongsToMany(User::class)->where('users.id', '=', Auth::User()->id);
    }

    public function scopeUnread($query)
    {
        return $query->doesntHave('i_read');
    }

    public function scopeRecent($query)
    {
        $today = date('Y-m-d H:00:00');
        $yesterday = date('Y-m-d H:i:s', strtotime("$today -1 day"));

        return $query->where('updated_at', '>=', $yesterday);
    }

    protected $with = ['author'];

    protected $touches = ['postable'];  // Update the timestamp for the parent of this post.

    protected $fillable = ['comment'];

    public $errors;

    public static $rules = ['comment' => 'required|min:1'];

    public function isValid()
    {
        $validation = Validator::make($this->attributes, static::$rules);

        if ($validation->passes()) {
            return true;
        }

        $this->errors = $validation->messages();

        return false;
    }

    public function getCreatedLocalAttribute()
    {
        $user_tz = User::get_local_tz();

        return (isset($user_tz)) ? $this->created_at->setTimezone($user_tz) : $this->created_at;
    }

    public function mark_read(User $user)
    {
        if (! isset($this->users_read)) {
            $this->load('users_read');
        }  // it kills me that I have to do this...
        if (! ($this->users_read->contains($user))) {
            $this->users_read()->attach($user);
        }
    }

    // only works if we've eager loaded users_read...
    public function has_read(User $user)
    {
        return $this->users_read->contains($user);
    }

    public function canDelete($user)
    {
        return ($this->user_id == $user->id) || $user->is_admin;
    }

    public function niceComment()
    {
        // $result = preg_replace('/\r?\n(\r?\n)+/','<br /><br />', e($this->comment));
        // return preg_replace('/\r?\n/','<br />',$result);
        return \Illuminate\Mail\Markdown::parse(strip_tags($this->comment, '<br><br />'));
    }
}
