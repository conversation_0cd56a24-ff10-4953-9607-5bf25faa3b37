<?php

namespace App\Models;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\ClientException;
use Illuminate\Support\Facades\Log;

class OnesignalApiInterface
{
    protected $rest_api_key;

    protected $onesignal_app_id;

    protected $auth_headers;

    protected $client;

    public function __construct()
    {
        $this->rest_api_key = $_ENV['ONESIGNAL_API_KEY'];
        $this->onesignal_app_id = $_ENV['ONESIGNAL_APP_ID'];
        $this->auth_headers = [
            'Authorization' => 'Basic '.$this->rest_api_key,
        ];
        $this->client = new Client;
    }

    public function getUsers()
    {
        $url = 'https://onesignal.com/api/v1/players?app_id='.$this->onesignal_app_id.'&limit=100';
        $data = [
            'headers' => $this->auth_headers,
        ];
        $response = $this->client->get($url, $data);

        return json_decode($response->getBody());
    }

    public function postToUsers($user_ids, $subject, $message, $return_url = null)
    {
        $url = 'https://onesignal.com/api/v1/notifications';
        $headings = ['en' => $subject];
        $contents = ['en' => $message];

        $post_body = [
            'app_id' => $this->onesignal_app_id,
            'include_player_ids' => $user_ids,
            'headings' => $headings,
            // 'subtitle'           => '',
            'contents' => $contents,
            'content_available' => true,
        ];
        if (isset($return_url)) {
            $post_body['data'] = ['targetUrl' => $return_url];
        }

        $data = [
            'headers' => $this->auth_headers,
            'json' => $post_body,
            // 'http_errors' => false,
        ];

        Log::info('OneSignal API post:', $post_body);

        $response = null;
        try {
            $response = $this->client->post($url, $data);
        } catch (ClientException $e) {
            Log::error('OneSignal API error', $e->getResponse());

            // echo"\nRequest:\n";
            // print_r($e->getRequest());
            // echo"\nResponse:\n";
            // print_r($e->getResponse());
            return false;
        }

        return json_decode($response->getBody());
    }
}
