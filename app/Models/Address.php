<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Support\Facades\Validator;

class Address extends Model
{
    protected $table = 'addresses';

    public function addressable(): MorphTo
    {
        return $this->morphTo();
    }

    protected $fillable = ['addr1', 'addr2', 'city', 'state', 'postcode', 'country'];

    protected $hidden = ['id', 'addressable_type', 'addressable_id', 'type', 'created_at', 'updated_at', 'addr3', 'addr4'];

    public $errors;

    public static $rules = [
        'addr1' => 'required',
        'city' => 'required',
        'state' => 'required',
        'postcode' => 'required',
        'country' => 'required',
    ];

    public function isValid()
    {
        $validation = Validator::make($this->attributes, static::$rules);

        if ($validation->passes()) {
            return true;
        }

        $this->errors = $validation->messages();

        return false;
    }
}
