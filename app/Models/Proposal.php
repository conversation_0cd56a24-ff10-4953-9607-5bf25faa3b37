<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class Proposal extends Model
{
    use SoftDeletes;

    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'creator_id');
    }

    public function issue(): BelongsTo
    {
        return $this->belongsTo(Issue::class)->withTrashed();
    }

    public function pay_info(): BelongsTo
    {
        return $this->belongsTo(PayInfo::class, 'payment_info_id');
    }

    public function users_voted(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'proposal_votes')->withTimestamps()->withPivot('vote');
    }

    public function scopeIsOpenForVoting($query)
    {
        return $query->where('voting_closed', '=', '0');
    }

    protected $fillable = ['amount', 'deadline', 'extend_deadline'];

    protected $attributes = ['voting_closed' => false];

    public $errors;

    public $validation_rules = [
        'amount' => 'required|numeric|min:1',
        'deadline' => 'required_without:extend_deadline|date|after:today',
        'extend_deadline' => 'required_without:deadline|integer|min:3',
    ];

    private static $max_amount;

    public static function set_max_amount($amount)
    {
        static::$max_amount = $amount;
    }

    public function isValid($rules = null)
    {
        if (isset(static::$max_amount)) {
            $this->validation_rules['amount'] .= '|max:'.static::$max_amount;
        }
        $rules = $rules ?: $this->validation_rules;
        $dirty = $this->getDirty();
        if ($this->attributes['voting_closed'] && ! isset($dirty['deadline'])) {
            $rules['deadline'] = 'required|date';
        }
        $validation = Validator::make($this->attributes, $rules);

        if ($validation->passes()) {
            return true;
        }

        $this->errors = $validation->messages();

        return false;
    }

    public function setExtendDeadlineAttribute($num_days)
    {
        if (is_numeric($num_days)) {
            $num_days = (int) $num_days;
            $deadline = (isset($this->deadline)) ? $this->deadline_c : Carbon::parse('now');
            $this->attributes['deadline'] = $deadline->addDays($num_days)->toDateTimeString();
        }
    }

    public function setDeadlineAttribute($value)
    {
        if (empty($value)) {
            $this->attributes['deadline'] = null;
        } else {
            // Laravel's docs SAY that the 'date' validation will pass anything considered valid by strtotime
            // but IT LIES!!!  So we do a hacky validation here:
            $check_date = strtotime($value);
            $this->attributes['deadline'] = ($check_date === false) ? $value : Carbon::parse($value)->toDateTimeString();
        }
    }

    /* This is screwing up any type="date" form inputs in chrome:
        public function getDeadlineAttribute($value) {
            return isset($value) ? Carbon::parse($value) : $value;
        }
    */

    public function getDeadlineCAttribute()
    {
        return isset($this->attributes['deadline']) ? Carbon::parse($this->attributes['deadline']) : null;
    }

    public function getDeadlineLocalAttribute()
    {
        if (isset($this->attributes['deadline'])) {
            $deadline = Carbon::parse($this->attributes['deadline']);
            $user_tz = User::get_local_tz();

            return (isset($user_tz)) ? $deadline->setTimezone($user_tz) : $deadline;
        }

        return null;
    }

    public function getVoteStatusAttribute()
    {
        if ($this->voting_closed) {
            if ($this->vote_passed) {
                $status = 'approved';
            } else {
                $status = 'expired';
            }
        } else {
            if ($this->vote_passed) {
                $status = 'provisionally approved';
            } else {
                $status = 'open';
            }
        }

        return $status;
    }

    public function has_voted(User $user)
    {
        return $this->users_voted()->find($user->id);
    }

    public static $payment_status_select = [
        // 'awaiting_approval' => 'Awaiting Approval',
        'under_review' => 'Under Review',
        'need_payment_info' => 'Need Payment Info',
        'processing' => 'Processing',
        'payment_sent' => 'Payment Sent',
        'payment_received' => 'Payment Received',
        'cancelled' => 'Payment Cancelled',
    ];

    public function vote(User $user, $vote, $post_message = true)
    {
        if ($this->has_voted($user)) {
            if ($this->users_voted()->find($user->id)->vote != $vote) {
                $this->users_voted()->updateExistingPivot($user->id, ['vote' => $vote], true);
                $message = "Changed vote to $vote";
            }
        } else {
            $this->users_voted()->attach($user->id, ['vote' => $vote]);
            $message = "Voted $vote";
        }

        // If the vote has changed:
        if (isset($message) && $post_message) {
            $this->load('issue.discussion.posts.users_read');
            $this->issue->discussion->post_comment($user, $message);  // post their comment
            $this->test_approved();    // and change the proposal's status if the vote now passes
            $this->save();
        }
    }

    public function my_vote($user)
    {
        if (! isset($this->users_voted)) {
            $this->load('users_voted');
        }
        $me = $this->users_voted->find($user);

        return (isset($me)) ? $me->pivot->vote : null;
    }

    public function vote_tally()
    {
        $issue = $this->issue ?: $this->issue()->first();
        $discussion = $issue->discussion ?: $issue->discussion()->first();
        $group = $discussion->group()->first();
        $tally = [
            'concur' => 0,
            'open' => 0,
            'wonder' => 0,
            'disfavor' => 0,
            'no_vote' => $group->active_members()->count(),
        ];
        $votes = DB::table('proposal_votes')->where('proposal_id', '=', $this->id)->pluck('vote')->toArray();
        foreach ($votes as $vote) {
            $tally[$vote]++;
            $tally['no_vote']--;
        }

        return $tally;
    }

    public function votes_for_chart()
    {
        $tally = $this->vote_tally();
        $total_votes = 0;
        foreach ($tally as $num_votes) {
            $total_votes += $num_votes;
        }

        if ($total_votes) {
            foreach ($tally as &$num_votes) {
                $num_votes = round(100 * $num_votes / $total_votes);
            }

            return $tally;
        }
    }

    public function test_approved()
    {
        $issue = $this->issue ?: $this->issue()->first();
        $discussion = $issue->discussion ?: $issue->discussion()->first();
        $group = $discussion->group()->first();
        $group->load('members', 'voting_matrix');

        $tally = $this->vote_tally();

        $num_yes = 0;
        $eligible_voters = 0;
        foreach ($tally as $vote => $num) {
            $eligible_voters += $num;
            switch ($vote) {
                case 'concur':
                case 'open':
                    $num_yes += $num;
                    break;
            }
        }

        /* Obsolete:
        if ($group->voting_matrix->vote_passes($group->current_funds,$this->amount,$eligible_voters,$num_yes)) {
            $this->status = 'approved';
            $this->save();
        }
        */
        $this->vote_passed = $group->voting_matrix->vote_passes($group->current_funds, $this->amount, $eligible_voters, $num_yes);
    }
}
