<?php

namespace App\Models;

class CsvHelper
{
    public static function response_stream($hash_array, $filename = 'output')
    {
        // add headers to the top of the file
        array_unshift($hash_array, array_keys($hash_array[0]));

        $callback = function () use ($hash_array) {
            $FH = fopen('php://output', 'w');
            foreach ($hash_array as $row) {
                fputcsv($FH, $row);
            }
            fclose($FH);
        };

        $headers = [
            'Content-type' => 'application/csv',   'Cache-Control' => 'must-revalidate, post-check=0, pre-check=0',   'Content-type' => 'text/csv',   'Content-Disposition' => "attachment; filename=$filename.csv",   'Expires' => '0',   'Pragma' => 'public',
        ];

        return \Response::stream($callback, 200, $headers);
    }
}
