<?php

namespace App\Models;

use Monarobase\CountryList\CountryListFacade as Countries;

class LocationHelper
{
    public static $current_countries = ['CA', 'ZA', 'GB', 'US'];

    private static function isCommonCountry($country_abbrev)
    {
        return in_array($country_abbrev, self::$current_countries);
    }

    public static function countriesForSelect($unselected_text = 'Please Select...')
    {
        // Use getList() instead of all() which doesn't exist
        $list = Countries::getList('en');

        // Convert to collection for easier manipulation
        $list = collect($list);

        // Filter out entries with keys that aren't 2 characters (if needed)
        $list = $list->filter(function ($value, $key) {
            return strlen($key) == 2;
        })->toArray();

        $add_to_array = function ($array, $key) use ($list) {
            if (isset($list[$key])) {
                $array[$key] = $list[$key];
            }
            return $array;
        };

        $common_countries = array_reduce(self::$current_countries, $add_to_array, []);

        return array_merge(
            ['0' => $unselected_text],
            $common_countries,
            ['1' => '--------------------'],
            $list
        );
    }

    public static function subdivisionsForSelect($country = 'US', $unselected_text = 'Please Select...')
    {
        // If country is US, manually provide the states
        if ($country === 'US') {
            $states = [
                'US-AL' => 'Alabama',
                'US-AK' => 'Alaska',
                'US-AZ' => 'Arizona',
                'US-AR' => 'Arkansas',
                'US-CA' => 'California',
                'US-CO' => 'Colorado',
                'US-CT' => 'Connecticut',
                'US-DE' => 'Delaware',
                'US-FL' => 'Florida',
                'US-GA' => 'Georgia',
                'US-HI' => 'Hawaii',
                'US-ID' => 'Idaho',
                'US-IL' => 'Illinois',
                'US-IN' => 'Indiana',
                'US-IA' => 'Iowa',
                'US-KS' => 'Kansas',
                'US-KY' => 'Kentucky',
                'US-LA' => 'Louisiana',
                'US-ME' => 'Maine',
                'US-MD' => 'Maryland',
                'US-MA' => 'Massachusetts',
                'US-MI' => 'Michigan',
                'US-MN' => 'Minnesota',
                'US-MS' => 'Mississippi',
                'US-MO' => 'Missouri',
                'US-MT' => 'Montana',
                'US-NE' => 'Nebraska',
                'US-NV' => 'Nevada',
                'US-NH' => 'New Hampshire',
                'US-NJ' => 'New Jersey',
                'US-NM' => 'New Mexico',
                'US-NY' => 'New York',
                'US-NC' => 'North Carolina',
                'US-ND' => 'North Dakota',
                'US-OH' => 'Ohio',
                'US-OK' => 'Oklahoma',
                'US-OR' => 'Oregon',
                'US-PA' => 'Pennsylvania',
                'US-RI' => 'Rhode Island',
                'US-SC' => 'South Carolina',
                'US-SD' => 'South Dakota',
                'US-TN' => 'Tennessee',
                'US-TX' => 'Texas',
                'US-UT' => 'Utah',
                'US-VT' => 'Vermont',
                'US-VA' => 'Virginia',
                'US-WA' => 'Washington',
                'US-WV' => 'West Virginia',
                'US-WI' => 'Wisconsin',
                'US-WY' => 'Wyoming',
                'US-DC' => 'District of Columbia',
                'US-AS' => 'American Samoa',
                'US-GU' => 'Guam',
                'US-MP' => 'Northern Mariana Islands',
                'US-PR' => 'Puerto Rico',
                'US-UM' => 'United States Minor Outlying Islands',
                'US-VI' => 'Virgin Islands, U.S.'
            ];

            return array_merge(['0' => $unselected_text, 'zzzz' => 'Non-US'], $states);
        }

        // For other countries, try to get data from the package
        try {
            // Get the country data directly
            $countryData = Countries::getOne($country);

            // If country not found or doesn't have states data
            if (!$countryData) {
                return ['0' => $unselected_text, 'zzzz' => 'Non-US'];
            }

            $states = [];

            // If the country has a 'states' property that's an array
            if (isset($countryData['states']) && is_array($countryData['states'])) {
                foreach ($countryData['states'] as $code => $name) {
                    $states[$code] = is_array($name) ? $name['name'] : $name;
                }
            }

            return array_merge(['0' => $unselected_text, 'zzzz' => 'Non-US'], $states);
        } catch (\Exception $e) {
            // Fallback to empty states if there's an error
            return ['0' => $unselected_text, 'zzzz' => 'Non-US'];
        }
    }

    private static function memoizedCountries($country_abbrev)
    {
        static $cached_countries = [];

        // For some reason a blank string matches the Carribean Netherlands...
        // probably because they don't have a two-letter abbrev for that country.
        if (trim($country_abbrev) === '') {
            return;
        }

        if (! array_key_exists($country_abbrev, $cached_countries)) {
            $country = Countries::getOne($country_abbrev);
            $cached_countries[$country_abbrev] = $country ? $country : null;
        }

        return $cached_countries[$country_abbrev];
    }

    private static function memoizedStates($country_abbrev, $state_abbrev)
    {
        static $cached_states = [];
        $key = "{$country_abbrev}:{$state_abbrev}";

        if (! array_key_exists($key, $cached_states)) {
            $country = self::memoizedCountries($country_abbrev);
            if (is_null($country)) {
                $cached_states[$key] = null;
            } else {
                // Check if $country is an object before calling methods on it
                if (is_object($country)) {
                    $state = $country->hydrateStates()->states->where('iso_3166_2', $state_abbrev)->first();
                    $cached_states[$key] = (count($state)) ? $state : null;
                } else {
                    // If $country is not an object (likely a string), return null
                    $cached_states[$key] = null;
                }
            }
        }

        return $cached_states[$key];
    }

    public static function countryName($abbrev)
    {
        $country = self::memoizedCountries($abbrev);

        // Check if $country is an array with the expected structure
        if (isset($country) && is_array($country) && isset($country['name']) && isset($country['name']['common'])) {
            return $country['name']['common'];
        }

        return $abbrev;
    }

    public static function subdivisionName($abbrev, $country_abbrev = 'US')
    {
        if ($abbrev == 'zzzz') {
            return '';
        }

        $state = self::memoizedStates($country_abbrev, $abbrev);

        return (isset($state)) ? $state['name'] : $abbrev;
    }
}
