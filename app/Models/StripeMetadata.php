<?php

namespace App\Models;

abstract class StripeMetadata_generic
{
    protected $data;

    public function __construct($data = null)
    {
        if (! isset($data)) {
            $data = [];
        }
        $this->data = $data;
    }

    public function toArray()
    {
        $result = [];
        foreach ($this->data as $key => $val) {
            $result[$key] = json_encode($val);
        }

        return $result;
    }

    public function toJson()
    {
        return json_encode($this->toArray());
    }

    public function __toString()
    {
        return $this->toJson();
    }

    abstract public function setGroup($group);

    abstract public function setUser($user);
}

class StripeMetadata_v1 extends StripeMetadata_generic
{
    private $entrypoint;

    public function setCCOps()
    {
        $this->data['for_cc_ops'] = 'true';

        return $this;
    }

    public function setGroup($group)
    {
        $this->data['group'] = [
            'id' => $group->id,
            'name' => $group->name,
        ];

        return $this;
    }

    public function setUser($user)
    {
        $address = $user->address ? $user->address : new Address;
        $this->data['user'] = [
            'id' => $user->id,
            'name' => $user->firstname.' '.$user->lastname,
            'address' => $address->toArray(),
        ];

        return $this;
    }
}

class StripeMetadata
{
    private static $current_version = 'v1';

    private $object;

    public static function create($data = [], $version = null)
    {
        return new static($data,$version);
    }

    public function __construct($data = [], $version = null)
    {
        if (! isset($version)) {
            $version = static::$current_version;
        }

        $object_type = 'App\Models\StripeMetadata_'.$version;
        $this->object = new $object_type($data);
    }

    public function __call($fcn, $args)
    {
        return call_user_func_array([$this->object, $fcn], $args);
    }
}
