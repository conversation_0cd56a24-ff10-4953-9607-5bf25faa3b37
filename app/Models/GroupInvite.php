<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;

class GroupInvite extends Model
{
    public function inviter(): BelongsTo
    {
        return $this->belongsTo(User::class, 'inviter_id');
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function group(): BelongsTo
    {
        return $this->belongsTo(Group::class, 'group_id');
    }

    private function setNewToken()
    {
        do {
            $token = bin2hex(openssl_random_pseudo_bytes(16));
            $matches = $this->where('token', '=', $token)->count();
        } while ($matches); // make sure it's unique
        $this->token = $token;

    }

    public static function inviteUser(User $user, Group $group)
    {
        // invite them only if they're not already a member or already have an invite:
        if (! $group->members->contains($user)) {
            $group_invite = new GroupInvite;
            $group_invite->user_id = $user->id;
            $group_invite->group_id = $group->id;
            $group_invite->inviter_id = Auth::User()->id;
            $group_invite->save();

            Mail::send(['emails.group_invite_user', 'emails.group_invite_user-text'], ['user' => $user, 'group' => $group], function ($message) use ($user, $group) {
                $message->to($user->email, $user->firstname.' '.$user->lastname);
                $message->subject('Commonchange: Invitation to join '.$group->name);
            });

            return true;
        }

        return false;
    }

    public static function inviteNonUser($email, Group $group)
    {
        $group_invite = GroupInvite::where('email', $email)->where('group_id', $group->id)->first();
        if (! isset($group_invite)) {
            $group_invite = new GroupInvite;
            $group_invite->email = $email;
            $group_invite->group_id = $group->id;
            $group_invite->setNewToken();
        }
        $group_invite->inviter_id = Auth::User()->id;
        $group_invite->save();

        return $group_invite->token;
    }

    public static function removeUser(User $user, Group $group)
    {
        foreach (GroupInvite::where('user_id', $user->id)->where('group_id', $group->id)->get() as $invite) {
            $invite->delete();
        }
    }

    public function setInviteToAuthUser()
    {
        $group = $this->group()->first();

        $user = Auth::User();
        if (! $group || $group->members()->find($user->id)) {
            // This group doesn't exist anymore, or I'm already a member of this group,
            // so this invite is pointless (and messes up the MyGroups list).
            $this->delete();
        } else {
            // Associate this invite with the current user
            $this->email = null;
            $this->token = null;
            $this->user_id = $user->id;
            $this->save();
        }
    }

    public static function checkToken()
    {
        $group_invite_token = session()->pull('group_invite_token');  // grab the token and remove it from the session.
        if (isset($group_invite_token)) {
            $group_invites = GroupInvite::where('token', '=', $group_invite_token)->get();
            foreach ($group_invites as $group_invite) {
                $group_invite->setInviteToAuthUser();
            }
        }
    }

    public static function checkTokenAndEmail()
    {
        self::checkToken();
        $group_invites = GroupInvite::where('email', '=', Auth::User()->email)->get();
        foreach ($group_invites as $group_invite) {
            $group_invite->setInviteToAuthUser();
        }
    }
}
