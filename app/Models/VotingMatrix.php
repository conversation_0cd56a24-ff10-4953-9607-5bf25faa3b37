<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class VotingMatrix extends Model
{
    use SoftDeletes;

    protected $table = 'voting_matrices';

    public $timestamps = false;

    public function groups(): HasMany
    {
        return $this->hasMany(Group::class);
    }

    public function voting_matrix_rows(): HasMany
    {
        return $this->hasMany(VotingMatrixRow::class)->orderBy('funds_percentage_upper', 'asc');
    }

    public function vote_passes($available_funds, $funds_requested, $num_eligible_voters, $num_yes)
    {
        if (! ($num_eligible_voters > 0)) {
            return false;
        }  // handle divide-by-zero problems!

        // Find the row in this group's matrix that applies to the
        //  percentage of funds being requested from the group's total funds:
        $funds_percentage = ($funds_requested < $available_funds) ? 100 * $funds_requested / $available_funds : 100;
        $matrix_row = $this->voting_matrix_rows()->where('funds_percentage_upper', '>=', $funds_percentage)->first();

        // Calculate percentage of 'yes' votes we have:
        $percent_yes = 100 * $num_yes / $num_eligible_voters;

        // Determine percentage of yes votes required by the Voting Matrix row:
        if ($num_eligible_voters >= 100) {
            $percent_required = $matrix_row->num_members_5;
        } elseif ($num_eligible_voters >= 21) {
            $percent_required = $matrix_row->num_members_4;
        } elseif ($num_eligible_voters >= 14) {
            $percent_required = $matrix_row->num_members_3;
        } elseif ($num_eligible_voters >= 6) {
            $percent_required = $matrix_row->num_members_2;
        } else {
            $percent_required = $matrix_row->num_members_1;
        }

        // if the vote passes, return true:
        return $percent_yes >= $percent_required;
    }
}
