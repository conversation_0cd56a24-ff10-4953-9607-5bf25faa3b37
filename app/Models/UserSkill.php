<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UserSkill extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'title',
        'description',
        'category',
        'experience_level',
        'is_available',
        'hourly_rate',
        'currency',
        'is_free',
        'requirements'
    ];

    protected $casts = [
        'is_available' => 'boolean',
        'is_free' => 'boolean',
        'hourly_rate' => 'decimal:2'
    ];

    /**
     * Get the user that owns the skill.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope to get only available skills.
     */
    public function scopeAvailable($query)
    {
        return $query->where('is_available', true);
    }

    /**
     * Scope to get skills by category.
     */
    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    /**
     * Scope to get free skills.
     */
    public function scopeFree($query)
    {
        return $query->where('is_free', true);
    }

    /**
     * Get formatted hourly rate with currency.
     */
    public function getFormattedHourlyRateAttribute()
    {
        if (!$this->hourly_rate || $this->is_free) {
            return 'Free';
        }

        $symbols = [
            'USD' => '$',
            'GBP' => '£',
            'EUR' => '€',
        ];

        $symbol = $symbols[$this->currency] ?? $this->currency;
        return $symbol . number_format($this->hourly_rate, 2) . '/hour';
    }

    /**
     * Get category display name.
     */
    public function getCategoryDisplayAttribute()
    {
        $categories = [
            'technical' => 'Technical',
            'creative' => 'Creative',
            'manual' => 'Manual Labor',
            'professional' => 'Professional Services',
            'educational' => 'Educational',
            'health' => 'Health & Wellness',
            'domestic' => 'Domestic',
            'other' => 'Other'
        ];

        return $categories[$this->category] ?? ucfirst($this->category);
    }

    /**
     * Get experience level display name.
     */
    public function getExperienceLevelDisplayAttribute()
    {
        $levels = [
            'beginner' => 'Beginner',
            'intermediate' => 'Intermediate',
            'advanced' => 'Advanced',
            'expert' => 'Expert'
        ];

        return $levels[$this->experience_level] ?? ucfirst($this->experience_level);
    }
}
