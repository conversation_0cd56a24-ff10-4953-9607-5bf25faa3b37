<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Validator;

class PayInfo extends Model
{
    use SoftDeletes;

    protected $table = 'payment_info';

    public function address()
    {
        return $this->morphOne(Address::class, 'addressable');
    }

    public function proposal(): HasOne
    {
        return $this->hasOne(Proposal::class);
    }

    protected $with = ['address'];  // automagically loads the address object when we load a recipient.

    protected $fillable = [
        'notes', 'payment_type',
        'wire_bank_name', 'wire_bank_routing', 'wire_bank_address', 'wire_acct_number',
    ];

    public function setPasswordAttribute($cleartext)
    {
        $this->attributes['password'] = Hash::make($cleartext);
    }

    // /////////// Validation //////////////

    public $errors;

    public static $rules = [
        'firstname' => 'regex:/^[\pL\pN \'\-.]*$/',
        'lastname' => 'regex:/^[\pL\pN \'\-.]*$/',
        'email' => 'email',
        'phone' => 'min:10',
        'paypal_email' => 'email',
        'uk_sortcode' => 'regex:/^\d{2}-\d{2}-\d{2}$/',
        'uk_account' => 'regex:/^\d+$/',
    ];

    // Had to do this because we no longer use paypal, but the default error message includes the field name.
    public static $custom_messages = [
        'paypal_email.email' => 'Please enter a valid email address.',
    ];

    public function isValid()
    {
        $validation = Validator::make($this->attributes, static::$rules, static::$custom_messages);

        if ($validation->passes()) {
            return true;
        }

        $this->errors = $validation->messages();

        return false;
    }
}
