<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Subscription extends Model
{
    public $table = 'user_subscriptions';

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    protected $fillable = ['customer_id'];

    public static function get_or_create_customer($user, $source = null)
    {
        $stripe = new \Stripe\StripeClient($_ENV['STRIPE_SECRET_KEY']);
        $subscriber = Subscription::where('user_id', (int) $user->id)->first();

        if (! isset($subscriber)) {
            $metadata = StripeMetadata::create()->setUser($user)->toArray();
            $data = [
                'email' => $user->email,
                'metadata' => $metadata,
            ];
            if (isset($source)) {
                $data['source'] = $source;
            }
            $customer = $stripe->customers->create($data);
            $subscriber = new Subscription(['customer_id' => $customer->id]);
            $subscriber->user()->associate($user);
            $subscriber->save();
        } else {
            $customer = $stripe->customers->retrieve($subscriber->customer_id);
            if (isset($source)) {
                $customer->card = $source;
                $customer->save();
            }
        }

        return $customer;
    }

    public static function get_subscription($customer, $plan)
    {
        $existing_plan = null;
        foreach ($customer->subscriptions->data as $sub) {
            if ($sub->plan->id == $plan) {
                $existing_plan = $sub->id;
            }
        }

        return $existing_plan;
    }

    public static function set_subscription($user, $plan, $amount, $metadata, $source = null)
    {
        $customer = self::get_or_create_customer($user);
        $existing_plan = self::get_subscription($customer, $plan);

        if (isset($existing_plan)) {
            $subscription = $customer->subscriptions->retrieve($existing_plan);
            $subscription->quantity = (int) $amount;
            $subscription->metadata = $metadata;
            if (isset($source)) {
                $subscription->source = $source;
            }
            $subscription->save();
        } else {
            $data = [
                'plan' => $plan,
                'quantity' => (int) $amount,
                'metadata' => $metadata,
            ];
            if (isset($source)) {
                $data['source'] = $source;
            }
            $customer->subscriptions->create($data);
        }
    }

    public static function cancel_subscription($user, $plan)
    {
        $customer = self::get_or_create_customer($user);
        $existing_plan = self::get_subscription($customer, $plan);
        if (isset($existing_plan)) {
            $customer->subscriptions->retrieve($existing_plan)->cancel();
        }
    }
}
