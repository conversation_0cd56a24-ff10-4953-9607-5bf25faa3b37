<?php

namespace App\Models;

use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;

class CollectionPaginator
{
    public static function make(Request $request, $collection, $page_size)
    {
        $page = (int) $request->input('page', 1);
        if ($page < 1) {
            $page = 1;
        }

        $start = $page_size * ($page - 1);
        $end = $start + $page_size;
        $items = $collection->slice($start, $end);

        $options = [
            'path' => $request->url(),
            'query' => $request->all(),
        ];

        return new LengthAwarePaginator($items, $collection->count(), $page_size, $page, $options);
    }
}
