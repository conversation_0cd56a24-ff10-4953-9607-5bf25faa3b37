<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Common Change - Verify Email</title>
    <style>
        :root {
            /* Primary colors */
            --primary-green: #9DC244;
            --primary-purple: #8854D0;
            --primary-indigo: #505090;
            --primary-navy: #243347;
            --primary-black: #1A1A1A;
            
            /* Light colors */
            --light-cream: #F9F9F0;
            --light-lavender: #F0F0F9;
            --light-blue: #E9EFF2;
            --light-gray: #F5F5F5;
            --light-white: #FFFFFF;
            
            /* Font */
            --font-family: 'Arial', sans-serif;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: var(--font-family);
        }
        
        body {
            background-color: var(--light-white);
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        /* Login Page */
        .login-page {
            display: flex;
            height: 100vh;
        }
        
        .login-image {
            flex: 1;
            background-size: cover;
            background-position: center;
        }
        
        .login-form {
            flex: 1;
            padding: 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        
        .login-logo {
            margin-bottom: 50px;
        }
        
        h1 {
            font-size: 28px;
            color: var(--primary-navy);
            margin-bottom: 20px;
        }
        
        .instruction-text {
            font-size: 16px;
            margin-bottom: 10px;
            color: var(--primary-black);
        }
        
        .email-highlight {
            font-weight: bold;
        }
        
        .expiry-note {
            font-size: 16px;
            margin-bottom: 30px;
            color: var(--primary-black);
        }
        
        .didnt-receive {
            margin-bottom: 30px;
            color: var(--primary-black);
        }
        
        .try-again {
            color: var(--primary-purple);
            text-decoration: none;
            font-weight: 500;
        }
        
        .try-again:hover {
            text-decoration: underline;
        }
        
        .otp-container {
            display: flex;
            gap: 10px;
            margin-bottom: 30px;
        }
        
        .otp-input {
            width: 60px;
            height: 60px;
            font-size: 24px;
            text-align: center;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        
        .otp-input:focus {
            border-color: var(--primary-purple);
            outline: none;
        }
        
        .btn-primary {
            background-color: var(--primary-purple);
            color: white;
            border: none;
            padding: 15px;
            width: 100%;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            margin-bottom: 20px;
        }
        
        .btn-primary:hover {
            background-color: #7643BE;
        }
        
        .divider {
            text-align: center;
            margin: 20px 0;
            color: #777;
        }
        
        .social-login {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        
        .social-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: white;
            border: 1px solid #ddd;
            padding: 12px;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            gap: 10px;
        }
        
        .social-btn:hover {
            background-color: #f8f8f8;
        }
        
        .google-icon {
            width: 20px;
            height: 20px;
        }
        
        .login-footer {
            text-align: center;
            margin-top: 30px;
        }
        
        .login-footer a {
            color: var(--primary-purple);
            text-decoration: none;
        }
        
        .login-footer a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="login-page">
        <div class="login-image" style="background-image: url('/api/placeholder/600/800');"></div>
        <div class="login-form">
            <div class="login-logo">
                <img src="/api/placeholder/180/60" alt="Common Change Logo">
            </div>
            
            <h1>Check your email for a code.</h1>
            
            <p class="instruction-text">
                We've sent a 6-character code to <span class="email-highlight"><EMAIL></span>.
            </p>
            <p class="expiry-note">
                The code expires shortly, so enter it soon.
            </p>
            
            <p class="didnt-receive">
                Didn't receive the email? <a href="#" class="try-again">Try again</a>
            </p>
            
            <div class="otp-container">
                <input type="text" maxlength="1" class="otp-input" autofocus>
                <input type="text" maxlength="1" class="otp-input">
                <input type="text" maxlength="1" class="otp-input">
                <input type="text" maxlength="1" class="otp-input">
                <input type="text" maxlength="1" class="otp-input">
                <input type="text" maxlength="1" class="otp-input">
            </div>
            
            <button class="btn-primary">Next</button>
            
            <div class="divider">OR</div>
            
            <div class="social-login">
                <button class="social-btn">
                    <img src="/api/placeholder/20/20" alt="Google Icon" class="google-icon">
                    Continue with Google
                </button>
                
                <button class="social-btn">
                    <img src="/api/placeholder/20/20" alt="Apple Icon" class="google-icon">
                    Continue with Apple
                </button>
                
                <button class="social-btn">
                    <img src="/api/placeholder/20/20" alt="Microsoft Icon" class="google-icon">
                    Continue with Microsoft
                </button>
                
                <button class="social-btn">
                    <img src="/api/placeholder/20/20" alt="Facebook Icon" class="google-icon">
                    Continue with Facebook
                </button>
            </div>
            
            <div class="login-footer">
                Already have an account? <a href="#">Login</a>
            </div>
        </div>
    </div>
    
    <script>
        // Auto-tab between OTP input fields
        const otpInputs = document.querySelectorAll('.otp-input');
        
        otpInputs.forEach((input, index) => {
            input.addEventListener('input', function() {
                if (this.value.length === 1 && index < otpInputs.length - 1) {
                    otpInputs[index + 1].focus();
                }
            });
            
            input.addEventListener('keydown', function(e) {
                if (e.key === 'Backspace' && !this.value && index > 0) {
                    otpInputs[index - 1].focus();
                }
            });
        });
    </script>
</body>
</html>
