<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Common Change</title>
    <style>
        :root {
            /* Primary colors */
            --primary-green: #9DC244;
            --primary-purple: #8854D0;
            --primary-indigo: #505090;
            --primary-navy: #243347;
            --primary-black: #1A1A1A;
            
            /* Light colors */
            --light-cream: #F9F9F0;
            --light-lavender: #F0F0F9;
            --light-blue: #E9EFF2;
            --light-gray: #F5F5F5;
            --light-white: #FFFFFF;
            
            /* Font */
            --font-family: 'Arial', sans-serif;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: var(--font-family);
        }
        
        body {
            background-color: var(--light-white);
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        /* Header */
        header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 0;
        }
        
        .logo {
            height: 50px;
        }
        
        .user-profile {
            display: flex;
            align-items: center;
            gap: 5px;
            background-color: #000;
            border-radius: 30px;
            padding: 5px;
        }
        
        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            overflow: hidden;
            background-color: #ccc;
        }
        
        .user-initial {
            width: 25px;
            height: 25px;
            background-color: #FFD700;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }
        
        /* Login Page */
        .login-page {
            display: flex;
            height: 100vh;
        }
        
        .login-image {
            flex: 1;
            background-size: cover;
            background-position: center;
        }
        
        .login-form {
            flex: 1;
            padding: 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        
        .login-logo {
            margin-bottom: 50px;
        }
        
        h1 {
            font-size: 32px;
            color: var(--primary-navy);
            margin-bottom: 20px;
        }
        
        .login-instructions {
            font-size: 18px;
            margin-bottom: 30px;
        }
        
        input[type="email"],
        input[type="text"],
        select {
            width: 100%;
            padding: 15px;
            margin-bottom: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        
        .btn-primary {
            background-color: var(--primary-purple);
            color: white;
            border: none;
            padding: 15px;
            width: 100%;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            margin-bottom: 20px;
        }
        
        .btn-primary:hover {
            background-color: #7643BE;
        }
        
        .btn-secondary {
            background-color: white;
            color: var(--primary-indigo);
            border: 1px solid var(--primary-indigo);
            padding: 15px;
            width: 100%;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
        }
        
        .btn-secondary:hover {
            background-color: #F0F0F9;
        }
        
        .divider {
            text-align: center;
            margin: 20px 0;
            color: #777;
        }
        
        .social-login {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        
        .social-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: white;
            border: 1px solid #ddd;
            padding: 12px;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            gap: 10px;
        }
        
        .social-icon {
            width: 20px;
            height: 20px;
        }
        
        .login-footer {
            text-align: center;
            margin-top: 30px;
        }
        
        .login-footer a {
            color: var(--primary-purple);
            text-decoration: none;
        }
        
        /* Content styles */
        .breadcrumb {
            margin-bottom: 20px;
            color: var(--primary-indigo);
        }
        
        .breadcrumb a {
            color: var(--primary-indigo);
            text-decoration: none;
            margin-right: 10px;
        }
        
        .breadcrumb span {
            margin: 0 10px;
            color: #777;
        }
        
        .heading {
            font-size: 28px;
            color: var(--primary-navy);
            margin-bottom: 20px;
        }
        
        .large-body-text {
            font-size: 20px;
            margin-bottom: 20px;
            line-height: 1.4;
        }
        
        .semi-bold {
            font-weight: 600;
        }
        
        .detail-text {
            font-size: 14px;
            color: #555;
            margin-bottom: 10px;
        }
        
        .detail-text-alt {
            font-size: 14px;
            color: #777;
            margin-bottom: 10px;
        }
        
        .longform-text {
            font-size: 16px;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--primary-indigo);
        }
        
        .text-link {
            color: var(--primary-indigo);
            text-decoration: none;
        }
        
        .text-link:hover {
            text-decoration: underline;
            color: var(--primary-purple);
        }
        
        .button-group {
            display: flex;
            gap: 20px;
            margin: 20px 0;
        }
        
        .button {
            padding: 10px 20px;
            border-radius: 5px;
            font-size: 14px;
            cursor: pointer;
        }
        
        .button-primary {
            background-color: var(--primary-purple);
            color: white;
            border: none;
        }
        
        .button-primary:hover {
            background-color: #7643BE;
        }
        
        .button-secondary {
            background-color: white;
            color: var(--primary-indigo);
            border: 1px solid var(--primary-indigo);
        }
        
        .button-secondary:hover {
            background-color: #F0F0F9;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 8px;
            font-size: 14px;
            color: #555;
        }
        
        .form-control {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        
        .select-wrapper {
            position: relative;
        }
        
        .select-wrapper:after {
            content: '▼';
            font-size: 12px;
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            pointer-events: none;
        }
        
        .toggle-switch {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .toggle-label {
            margin-left: 10px;
            font-size: 14px;
        }
        
        .toggle {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
        }
        
        .toggle input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        
        .toggle-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 24px;
        }
        
        .toggle-slider:before {
            position: absolute;
            content: "";
            height: 16px;
            width: 16px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        
        input:checked + .toggle-slider {
            background-color: var(--primary-green);
        }
        
        input:checked + .toggle-slider:before {
            transform: translateX(26px);
        }
    </style>
</head>
<body>
    <!-- Design System Page -->
    <div class="container" id="design-system" style="display: none;">
        <header>
            <img src="/api/placeholder/150/50" alt="Common Change Logo" class="logo">
            <div class="user-profile">
                <div class="user-avatar">
                    <img src="/api/placeholder/40/40" alt="User Avatar">
                </div>
                <div class="user-initial">K</div>
            </div>
        </header>
        
        <div class="content">
            <h1 class="heading">Heading</h1>
            
            <div class="breadcrumb">
                <a href="#">Breadcrumb Home</a>
                <span>›</span>
                <a href="#">Secondary</a>
                <span>›</span>
                Page Title
            </div>
            
            <p class="large-body-text">Large Body Text. <span class="semi-bold">Sometimes Semi-Bold.</span></p>
            
            <div class="detail-text">Detail Text</div>
            <div class="detail-text-alt">Detail Text Alt</div>
            
            <p class="longform-text">Longform Body Text - Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit, esse cillum dolore eu fugiat nulla pariatur.</p>
            
            <div class="button-group">
                <a href="#" class="text-link">Text link</a>
                <a href="#" class="text-link" style="color: var(--primary-purple);">Text link hover</a>
            </div>
            
            <div class="button-group">
                <button class="button button-primary">Button</button>
                <button class="button button-primary" style="background-color: #7643BE;">Button Primary - Hover</button>
            </div>
            
            <div class="button-group">
                <button class="button button-secondary">Button</button>
                <button class="button button-secondary" style="background-color: #F0F0F9;">Button Secondary - Hover</button>
            </div>
            
            <div class="form-group">
                <label class="form-label">Text Field</label>
                <input type="text" class="form-control" placeholder="Placeholder">
            </div>
            
            <div class="form-group">
                <label class="form-label">Select Field</label>
                <div class="select-wrapper">
                    <select class="form-control">
                        <option>Placeholder</option>
                    </select>
                </div>
            </div>
            
            <div class="toggle-switch">
                <label class="toggle">
                    <input type="checkbox" checked>
                    <span class="toggle-slider"></span>
                </label>
                <span class="toggle-label">Toggle On</span>
            </div>
            
            <div class="toggle-switch">
                <label class="toggle">
                    <input type="checkbox">
                    <span class="toggle-slider"></span>
                </label>
                <span class="toggle-label">Toggle Off</span>
            </div>
        </div>
    </div>
    
    <!-- Login Page -->
    <div class="login-page">
        <div class="login-image" style="background-image: url('/api/placeholder/600/800');"></div>
        <div class="login-form">
            <div class="login-logo">
                <img src="/api/placeholder/180/60" alt="Common Change Logo">
            </div>
            
            <h1>Welcome</h1>
            <p class="login-instructions">
                Create an account to begin.<br>
                First, enter your email.
            </p>
            
            <input type="email" placeholder="Email address">
            <button class="btn-primary">Next</button>
            
            <div class="divider">OR</div>
            
            <div class="social-login">
                <button class="social-btn">
                    <img src="/api/placeholder/20/20" alt="Google Icon" class="social-icon">
                    Continue with Google
                </button>
                
                <button class="social-btn">
                    <img src="/api/placeholder/20/20" alt="Apple Icon" class="social-icon">
                    Continue with Apple
                </button>
                
                <button class="social-btn">
                    <img src="/api/placeholder/20/20" alt="Microsoft Icon" class="social-icon">
                    Continue with Microsoft
                </button>
                
                <button class="social-btn">
                    <img src="/api/placeholder/20/20" alt="Facebook Icon" class="social-icon">
                    Continue with Facebook
                </button>
            </div>
            
            <div class="login-footer">
                Already have an account? <a href="#">Login</a>
            </div>
        </div>
    </div>
    
    <script>
        // Toggle between design system and login page
        // For demo purposes, uncomment to show design system
        // document.getElementById('design-system').style.display = 'block';
        // document.querySelector('.login-page').style.display = 'none';
    </script>
</body>
</html>
