# Email Verification Flow Documentation - v1.0 (May 2023)

## Overview
This document describes the email verification flow implemented in the Common Change application as of May 2023. This serves as a reference in case you need to revert to this implementation in the future.

## Flow Steps

1. **Landing Page** (`resources/views/landing.blade.php`)
   - User enters email address
   - Client-side validation occurs
   - AJAX request sent to `register.send-otp` endpoint
   - Loading spinner shown during request
   - On success, redirects to verification page

2. **Email Verification** (`resources/views/verify_email.blade.php`)
   - User receives 6-digit OTP via email
   - User enters OTP on verification page
   - Client-side validation occurs
   - AJAX request sent to verify the OTP
   - On success, redirects to registration page

3. **Registration** (`resources/views/session/register_start.blade.php`)
   - User completes account creation with verified email
   - Form includes password, name, and other required fields
   - On submission, account is created

## Key Components

### Controller Methods
- `UserController@sendOtp`: Generates and sends OTP
- `UserController@verifyEmail`: Verifies the submitted OTP
- `User<PERSON>ontroller@resendVerification`: Resends OTP if needed

### Routes
```php
Route::middleware('guest')->group(function () {
    Route::post('register/send-otp', [UserController::class, 'sendOtp'])->name('register.send-otp');
    Route::get('verify-email/{email}', [UserController::class, 'showVerifyEmail'])->name('email.verify.show');
    Route::post('verify-email', [UserController::class, 'verifyEmail'])->name('email.verify');
    Route::post('resend-verification', [UserController::class, 'resendVerification'])->name('email.resend');
});
```

### Session Variables
- `otp_email`: Stores the email address
- `otp_code`: Stores the generated OTP
- `otp_created_at`: Timestamp for OTP expiration (15 minutes)
- `email_verified`: Boolean flag for verification status
- `verified_email`: Stores the verified email address

### Email Templates
- `resources/views/emails/otp/verify.blade.php`: HTML email with OTP
- `resources/views/emails/otp/verify-text.blade.php`: Text version of OTP email

## JavaScript Implementation
The landing page uses AJAX to handle the form submission and redirect:

```javascript
fetch('{{ route("register.send-otp") }}', {
    method: 'POST',
    headers: {
        'X-CSRF-TOKEN': document.querySelector('input[name="_token"]').value,
        'Accept': 'application/json'
    },
    body: formData
})
.then(response => response.json())
.then(data => {
    if (data.success) {
        window.location.href = data.redirect;
    } else {
        showError(data.message);
    }
})
.catch(error => {
    showError(error.message);
});
```

## Error Handling
- Consistent error message styling across all pages
- Smooth fade-in animation for error messages
- Clear error messages for validation failures
- Proper handling of expired OTPs

## Notes for Future Reference
- OTP expires after 15 minutes
- OTP is 6 digits (generated with `sprintf('%06d', mt_rand(0, 999999))`)
- Email verification is required before account creation
- The flow uses session storage for OTP and verification status
