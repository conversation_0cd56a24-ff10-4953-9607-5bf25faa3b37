<PERSON><PERSON> removed
commit 24bea7daac1aa294337edcbd93e2078abac150ec (HEAD -> cc-new-look-1, origin/cc-new-look-1)
Author: <PERSON><PERSON><PERSON> <<EMAIL>>
Date:   Tue Apr 1 16:01:07 2025 +0600


Kernel Fixes
commit 96eb66f471060611e3f8c1054c233f6f9ed40788 (HEAD -> cc-new-look-1, origin/cc-new-look-1)
Author: <PERSON><PERSON><PERSON> <most<PERSON><EMAIL>>
Date:   Mon Mar 31 21:02:40 2025 +0600

Signup with Google SSO
commit 23b888d53da1ae86874476af21ac5e8a33cbf217 (HEAD -> cc-new-look-1)
Author: <PERSON><PERSON><PERSON> <most<PERSON><PERSON>@MB-Mac-Pro.local>
Date:   Sat Mar 29 23:19:31 2025 +0600
    
Email OTP signup process with sweet alert step 1, step 2, step 3 - stable
commit 3fe1a36e1681f89d5576da2c712c60ae409cda08 (HEAD -> cc-new-look-1)
Author: <PERSON><PERSON><PERSON> <<EMAIL>>
Date:   Sat Mar 29 17:59:50 2025 +0600

Email OTP signup process with sweet alert step 1, step 2, step 3
commit cb7242a6546171275cea71eaa67f5e2fd68a5bbe (HEAD -> cc-new-look-1)
Author: Mostanser Billah <<EMAIL>>
Date:   Sat Mar 29 17:56:08 2025 +0600

Email OTP signup process with sweet alert step 1, step 2
commit 07d0f1ecc1d071ee74516157b790cf8b270ed86b (HEAD -> cc-new-look-1)
Author: Mostanser Billah <<EMAIL>>
Date:   Sat Mar 29 17:47:40 2025 +0600

Traditional Registration with Email OTP - v1
commit 7d6667d7668205075b9e9fd2a4a6a7cf1471b670 (HEAD -> cc-new-look-1)
Author: Mostanser Billah <<EMAIL>>
Date:   Sat Mar 29 17:01:22 2025 +0600

v1 - stable version
commit 7679e7459a48012ba76bbbf033c2b6e3f62fc5a2 (HEAD -> cc-new-look-1)
Author: Mostanser Billah <<EMAIL>>
Date:   Sat Mar 22 22:07:12 2025 +0600
git reset --hard 7679e7459a48012ba76bbbf033c2b6e3f62fc5a2

Signup process including OTP and Desktop View
commit f2f68439c5ef633c94337285588a77162e737d5b (HEAD -> cc-new-look-1)
Author: Mostanser Billah <<EMAIL>>
Date:   Sat Mar 22 19:07:43 2025 +0600
git reset --hard f2f68439c5ef633c94337285588a77162e737d5b

