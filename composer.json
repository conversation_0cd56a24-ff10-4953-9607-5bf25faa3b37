{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.2", "google/recaptcha": "^1.2", "guzzlehttp/guzzle": "^7.8", "laravel/framework": "^11.44", "laravel/passport": "^12.0", "laravel/socialite": "^5.18", "laravel/tinker": "^2.9", "league/flysystem-aws-s3-v3": "^3.0", "monarobase/country-list": "^3.3", "pragmarx/google2fa": "^8.0", "predis/predis": "*", "realrashid/sweet-alert": "^7.3", "socialiteproviders/apple": "^5.6", "socialiteproviders/facebook": "^4.1", "socialiteproviders/manager": "^4.8", "socialiteproviders/microsoft": "^4.6", "spatie/laravel-html": "^3.11", "stripe/stripe-php": "^7.62", "symfony/http-client": "^7.2", "symfony/mailgun-mailer": "^7.2"}, "require-dev": {"barryvdh/laravel-ide-helper": "^3.0", "fakerphp/faker": "^1.23", "laravel/sail": "^1.39", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.0", "phpunit/phpunit": "^11.0.1"}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true}, "conflict": {"tightenco/collect": "*"}, "extra": {"laravel": {"dont-discover": ["tightenco/collect"]}}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "minimum-stability": "stable", "prefer-stable": true, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}}