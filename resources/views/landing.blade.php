@extends('layouts.modern')

@section('title', 'Welcome to Common Change')

@section('head')
<link rel="stylesheet" href="{{ asset('css/landing.css') }}">
@endsection

@section('content')
<!-- Signup Page -->
<div class="signup-page">
    <div class="signup-image"></div>
    <div class="signup-section">
        <div class="signup-logo">
            <img src="/images/logo.png" alt="Common Change Logo">
        </div>

        <h1>Welcome</h1>
        <p class="signup-instructions">
            <b>Create an account to begin.</b><br>
            First, enter your email.
        </p>

        <!-- Add error message container -->
        <div id="email-error" class="error-message"></div>

        <form method="POST" action="{{ route('register.send-otp') }}" id="email-form" novalidate>
            @csrf
            <input type="email" name="email" placeholder="Email address" required>
            <button type="submit" class="btn-primary">Next</button>
        </form>

        <div class="divider">OR</div>

        <div class="social-signup">
            <a href="{{ route('login.google') }}" class="social-btn">
                <img src="/images/google-icon.png" alt="Google Icon" class="social-icon">
                Continue with Google
            </a>

            <a href="{{ route('login.apple') }}" class="social-btn">
                <img src="/images/apple-icon.png" alt="Apple Icon" class="social-icon">
                Continue with Apple
            </a>

            <a href="{{ route('login.microsoft') }}" class="social-btn">
                <img src="/images/windows-icon.png" alt="Microsoft Icon" class="social-icon">
                Continue with Microsoft
            </a>

            <a href="{{ route('login.facebook') }}" class="social-btn">
                <img src="/images/facebook-icon.png" alt="Facebook Icon" class="social-icon">
                Continue with Facebook
            </a>
        </div>

        <div class="signup-footer">
            Already have an account? <a href="{{ route('auth.login') }}">Login</a>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const form = document.getElementById('email-form');

        form.addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(form);
            const submitButton = form.querySelector('button[type="submit"]');
            const email = form.querySelector('input[name="email"]').value;

            // Basic email validation
            if (!email || !email.includes('@') || !email.includes('.')) {
                Swal.fire({
                    icon: 'error',
                    title: 'Invalid Email',
                    text: 'Please enter a valid email address',
                    confirmButtonColor: '#3085d6'
                });
                return;
            }

            // Show loading state
            submitButton.disabled = true;
            submitButton.textContent = 'Sending...';

            // Show loading spinner with SweetAlert
            Swal.fire({
                title: 'Sending...',
                text: 'Please wait while we verify your email',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            // Get CSRF token safely
            const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '';

            const headers = {
                'Accept': 'application/json'
            };

            if (csrfToken) {
                headers['X-CSRF-TOKEN'] = csrfToken;
            }

            fetch(form.action, {
                method: 'POST',
                headers: headers,
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Email Sent',
                        text: 'We\'ve sent a verification code to your email',
                        confirmButtonColor: '#3085d6'
                    }).then(() => {
                        window.location.href = data.redirect;
                    });
                } else {
                    submitButton.disabled = false;
                    submitButton.textContent = 'Next';

                    // Handle duplicate email case
                    if (data.message && data.message.includes('already registered')) {
                        Swal.fire({
                            icon: 'info',
                            title: 'Account Exists',
                            text: 'This email is already registered. Would you like to login instead?',
                            showCancelButton: true,
                            confirmButtonText: 'Yes, login',
                            cancelButtonText: 'No, try another email',
                            confirmButtonColor: '#3085d6',
                            cancelButtonColor: '#d33'
                        }).then((result) => {
                            if (result.isConfirmed && data.redirect) {
                                window.location.href = data.redirect;
                            }
                        });
                    } else if (data.message && data.message.includes('deactivated')) {
                        Swal.fire({
                            icon: 'warning',
                            title: 'Account Deactivated',
                            text: 'This account has been deactivated. Please contact support for assistance.',
                            confirmButtonColor: '#3085d6'
                        });
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'Error',
                            text: data.message || 'An error occurred. Please try again.',
                            confirmButtonColor: '#3085d6'
                        });
                    }
                }
            })
            .catch(error => {
                console.error('Error:', error);
                submitButton.disabled = false;
                submitButton.textContent = 'Next';

                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: 'An unexpected error occurred. Please try again.',
                    confirmButtonColor: '#3085d6'
                });
            });
        });
    });
</script>
@endsection
