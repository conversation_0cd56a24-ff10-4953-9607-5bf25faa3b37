@extends('layouts.modern')

@section('title', 'Verify Email')

@section('content')
<!-- Signup Page -->
<div class="signup-page">
    <div class="signup-image" style="background-image: url('/images/landing-bg-1.png');"></div>
    <div class="signup-section">
        <div class="signup-logo">
            <img src="/images/logo.png" alt="Common Change Logo">
        </div>

        <h1>Check your email for a code.</h1>

        <div style="width: 60%; text-align: left;">
            <p class="instruction-text">
                We've sent a 6-character code to <span class="email-highlight">{{ $email }}</span>.
            </p>
            <p class="expiry-note">
                The code expires in 15 minutes, so enter it soon.
            </p>

            <p class="didnt-receive">
                Didn't receive the email? <a href="#" id="resend-link" class="try-again">Try again</a>
            </p>
        </div>

        <form id="verification-form">
            @csrf
            <div class="otp-container">
                <input type="text" maxlength="1" class="otp-input" name="code_1" id="code_1" autofocus>
                <input type="text" maxlength="1" class="otp-input" name="code_2" id="code_2">
                <input type="text" maxlength="1" class="otp-input" name="code_3" id="code_3">
                <input type="text" maxlength="1" class="otp-input" name="code_4" id="code_4">
                <input type="text" maxlength="1" class="otp-input" name="code_5" id="code_5">
                <input type="text" maxlength="1" class="otp-input" name="code_6" id="code_6">
            </div>

            <div id="error-message" class="error-message" style="display: none;"></div>
            <div id="loading-spinner" class="loading-spinner" style="display: none;">
                <div class="spinner"></div>
            </div>

            <button type="submit" class="btn-primary">Next</button>
        </form>
    </div>
</div>
@endsection

@section('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('verification-form');
    const errorElement = document.getElementById('error-message');
    const loadingSpinner = document.getElementById('loading-spinner');
    const resendLink = document.getElementById('resend-link');
    const otpInputs = document.querySelectorAll('.otp-input');

    // Auto-focus next input when a digit is entered
    otpInputs.forEach((input, index) => {
        input.addEventListener('input', function() {
            if (this.value.length === this.maxLength) {
                if (index < otpInputs.length - 1) {
                    otpInputs[index + 1].focus();
                }
            }
        });

        // Handle backspace to go to previous input
        input.addEventListener('keydown', function(e) {
            if (e.key === 'Backspace' && this.value.length === 0 && index > 0) {
                otpInputs[index - 1].focus();
            }
        });
    });

    // Handle form submission
    form.addEventListener('submit', function(e) {
        e.preventDefault();

        // Collect OTP from inputs
        let otp = '';
        otpInputs.forEach(input => {
            otp += input.value;
        });

        // Validate OTP
        if (otp.length !== 6 || !/^\d+$/.test(otp)) {
            Swal.fire({
                icon: 'error',
                title: 'Invalid Code',
                text: 'Please enter a valid 6-digit code.',
                confirmButtonColor: '#3085d6'
            });
            return;
        }

        // Show loading spinner
        Swal.fire({
            title: 'Verifying...',
            text: 'Please wait while we verify your code',
            allowOutsideClick: false,
            didOpen: () => {
                Swal.showLoading();
            }
        });

        fetch('{{ route("email.verify") }}', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('input[name="_token"]').value,
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            body: JSON.stringify({ code: otp })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Redirect immediately to the email confirmation screen without showing success message
                window.location.href = data.redirect;
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Verification Failed',
                    text: data.message || 'Failed to verify your email. Please try again.',
                    confirmButtonColor: '#3085d6'
                });
            }
        })
        .catch(error => {
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: 'An unexpected error occurred. Please try again.',
                confirmButtonColor: '#3085d6'
            });
        });
    });

    // Handle resend link
    resendLink.addEventListener('click', function(e) {
        e.preventDefault();

        resendLink.textContent = 'Sending...';

        fetch('{{ route("email.resend") }}', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('input[name="_token"]').value,
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            body: JSON.stringify({})
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                resendLink.textContent = 'Code sent!';
                setTimeout(() => {
                    resendLink.textContent = 'Try again';
                }, 3000);
            } else {
                resendLink.textContent = 'Failed to send';
                setTimeout(() => {
                    resendLink.textContent = 'Try again';
                }, 3000);
            }
        })
        .catch(error => {
            resendLink.textContent = 'Error';
            setTimeout(() => {
                resendLink.textContent = 'Try again';
            }, 3000);
        });
    });

    function showError(message) {
        // Clear existing errors
        errorElement.textContent = '';

        // Set new error message
        errorElement.textContent = message || 'An error occurred. Please try again.';

        // Show with fade-in effect
        errorElement.style.opacity = '0';
        errorElement.style.display = 'block';

        setTimeout(() => {
            errorElement.style.transition = 'opacity 0.3s ease';
            errorElement.style.opacity = '1';
        }, 10);
    }
});
</script>
@endsection
