@extends('layouts.email_text')

@section('content')
Results for '{{ $proposal->issue->title }}':

@if($proposal->vote_passed)
@if($proposal->creator_id == $member->id)
Your request has been recommended!  We now need you to fill out payment information for this request.
Please use the following URL to enter the payment details:
{{ URL::route('issue.edit',array('issue'=>$proposal->issue->id)) }}#payment_info
@else
The group recommends a gift to be shared for the request!
@endif
@else
The group has not put forth a recommendation for the request. Please read comments to online to understand why this might be.
@endif

Use the following URL to view the request details on CommonChange.com:
{{ URL::route('issue.show',array('issue'=>$proposal->issue->id)) }}
@stop
