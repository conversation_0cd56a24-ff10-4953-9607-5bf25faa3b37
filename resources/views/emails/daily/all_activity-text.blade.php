@extends('layouts.email_text')

@section('content')
There has been activity in your group(s) in the last 24 hours:

@foreach($user_data->groups as $group_data)
-----
{{ $group_data->group->name }}

@if(count($group_data->unvoted_proposals))
REQUESTS THAT NEED YOUR ATTENTION:

@foreach($group_data->unvoted_proposals as $proposal)
	{{ $proposal->issue->title }} - {{ $proposal->issue->creator->firstname }} {{ $proposal->issue->creator->lastname }}
	{{ URL::route('issue.show',$proposal->issue->id) }}

@endforeach
@endif
@if(isset($group_data->new_discussions))
{{ $group_data->new_discussions->rendered_activity_text }}
@endif
@endforeach
@stop