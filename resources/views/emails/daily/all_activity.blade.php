@extends('layouts.email')

@section('content')
<p>There has been activity in your group(s) in the last 24 hours:</p>

@foreach($user_data->groups as $group_data)
<div style="margin-bottom:10px;">
	<h3 style="margin:30px 0px 5px;">{{ $group_data->group->name }}</h3>
	<div style="margin-left:20px;">
	@if(count($group_data->unvoted_proposals))
		<h4 style="margin:10px 0px 5px;">REQUESTS THAT NEED YOUR ATTENTION:</h4>
		<div>
		@foreach($group_data->unvoted_proposals as $proposal)
			<a href="{{ URL::route('issue.show',$proposal->issue->id) }}">{{ $proposal->issue->title }}</a>
			&middot; {{ $proposal->issue->creator->firstname }} {{ $proposal->issue->creator->lastname }}<br />
		@endforeach
		</div>
	@endif
	</div>
	@if(isset($group_data->new_discussions))
	{!! $group_data->new_discussions->rendered_activity !!}
	@endif
</div>
@endforeach

@stop
