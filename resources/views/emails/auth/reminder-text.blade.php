@extends('layouts.email_text')

@section('content')
A password reset has been requested for your account on CommonChange.

If you didn't request a password request, you can ignore this email and your password won't change.

Otherwise, use the following URL to continue with the password reset process:
{{ URL::to('password/reset', array($token)) }}

This URL will expire in {{ config('auth.reminder.expire', 60) }} minutes.
@stop