@extends('layouts.email')

@section('content')
<h3>Results for <strong>{{ $proposal->issue->title }}</strong>:</h3>
	@if($proposal->vote_passed)
		@if($proposal->creator_id == $member->id)
			<p>Your request has been approved!  We now need you to fill out payment information for this request.</p>
			<p>Please <a href="{{ URL::route('issue.edit',array('issue'=>$proposal->issue->id)) }}#payment_info">click here</a> to enter the payment details.</p>
		@else
			The group recommends a gift to be shared for the request!
		@endif
	@else
		The group has not put forth a recommendation for the request. Please read comments to online to understand why this might be.
	@endif
</p>
<p>
	<a href="{{ URL::route('issue.show',array('issue'=>$proposal->issue->id)) }}"><strong>Click here</strong</a>
	to view the request details on CommonChange.com.
</p>
@stop