@extends('layouts.master')

@section('title')
	All Groups
@stop

@section('head')
<link type="text/css" rel="stylesheet" href="{{ asset('/css/jqueryui.css?version=1') }}" media="all">
<link type="text/css" rel="stylesheet" href="{{ asset('/css/welcomenav.css?version=2') }}" media="all">
<link type="text/css" rel="stylesheet" href="{{ asset('/css/selectboxit.css?version=1') }}" media="all">
@stop

@section('content')
<section id="main-bg">
	<div class="container  no-padding">
		<div class="col-lg-12 col-sm-12 col-xs-12 no-padding margin-padding ">
		<div class="col-lg-8 col-sm-6 col-xs-12 allgroup-contain "> <span>ALL GROUPS</span> </div>
		<div class="col-lg-4 col-sm-6 col-xs-12 no-padding new-radius centerposition ">
		{{ html()->modelForm($input, 'GET', route('group.index'))->class('form-horizontal margin-mobile')->attribute('name', 'group')->open() }}
			<div class="col-lg-6 col-sm-6 col-xs-12 pull-left ">
			<!-- <div class="ui-widget"> -->
				<!--
				<select name="sort" id="combobox">
					<option value="">Select one...</option>
					<option value="option2"> Default</option>
					<option value="option3">Alphabetical (A-Z)</option>
					<option value="option4">Alphabetical (Z-A)</option>
					<option value="option5">State</option>
					<option value="option5">Country</option>
					<option value="option5">Recent (??)</option>
				</select>
				-->
				{{ html()->select('sort', array('alpha' => 'Alphabetical (A-Z)', 'revalpha' => 'Alphabetical (Z-A)', 'state' => 'State', 'country' => 'Country'))->attribute('onchange', 'document.group.submit()') }}
			<!-- </div> -->
			</div>
			<div class="col-lg-6 col-sm-6 col-xs-12 no-padding pull-right  ">
				<div class="input-group">
				  <!-- <input type="text" class="form-control cust-form1" placeholder="" name=""> -->
				  {{ html()->text('search')->class('form-control cust-form1')->placeholder('Search') }}
				  <div class="input-group-btn">
					<button class="btn btn-success btn-success3" type="submit"><i class="glyphicon glyphicon-search"></i></button>
				  </div>
				</div>
			</div>
		</div>
		</div>
	</div>

		<div class="container group-list no-padding clearfix">
		{{ $pGroups->links() }}
		@forelse ($groups as $duple)
			<!-- <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 group-description signup no-padding people-margindbottom"> -->
			<div class="row">
			@foreach ($duple as $group)
				<div class="col-sm-6 col-xs-12 clearfix padding">
				@include('groups.small_pane')
				</div>
			@endforeach
			</div>
		@empty
			<p>No groups to display.</p>
		@endforelse
		</div>
</section>
@stop

@section('scripts')
<script src="{{ asset('/js/bootstrap-toggle.js') }}"></script>
<script src="{{ asset('/js/jquery-ui.js') }}"></script>
<script src="{{ asset('/js/selectBoxIt.js') }}"></script>
<script>
	console.log('did it');
	$("select").selectBoxIt({
		autoWidth: false,
	});
</script>
@stop