@extends('layouts.master')
@inject('locationHelper', 'App\Models\LocationHelper')
@inject('navBar', 'App\Models\NavBar')

@section('title')
	My Groups
@stop

@section('head')
	<link type="text/css" rel="stylesheet" href="{{ asset('/css/dark1.css?version=1') }}" media="all">
	<link type="text/css" rel="stylesheet" href="{{ asset('/css/welcomenav.css?version=2') }}" media="all">
	<link type="text/css" rel="stylesheet" href="{{ asset('/css/selectboxit.css?version=1') }}" media="all">
	{{ $navBar->setActive('mygroups') }}
	<style>
		.group-banner {
			width: 100%;
			height: 150px;
			background-size: cover;
			background-position: center;
			border-radius: 8px;
			margin-bottom: 16px;
		}
	</style>
@stop

@section('home-content')
	<div class="col-sm-6 col-xs-12 margin-padding">
		<span style="margin:0px">MY GROUPS</span>
	</div>
	<div class="col-sm-6 col-xs-12 margin-padding">
		<div class="pull-right">
			<a href="{{ URL::route('group.create') }}" class="btn btn-success good_spacing" role="button">
				<i class="glyphicon glyphicon-plus"></i> CREATE A NEW GROUP</a>
			<a href="{{ URL::route('group.index') }}" class="btn btn-success good_spacing" role="button">
				<i class="glyphicon glyphicon-search"></i> FIND A GROUP</a>
		</div>
	</div>
@stop

@section('content')
	<!--main contain part -->
	@forelse ($groups as $group)
		<div class="col-xs-12 contain-margin no-padding">
			<div class="shared-people welcome-content clearfix">
				<div class="group-banner" style="background-image: url('{{ $group->group_banner_image ? asset('storage/' . $group->group_banner_image) : asset('images/group-placeholder.jpg') }}');"></div>
				<div class="col-lg-12 col-sm-12 no-padding">
					<div class="col-sm-5 col-xs-12 no-padding">
						<h2>{{ $group->name }}</h2>
					</div>
					<div class="col-sm-7 col-xs-12 no-padding">
						<div class="col-xs-12 col-md-4 pull-left no-padding">
							@if($group->id != 1 && isset($group->pivot))
								{{ html()->form('PUT', route('group.update_user', [$group->id, Auth::User()->id]))->attribute('name', 'group' . $group->id)->open() }}
									{{ html()->select('email_notification', array('use_default' => "Use my default email setting", 'per_comment' => 'One email per comment', 'daily_per_discussion' => 'Daily Summary per Discussion', 'daily_digest' => 'Add to Daily Digest', 'none' => "Don't send email updates"), $group->pivot->email_notification)->attribute('onchange', 'document.group' . $group->id . '.submit()') }}
								{{ html()->form()->close() }}
							@endif
						</div>
						<div class="col-xs-12 col-md-8 no-padding"><div class="pull-right">
							@if($group->suspended)
								<a title="Group Suspended" class="btn btn-cancel big-button img-margin" role="button">GROUP SUSPENDED</a>
							@elseif($group->isInvited(Auth::User()))
								<a href="{{ URL::route('group.show', $group->id) }}" title="View Group" class="btn btn-success big-button img-margin" role="button"><img src="/images/search.png">VIEW INVITATION</a>
							@else
								@if(($group->canAdmin(Auth::User())))
									<a href="{{ URL::route('group.admin', $group->id) }}" title="Administer Group" class="btn btn-success" role="button">
										<i class="glyphicon glyphicon-cog"></i> ADMIN</a>&nbsp;&nbsp;
								@endif
								@if($group->id != Auth::User()->default_group_id)
									<a href="{{ URL::route('user.set_default_group', $group->id) }}" title="Set as your default group" class="btn btn-success" role="button">
										<i class="glyphicon glyphicon-bookmark"></i> SET DEFAULT</a>&nbsp;&nbsp;
								@endif
								<a href="{{ URL::route('group.show', $group->id) }}" title="View Group" class="btn btn-success" role="button">
									<i class="glyphicon glyphicon-list-alt"></i> DETAILS</a>
							@endif
						</div></div>
					</div>
				</div>
				<div class="col-xs-12  group-text group-margin1  no-padding">
					<p>
					<span>Location:</span>&nbsp;{{ $group->city }},
						@if($group->state != 'zzzz')
							{{ $locationHelper->subdivisionName($group->state) }},
						@endif
						{{ $locationHelper->countryName($group->country) }}&nbsp;&nbsp;&nbsp;
					<span>Members:</span>&nbsp;{{ $group->members->count() }}&nbsp;&nbsp;&nbsp;
					<span>Discussions:</span>&nbsp;{{ $group->discussions->count() }}&nbsp;&nbsp;&nbsp;
					<span>Issues:</span>&nbsp;{{ $group->issues->count() }}&nbsp;&nbsp;&nbsp;
					</p>
				</div>
				@include('groups.financial_info')
				@include('groups.members')
			</div>
		</div>
	@empty
		<p>No groups to display.</p>
	@endforelse
@stop

@section('scripts')
	<script src="{{ asset('/js/jquery-ui.js') }}"></script>
	<script>
		$(document).ready(function() {
			$(".basic-example").heapbox();
		});
	</script>
	<script src="{{ asset('/js/bootstrap-toggle.js') }}"></script>
	<script src="{{ asset('/js/jquery.heapbox-0.9.4.js') }}"></script>
	<script src="{{ asset('/js/selectBoxIt.js') }}"></script>
	<script>
		$("select").selectBoxIt({
			autoWidth: false
		});
	</script>
@stop