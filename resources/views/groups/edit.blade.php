@extends('layouts.no_container')

@section('title')
	Change Group Settings
@stop

@section('head')
	@include('groups.create_head')
	<link type="text/css" rel="stylesheet" href="{{ asset('/css/welcomenav.css?version=2') }}" media="all">
@stop

@section('home-content')
	<span></span>
@stop

@section('content')
<section id="main-bg">
<div class="container welcome-contentpart no-padding"> <span>GROUP ADMIN</span> </div>
@include('groups.bar',array('current_page'=>'details'))
<div class="container no-padding">

<div class="col-lg-12 welcome-content group-font clearfix ">
  <h4>Edit settings for {{ $group->name }}</h4>
  
  @if(Session::get('success',false))
	<p>Group successfully updated!</p>
  @endif

  <!-- start form part -->
  {{ html()->modelForm($group, 'PUT', route('group.update', $group->id))->class('form-horizontal cust-control')->attribute('name', 'group')->open() }}
  @include('groups.form')
  <div class="form-group cust-group but-padding">
    <div class="col-sm-offset-3 col-sm-3 col-xs-12">
      <button type="submit" class="btn btn-success big-button">SAVE CHANGES</button>
    </div>
  </div>
<!-- end Submit buttons --> 
  {{ html()->closeModelForm() }}
	   <!-- end of form part  --> 
</div>
</div>
</section>

  
<!-- end edit group detail contain --> 
@stop

@section('scripts')
	@include('groups.create_scripts')
@stop