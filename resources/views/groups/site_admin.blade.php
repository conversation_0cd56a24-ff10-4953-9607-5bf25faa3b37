@extends('layouts.master')

@section('title')
	Group Admin
@stop

@section('head')
@stop

@section('content')
<section id="main-bg">
<div class="container welcome-contentpart mobile-padd no-padding"> <span>GROUP ADMIN</span> </div>
@include('groups.bar',array('current_page'=>'site_admin'))
<div class="container no-padding">
<div class="col-lg-12 welcome-content contain-margin">
	<h2>{{ $group->name }}</h2>
	<hr>
	<div class="admin-panel clearfix">
		<h3>Edit details:</h3>
		{{ html()->modelForm($group, 'PUT', route('group.admin_update', $group->id))->class('form-inline')->open() }}
			<div class="form-group">
				<label for="current_funds">Current Funds:</label>
				{{ html()->text('current_funds')->id('current_funds')->class('form-control') }}&nbsp;&nbsp;
			</div>
			<div class="form-group">
				<label for="shared_funds">Funds Shared:</label>
				{{ html()->text('shared_funds')->id('shared_funds')->class('form-control') }}&nbsp;&nbsp;
			</div>
			<div class="checkbox">
				<label>
					{{ html()->checkbox('suspended', null, 'yes') }}
					Group Suspended&nbsp;&nbsp;
				</label>
			</div>
			<div class="form-group">
				<button type="submit" class="btn btn-success">SUBMIT CHANGES</button>
			</div>
		{{ html()->closeModelForm() }}
	</div>
	</div>

</div>
</section>
@stop

@section('scripts')
	@include('groups.create_scripts')
@stop