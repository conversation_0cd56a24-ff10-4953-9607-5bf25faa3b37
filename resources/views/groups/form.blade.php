@inject('locationHelper', 'App\Models\LocationHelper')
<div class="form-group cust-group">
  <label for="input" class="col-sm-3 control-label"> <span>Group Name:</span></label>
  <div class="col-sm-4">
	<!-- <input type="text" class="form-control cust-form" id="input" placeholder=""> -->
	@error('name')
		<span class="error">{{ $message }}</span>
	@enderror
	{{ html()->text('name')->class('form-control cust-form')->id('input')->placeholder('Group name') }}
  </div>
</div>
<div class="form-group cust-group">
  <label for="input" class="col-sm-3 control-label"> <span>Short Name (alphanumeric):</span></label>
  <div class="col-sm-4">
	<!-- <input type="text" class="form-control cust-form" id="input" placeholder=""> -->
	@error('shortname')
		<span class="error">{{ $message }}</span>
	@enderror
	{{ html()->text('shortname')->class('form-control cust-form')->id('input')->placeholder('Short name') }}
  </div>
</div>
<div class="form-group cust-group">
  <label for="input" class="col-sm-3 control-label"> <span>Your Location City:</span></label>
  <div class="col-sm-4">
	<!-- <input type="text" class="form-control cust-form" id="input" placeholder=""> -->
	@error('city')
		<span class="error">{{ $message }}</span>
	@enderror
	{{ html()->text('city')->class('form-control cust-form')->id('input')->placeholder('City') }}
  </div>
</div>
<div class="form-group cust-group">
  <label for="input" class="col-sm-3 control-label"> <span>Your Location State:</span> </label>
  <div class="col-sm-4">
	<!--
	<select class="basic-example">
	  <option value="option1">None</option>
	  <option value="option2">None</option>
	  <option value="option1">None</option>
	</select>
	-->
	@error('state')
		<span class="error">{{ $message }}</span>
	@enderror
	{{ html()->select('state', $locationHelper->subdivisionsForSelect('US'))->id('combobox') }}
  </div>
</div>
<div class="form-group cust-group">
  <label for="input" class="col-sm-3 control-label"> <span>Your Location Country:</span> </label>
  <div class="col-sm-4">
	<!--
	<select class="basic-example">
	  <option value="option1"> USA</option>
	  <option value="option2"> USA</option>
	  <option value="option1"> USA</option>
	</select>
	-->
	@error('country')
		<span class="error">{{ $message }}</span>
	@enderror
	{{ html()->select('country', $locationHelper->countriesForSelect())->id('combobox1') }}
  </div>
</div>
<div class="form-group cust-group">
  <label for="input" class="col-sm-3 control-label"><span>Group Description:</span></label>
  <div class="col-sm-7">
	<!--
	<textarea class="form-control cust-form" rows="4"
	 placeholder="Is your group based on geographic proximity, a shared interest, or an existing  relational connection or community? If so, be sure to include that your description."></textarea>
	 -->
	 @error('description')
		<span class="error">{{ $message }}</span>
	@enderror
	 {{ html()->textarea('description')->rows('4')->class('form-control cust-form')->placeholder('Is your group based on geographic proximity, a shared interest, or an existing relational connection or community? If so, be sure to include that in your description. What do you hope to accomplish, what is your purpose, who can join?') }}
  </div>
</div>
@if($isNew ?? false)
	<div class=" col-lg-7 col-sm-7 col-sm-offset-3 col-lg-offset-3 inner-padding ">
	<div class=" col-lg-12 col-md-12 col-xs-12 col-sm-12  text-inner ">
	  <p><span>IMPORTANT NOTE: </span> Groups cannot limit their purpose to the support of a single individual (e.g. fundraising for a single person with cancer,                                             fundraising for a trip, etc.) Many other crowd-funding platforms are already available for this type of fundraising. Common Change groups are to be used                                             to pool resources which are then distributed to a variety of needy individuals (not organizations) based on the collective input of group members.
	  <a href="https://www.commonchange.com/wp-content/uploads/2014/02/Group-Fund-Policy-v041514.pdf" target="_blank">Read the Full Group Fund Policy document </a></p>
	  <table>
		<tbody>
		  <tr>
			<td><input type="checkbox" name="checkboxG4" id="checkboxG4" class="css-checkbox">
			  <label for="checkboxG4" class="css-label font">As group facilitator, I understand and agree to comply with this requirement.</label></td>
		  </tr>
		</tbody>
	  </table>
	</div>
	</div>
@endif
<div class="clearfix"></div>
<div class="form-group cust-group">
  <label for="input" class="col-sm-3 control-label infoimg">
  <span>Member Covenant:<a href="#" data-toggle="modal" data-target=".bs-example-modal-sm"><img src="/images/infoicon.png"></a> </span></label>
  <div class="modal fade bs-example-modal-sm" tabindex="-1" role="dialog" aria-labelledby="mySmallModalLabel" aria-hidden="true">
	<div class="modal-dialog modal-sm">
	  <div class="modal-content">
	<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
		<div class="shared-people welcome-content  clearfix"> <span>
		  <blockquote>NOTE</blockquote>
		  </span>
		  <!-- start form sign in part form-->
		  <P>Describe the expectations for group member participation and commitment. This is also the place to describe your vision for the group's giving, any                                             particular missions or limitations (e.g. the group supports particular geographically based needs), and what you hope to achieve as a group.  We've                                                included some typical covenants as a starting place. Feel free to change these as you wish.</P>
		  <!-- end of sign up part form-->

		</div>
	  </div>
	  </div>
	</div>
	<div class="col-sm-7">
	<!--
	<textarea class="form-control cust-form" rows="13"
	 placeholder="We share the hope of a transformation in ourselves and our communities by sharing our resources to feed the hungry, clothe the naked, shelter the homeless, heal the sick, and visit the imprisoned. We commit to honestly sharing the needs of our community and those with whom we are in relationship. We commit to active participation in the group by bringing needs, weighing in on discussions, making regular contributions, and bringing our talents and gifts to the support of those in need. We commit to making an effort to create ongoing relationships across lines of class, race and geography, and to building reconciliation through these relationships. We invite each other to encourage and stretch our understanding and practice of collaborative, relational giving. Our collective participation helps make the voice of a new way be heard throughout the country and the globe."></textarea>
	 -->
	 @error('covenant')
		<span class="error">{{ $message }}</span>
	@enderror
	 {{ html()->textarea('covenant')->rows('13')->class('form-control cust-form')->placeholder('We share the hope of a transformation in ourselves and our communities by sharing our resources to feed the hungry, clothe the naked, shelter the homeless, heal the sick, and visit the imprisoned. We commit to honestly sharing the needs of our community and those with whom we are in relationship. We commit to active participation in the group by bringing needs, weighing in on discussions, making regular contributions, and bringing our talents and gifts to the support of those in need. We commit to making an effort to create ongoing relationships across lines of class, race and geography, and to building reconciliation through these relationships. We invite each other to encourage and stretch our understanding and practice of collaborative, relational giving. Our collective participation helps make the voice of a new way be heard throughout the country and the globe.') }}
  </div>
</div>
<div class="form-group cust-group">
	<label for="input" class="col-sm-3 control-label"> <span>Privacy Settings:</span> </label>
	<div class="col-sm-7 privancy-text">
		<p> Set the privacy level of your group now.  <span>Public Group </span>can be viewed and anyone
		who agrees with your member covenant can join. <span>Private groups</span> can be viewed,
		but users need to contact you to request to join. <span>Secret groups</span> are completely
		private, unlisted, and can only be joined by invitation.
		</p>
	</div>
	<div class="col-sm-offset-3 col-sm-4">
		<!--
		<select class="basic-example cust-head">
		<option value="option1">Public</option>
		<option value="option2">Private</option>
		<option value="option1">Secret</option>
		</select>
		-->
		@error('type')
			<span class="error">{{ $message }}</span>
		@enderror
		{{ html()->select('type', array(0 => 'Please Select', 'public' => 'Public', 'private' => 'Private', 'secret' => 'Secret'))->id('combobox2') }}
	</div>
</div>
<div class="form-group cust-group">
  <label for="input" class="col-sm-3 control-label"> <span>Who Can Invite
	New Users:</span> </label>
  <div class="col-sm-4">
	<!--
	<select class="basic-example">
	  <option value="option1">Select Who Can Join This Group</option>
	  <option value="option2">Only I can invite new members</option>
	  <option value="option1">Administrators can invite new members</option>
	  <option value="option1">Any member can invite</option>
	</select>
	-->
	@error('inviter')
		<span class="error">{{ $message }}</span>
	@enderror
	{{ html()->select('inviter', array(0 => 'Please Select', 'owner' => 'Only I can invite new members', 'admin' => 'Administrators can invite new members', 'member' => 'Any member can invite'))->id('combobox3') }}
  </div>
</div>
<div class="form-group cust-group">
  <label for="input" class="col-sm-3 control-label"> <span>Minimum donation:</span></label>
  <div class="col-sm-4">
	<!-- <input type="text" class="form-control cust-form" id="input" placeholder="$25"> -->
	@error('min_donation')
		<span class="error">{{ $message }}</span>
	@enderror
	{{ html()->text('min_donation')->class('form-control cust-form')->id('input')->placeholder('$25') }}
  </div>
</div>
<div class="form-group cust-group">
  <label for="input" class="col-sm-3 control-label"> <span>Donation frequency:</span> </label>
  <div class="col-sm-4 cust-head">
	<!--
	<select class="basic-example">
		<option value="option1">Weekly</option>
		<option value="option2">Bi-weekly</option>
		<option value="option2">Monthly</option>
		<option value="option3">One-time</option>
		<option value="option4">Annually</option>
		<option value="option4">Up to Member</option>
	</select>
	-->
	@error('donation_frequency')
		<span class="error">{{ $message }}</span>
	@enderror
	{{ html()->select('donation_frequency', array(0 => 'Please Select', 'weekly' => 'Weekly', 'biweekly' => 'Bi-weekly', 'monthly' => 'Monthly', 'one-time' => 'One-time', 'annually' => 'Annually', 'member-choice' => 'Up to Member'))->id('combobox4') }}
  </div>
</div>
