@inject('locationHelper', 'App\Models\LocationHelper')
<div class="shared-people welcome-content clearfix group-pane">
	<div class="group-banner" style="width: 100%; height: 150px; background-size: cover; background-position: center; border-radius: 8px; margin-bottom: 16px; background-image: url('{{ $group->group_banner_image ? asset('storage/' . $group->group_banner_image) : asset('images/group-placeholder.jpg') }}');"></div>
	<h2>{{ $group->name }}</h2>
	<div class="col-lg-5 col-md-5 col-sm-6 col-xs-6 no-padding">
		@if(isset($wizard))
			<a href="{{ URL::route('group.show', array('group'=>$group->id,'wizard'=>1)) }}" title="" class="btn btn-success big-button" role="button">
		@else
			<a href="{{ URL::route('group.show', array('group'=>$group->id)) }}" title="" class="btn btn-success big-button" role="button">
		@endif
			<img src="/images/search.png">
			@if ($group->isInvited(Auth::User()))
				VIEW INVITE
			@elseif ($group->type == 'public')
				VIEW PUBLIC GROUP
			@elseif ($group->type == 'private')
				VIEW PRIVATE GROUP
			@elseif ($group->type == 'secret')
				VIEW SECRET GROUP
			@endif
		</a>
	</div>
	<div class="col-lg-12 col-md-12  col-sm-12 col-xs-12  group-text group-margin  no-padding clearfix">
		<p><span>Location:</span>
			{{ $group->city }},
			@if($group->state != 'zzzz')
				{{ $locationHelper->subdivisionName($group->state) }},
			@endif
			{{ $locationHelper->countryName($group->country) }}<br>
		<span>Members: </span>{{ $group->members->count() }}</p>
		<p><span> Description:</span> {{ Str::limit($group->description,190) }}</p>
	</div>
	<!-- img part -->
	<div class="col-xs-12 no-padding memberpix">
	@foreach ((($group->members->count() > 18) ? $group->members->shuffle()->sort(function($a,$b) { return App\Models\User::picSort($a,$b); })->slice(0,17) : $group->members->shuffle()) as $member)
		<span title="{{$member->firstname}} {{$member->lastname}}" class="mediumpix" style="background-image: url('{{ $member->profile_pic }}')"></span>
	@endforeach
	@if($group->members->count() > 18)
		<span title="plus {{ $group->members->count() - 17 }} more members"class="mediumpix" style="background-image: url('/pix/profile-placeholder.png')"><div><h2>+{{ $group->members->count() - 17 }}</h2></div></span>
	@endif
	</div>
</div>
