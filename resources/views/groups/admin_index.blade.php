@extends('layouts.master')
@inject('locationHelper', 'App\Models\LocationHelper')

@section('title')
	Admin: Groups
@stop

@section('head')
<link type="text/css" rel="stylesheet" href="{{ asset('/css/jqueryui.css?version=1') }}" media="all">
<link type="text/css" rel="stylesheet" href="{{ asset('/css/welcomenav.css?version=2') }}" media="all">
<link type="text/css" rel="stylesheet" href="{{ asset('/css/selectboxit.css?version=1') }}" media="all">
@stop

@section('content')
<section id="main-bg">
	<div class="container  no-padding">
		<div class="col-lg-12 col-sm-12 col-xs-12 no-padding margin-padding ">
		<div class="col-xs-12">
			@include('admin.bar',array('current_page'=>'groups'))
		</div>
		<div class="col-lg-8 col-sm-6 col-xs-12 allgroup-contain "> <span>GROUPS</span> </div>
		<div class="col-lg-4 col-sm-6 col-xs-12 no-padding new-radius centerposition ">
		{{ html()->modelForm($input, 'GET', route('group.admin_index'))->class('form-horizontal margin-mobile')->attribute('name', 'group')->open() }}
			<div class="col-lg-6 col-sm-6 col-xs-12 pull-left ">
			<!-- <div class="ui-widget"> -->
				<!--
				<select name="sort" id="combobox">
					<option value="">Select one...</option>
					<option value="option2"> Default</option>
					<option value="option3">Alphabetical (A-Z)</option>
					<option value="option4">Alphabetical (Z-A)</option>
					<option value="option5">State</option>
					<option value="option5">Country</option>
					<option value="option5">Recent (??)</option>
				</select>
				-->
				{{ html()->select('sort', array('alpha' => 'Alphabetical (A-Z)', 'revalpha' => 'Alphabetical (Z-A)', 'state' => 'State', 'country' => 'Country'))->attribute('onchange', 'document.group.submit()') }}
			<!-- </div> -->
			</div>
			<div class="col-lg-6 col-sm-6 col-xs-12 no-padding pull-right  ">
				<div class="input-group">
				  <!-- <input type="text" class="form-control cust-form1" placeholder="" name=""> -->
				  {{ html()->text('search')->class('form-control cust-form1')->placeholder('Search') }}
				  <div class="input-group-btn">
					<button class="btn btn-success btn-success3" type="submit"><i class="glyphicon glyphicon-search"></i></button>
				  </div>
				</div>
			</div>
		{{ html()->closeModelForm() }}
		</div>
		</div>
	</div>

	<div class="container welcome-content no-padding">
		<div class="col-xs-12 clearfix">{{ $groups->links() }}</div>
		<div class="col-xs-12 discuss-container no-padding">
		<!--main contain part -->
		<div class="col-lg-12 col-sm-12 mobilescroll col-xs-12 no-padding">
			<table class="table table-responsive table-condensed">
				<thead class="tdclass">
					<tr >
						<th>NAME</th>
						<th>COUNTRY</th>
						<th>LAST ACTIVITY</th>
						<th style="text-align:right">MEMBERS</th>
						<th style="text-align:right">BALANCE</th>
						<th style="text-align:right">SHARED AMOUNT</th>
						<th></th>
					</tr>
				</thead>
				<tbody>

				@forelse($groups as $group)
					<tr>
						<td>@if($group->type == 'private')
						    	<span class="label label-warning">Pr</span>
					    	@elseif($group->type == 'secret')
						    	<span class="label label-danger">Se</span>
					    	@else
					    		<span class="label label-success">Pu</span>
					    	@endif
							<a href="{{ URL::route('group.site_admin',array('id'=>$group->id)) }}" target="cc_group_admin">{{ $group->name }}</a>

						    </td>
						<td><span title="{{ $locationHelper->countryName($group->country) }}">{{ $group->country }}</span></td>
						<td>
							@if($group->discussions()->count())
								{{ $group->discussions()->orderBy('updated_at','desc')->first()->updated_at->diffForHumans() }}</td>
							@else
								No activity yet.
							@endif
						<td style="text-align:right">{{ $group->members()->count() }}</td>
						<td style="text-align:right">{{ $group->current_funds }}</td>
						<td style="text-align:right">{{ $group->shared_funds }}</td>
						<td>
							<a href="{{ URL::route('group.toggle_suspended',array('id'=>$group->id)) }}" title="Toggle suspended status">
								@if($group->suspended)
									<span style="color:red" title="Reactivate group"><i class="glyphicon glyphicon-minus-sign"></i></span>
								@else
									<span style="color:green"  title="Suspend group"><i class="glyphicon glyphicon-ok-sign"></i></span>
								@endif
							</a>
						</td>
				@empty
					<tr><td colspan="6">No groups have been created.  How are you even seeing this message...</td></tr>
				@endforelse

				</tbody>
			</table>
			</container>
			<div class="clearfix"></div>
		</div>
		</div>
	</div>
</section>
@stop

@section('scripts')
<script src="{{ asset('/js/bootstrap-toggle.js') }}"></script>
<script src="{{ asset('/js/jquery-ui.js') }}"></script>
<script src="{{ asset('/js/selectBoxIt.js') }}"></script>
<script>
	console.log('did it');
	$("select").selectBoxIt({
		autoWidth: false,
	});
</script>
@stop