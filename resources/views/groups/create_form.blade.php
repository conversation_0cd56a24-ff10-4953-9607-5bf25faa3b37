<div class="container no-padding">
	<div class="col-lg-12 welcome-content welcone-pedding group-font clearfix ">
		<h4>Create a Group</h4>
		<p>Thank you for choosing to create a new Common Change group. The form below will guide you through creating a group. You can "chat" with us at any time if you want help, by clicking on the Chat window on the bottom right of the screen</p>

		<!-- start form part -->
		<!-- <form class="form-horizontal cust-control"> -->
		@if(isset($wizard))
		{{ html()->modelForm($group->toArray(), 'POST', url('/wizard/group/create'))->class('form-horizontal cust-control')->attribute('name', 'group')->open() }}
		@else
		{{ html()->modelForm($group->toArray(), 'POST', route('group.store'))->class('form-horizontal cust-control')->attribute('name', 'group')->open() }}
		@endif
		
		@include('groups.form')
		<div class="form-group cust-group">
		<div class="col-sm-offset-3 col-sm-5 col-xs-12 but-padding"> 
			<!-- <a  title="Submit a need Request" class="btn btn-success big-button" role="button" onclick="document.group.submit();"><span></span>SAVE & CONTINUE</a> -->
			<button type="submit" title="Submit a need Request" class="btn btn-success big-button"><span></span>
				@if(isset($wizard))
				SAVE & CONTINUE
				@else
				CREATE GROUP
				@endif
			</button>
		</div>
		</div>
		<!-- end Submit buttons --> 
		<!-- </form> -->
		{{ html()->closeModelForm() }}
		<!-- end of form part  --> 

	</div>
	<!-- end edit group detail contain --> 
</div>
