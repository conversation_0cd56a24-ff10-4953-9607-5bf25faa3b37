@extends('layouts.no_container')

@section('title')
	Group Admin
@stop

@section('head')
@stop

@section('content')
<section id="main-bg">
<div class="container welcome-contentpart mobile-padd no-padding"> <span>GROUP ADMIN</span> </div>
@include('groups.bar',array('current_page'=>'members'))
<div class="container no-padding">
<div class="col-lg-12 welcome-content contain-margin">
	<h2>{{ $group->name }}</h2>
	<hr>
	<div class="admin-panel clearfix">
	<h3>Members:</h3>
	<div class="col-xs-12 no-padding contain-margin">
		@forelse($group->members as $member)
			<div class="col-md-3 col-sm-4 col-xs-12 contain-margin">
				<div class="{{ $member->roleClass() }} member-admin">
				<div>
					<span class="tinypix" title="{{$member->firstname}} {{$member->lastname}}" style="background-image: url('{{ $member->profile_pic }}')"></span>
					<div class="pull-right"><strong>{{ $member->firstname }} {{ $member->lastname }}</strong></div>
				</div>
				@if($member->pivot->is_absent) <span class="pull-left"><i>Inactive</i></span> @endif
				<span class="pull-right">{{ $member->pivot->role }}</span>
				<div class="clearfix"></div>
				<hr>
				<a href="{{ URL::route('group.toggle_absent',array('id'=>$group->id, 'userid'=>$member->id)) }}" class="pull-left">toggle active status</a>
				<div class="clearfix"></div>
				<hr>
				@if($member->pivot->role == 'member')
					<a href="{{ URL::route('group.toggle_admin',array('id'=>$group->id, 'userid'=>$member->id)) }}" class="pull-left">make admin</a>
					<a href="{{ URL::route('group.remove',array('id'=>$group->id, 'userid'=>$member->id)) }}" class="pull-right">remove user</a>
				@elseif($member->pivot->role == 'admin')
					<a href="{{ URL::route('group.toggle_admin',array('id'=>$group->id, 'userid'=>$member->id)) }}" class="pull-left">revoke admin</a>
					<a href="{{ URL::route('group.remove',array('id'=>$group->id, 'userid'=>$member->id)) }}" class="pull-right">remove user</a>
				@else
					&nbsp;
				@endif
				<div class="clearfix"></div>
				</div>
			</div>
		@empty
			No Members
		@endforelse
		</div>
	</div>
	</div>
</div>
</section>
@stop

@section('scripts')
	@include('groups.create_scripts')
@stop