@inject('locationHelper', 'App\Models\LocationHelper')
<div class="container group-content welcome-content home-content-bg">
	<!-- <h2>My Group:</h2> -->
	<cite>{{ $group->name }}</cite> <p><span class="font3">Location:</span>{{ $group->city}}, {{ $locationHelper->subdivisionName($group->state) }}, {{ $locationHelper->countryName($group->country) }}</p> <p class="input-margin"><span class="font3">Members:</span>{{ $group->members->count() }}</p>
	@include('groups.financial_info')
	@include('groups.members')
	<div class="col-xs-12 member clearfix">
			<h2>Activity:</h2>

  <div class="col-lg-12 col-sm-12 col-xs-12 no-padding clearfix discuss-container">
	<div class="col-lg-12 col-sm-12  col-xs-12 datemargin">
	{{ html()->modelForm($input, 'GET', route(Route::currentRouteName()))->class('form-horizontal margin-mobile')->attribute('name', 'discuss')->open() }}
	  <div class="col-lg-7 col-sm-5  col-md-7 col-xs-12 no-padding centerposition clearfix ">
		<!-- <form class="form-horizontal margin-mobile"> -->
		  <div class="control-group">
			<div class="controls form-inline">
				<label for="from_date">Date:</label>
				<!-- <input type="text" value="" name="from_date" id="from_date" readonly class="form-control cust-width cust-form form_datetime"> -->
				{{ html()->text('from_date')->id('from_date')->class('form-control cust-width cust-form form_datetime') }}
				<label for="to_date">to</label>
				<!-- <input type="text" value="" name="to_date" id="to_date" readonly class="form-control cust-width cust-form form_datetime"> -->
				{{ html()->text('to_date')->id('to_date')->class('form-control cust-width cust-form form_datetime') }}
				<!-- <a href="#" title="" class="btn btn-success small-button " role="button"> FILTER</a> -->
				<button type="submit" class="btn btn-success small-button">FILTER</button>
			</div>
		</div>
	  </div>
	  <div class="col-lg-5 col-sm-7  col-md-5 col-xs-12 centerposition ">
		<div class="col-lg-6 col-sm-6  col-md-6 col-xs-12">
				{{ html()->select('sort', array('newest' => 'Newest', 'oldest' => 'Oldest', 'alpha' => 'Alphabetical (A-Z)', 'revalpha' => 'Alphabetical (Z-A)', 'state' => 'State', 'country' => 'Country'))->attribute('onchange', 'document.discuss.submit()') }}
		</div>
		<div class="col-lg-6 col-sm-6  col-md-6 col-xs-12 no-padding ">
		  <!-- <form class="navbar-form no-margin" role="search"> -->
			<div class="input-group">
			  <!-- <input type="text" class="form-control cust-form" placeholder="Search" name="q"> -->
			  {{ html()->text('search')->class('form-control cust-form')->placeholder('Search') }}
			  <div class="input-group-btn">
				<button class="btn but1-margin btn-success btn-default" type="submit"><i class="glyphicon glyphicon-search"></i></button>
			  </div>
			</div>
		  <!-- </form> -->
		</div>
	  </div>
	{{ html()->closeModelForm() }}
	</div>
	<div class="clearfix"></div>
	<!-- start of table -->
	<div class="col-lg-12 col-sm-12 mobilescroll col-xs-12 no-padding">
	{{ $discussions->links() }}
	  <table class="table table-responsive table-condensed">
		<thead class="tdclass">
		  <tr >
			<th>BY</th>
			<th>TYPE</th>
			<th>TITLE</th>
			<th>LAST ACTIVITY</th>
			<th>READ?</th>
		  </tr>
		</thead>
		<tbody>

		@forelse($discussions as $discussion)
			<tr>
				<th><span class="tinypix" title="{{$discussion->creator->firstname}} {{$discussion->creator->lastname}}" style="background-image: url('{{ $discussion->creator->profile_pic }}')"></span></th>
				<td>@if(isset($discussion->issue)) Request @else Discussion @endif</td>
				<td><a href="{{ URL::route('discussion.show',array('discussion'=>$discussion->id)) }}">{{ $discussion->title }}</a></td>
				<td><span title="{{ $discussion->updated_local.' '.$discussion->updated_local->tzName }}">{{ $discussion->updated_at->diffForHumans() }}</span></td>
				@if ($discussion->is_unread)
					<td class="worning tdimg">Unread</td>
				@else
					<td>Read</td>
				@endif
			</tr>
		@empty
			<tr><td colspan="6">No activity to display.</td></tr>
		@endforelse

		</tbody>
	  </table>
	</div>
		<!-- end of table -->
  </div>
	</div>
</div>