@extends('layouts.master')
@inject('locationHelper', 'App\Models\LocationHelper')

@section('title')
	Group Detail
@stop

@section('head')
@stop

@section('home-content')
	<span>Group Detail</span>
@stop

@section('content')
<div class="col-xs-12 group-content"> <cite>{{ $group->name }}</cite>
	@if(!$public && !$is_nonmember && $group->owner()->id != Auth::User()->id)
		<a href="{{ URL::route('group.leave',array('id'=>$group->id)) }}" title="Leave Group" class="btn btn-success1 btn-success pull-right small-button img-margin" role="button">
			<i class="glyphicon glyphicon-remove-circle"></i> LEAVE GROUP</a>
	@endif
	<div class="col-xs-12 no-padding">
		<blockquote class="cust-block">
			<ul class="list-unstyled date-content">
				<li>Location:<span>
					{{ $group->city }},
					@if($group->state != 'zzzz')
						{{ $locationHelper->subdivisionName($group->state) }},
					@endif
					{{ $locationHelper->countryName($group->country) }}
				</span></li>
				<li>Members:<span>{{ $group->members->count() }}</span></li>
				<li>Date Started:<span>{{ $group->created_at->format('M j, Y') }}</span></li>
				<li>Status:<span>
					@if ($group->type == 'public')
						Public Group (anyone can join)
					@elseif ($group->type == 'private')
						Private Group (you can ask to join)
					@else
						Secret Group
					@endif
				</span></li>
				<li>Donation Requirements to Join:<span>
					@if($group->min_donation)
						{{ $group->currency }}{{ $group->min_donation }}
						on a {{ $group->donation_frequency }} basis.
					@else
						no requirement
					@endif
				</span></li>
			</ul>
		</blockquote>
	</div>
	<div class="col-xs-12 no-padding">
		<blockquote class="cust-block">
			<h6>DESCRIPTION</h6>
			<p>{{ $group->description }}</p>
		</blockquote>
	</div>
	<!-- Start of member part -->
	@include('groups.members')
	<!-- End of member part -->
	<div class="col-xs-12 group-content member-covenant clearfix">
		<h6>MEMBER COVENANT</h6>
		<p>{{ $group->covenant }}</p>
		@if($public)
			<a href="{{ URL::route('group.show',array('group'=>$group->id)) }}" title="Sign In to Join Group" class="btn btn-success btn-success1 big-button" role="button">SIGN IN TO JOIN</a>
		@elseif($is_nonmember)
			@if($wizard)
				{{ html()->form('POST', route('wizard.join', $group->id))->open() }}
			@else
				{{ html()->form('POST', route('group.join', $group->id))->open() }}
			@endif
			<table>
				<tr>
					<td>
						<!--
						<input type="checkbox" name="checkboxG4" id="checkboxG4" class="css-checkbox" />
						<label for="checkboxG4" class="css-label font1">I agree to the Member Covenant.</label>
						-->
						{!! $errors->first('accept_covenant', '<span class="error">You must check the checkbox to join.</span>') !!}
						{{ html()->checkbox('accept_covenant', false)->id('accept_covenant')->class('css-checkbox1') }}
						<label for="accept_covenant" class="css-label1 font1">I agree to the Member Covenant.</label>
					</td>
				</tr>
			</table>
			<div class="col-lg-12 col-lg-12 no-padding img-margin cust-marginimg ">
				@if ($group->isInvited(Auth::User()))
					<button type="submit" title="Join Group" class="btn btn-success btn-success1 big-button" style="margin-bottom:10px;"> <img src="/images/button-three.png">ACCEPT INVITATION TO JOIN</button>
					<a href="{{ URL::route('invite.decline',array('groupid'=>$group->id)) }}" title="Join Group" class="btn btn-success btn-success1 big-button" role="button"> <img src="/images/button-three.png">DECLINE INVITATION</a>
				@elseif ($group->type == 'public')
					<button type="submit" title="Join Group" class="btn btn-success btn-success1 big-button" style="margin-bottom:10px;"> <img src="/images/button-three.png">JOIN GROUP</button>
				@elseif ($group->type == 'private')
					<button type="submit" title="Join Group" class="btn btn-success btn-success1 big-button" style="margin-bottom:10px;"> <img src="/images/button-three.png">REQUEST TO JOIN</button>
				@else
				  <a title="Private Group" class="btn btn-success btn-success1 privet-group big-button" role="button">PRIVATE GROUP</a>
				@endif

				<a href="mailto:{{ $group->owner()->email }}" title="Contact Group Facilitator" class="btn btn-success btn-success1 big-button" role="button">CONTACT GROUP FACILITATOR</a>
				<a href="{{ URL::previous() }}" title="Go Back" class="btn btn-success btn-success1 big-button" role="button">BACK </a>
			</div>
			{{ html()->form()->close() }}
		@endif
	</div>
</div>
@stop

@section('scripts')
	@include('groups.create_scripts')
@stop