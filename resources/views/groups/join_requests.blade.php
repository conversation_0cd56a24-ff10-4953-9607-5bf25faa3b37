@extends('layouts.no_container')

@section('title')
	Group Join Requests
@stop

@section('head')
@stop

@section('content')
<section id="main-bg">
<div class="container welcome-contentpart mobile-padd no-padding"> <span>JOIN REQUESTS</span> </div>
@include('groups.bar',array('current_page'=>'join'))
<div class="container no-padding">
<div class="col-lg-12 welcome-content contain-margin">
	<h2>{{ $group->name }}</h2>
	<div class="admin-panel clearfix">
	<h3>Join Requests:</h3>
	<div class="col-xs-12 no-padding contain-margin">
		@forelse($group->join_requests as $user)
			<div class="col-md-3 col-sm-4 col-xs-12 contain-margin">
				<div class="membr member-admin">
				<span class="tinypix" title="{{$user->firstname}} {{$user->lastname}}" style="background-image: url('{{ $user->profile_pic }}')"></span>
				<span><strong>{{ $user->firstname }} {{ $user->lastname }}</strong></span>
				<hr>
				<span>{{ $user->pivot->message }}</span>
				<div class="clearfix"></div>
				<hr>
					<a href="{{ URL::route('group.accept_request',array('id'=>$group->id,'userid'=>$user->id)) }}" class="pull-left">accept request</a>
					<a href="{{ URL::route('group.reject_request',array('id'=>$group->id,'userid'=>$user->id)) }}" class="pull-right">deny request</a>
				<div class="clearfix"></div>
				</div>
			</div>
		@empty
			<p>No Requests</p>
		@endforelse
	</div>
	<div class="clearfix"></div>
</div>
</section>
@stop

@section('scripts')
	@include('groups.create_scripts')
@stop