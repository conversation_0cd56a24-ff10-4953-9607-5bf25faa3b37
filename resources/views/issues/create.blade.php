@extends('layouts.master')

@section('title')
	Submit a Request
@stop

@section('head')
<link type="text/css" rel="stylesheet" href="{{ asset('/css/bootstrap-datetimepicker.css?version=1') }}" media="all">
<link type="text/css" rel="stylesheet" href="{{ asset('/css/jquery-ui1.css?version=1') }}" media="all">
<link type="text/css" rel="stylesheet" href="{{ asset('/css/selectboxit.css?version=1') }}" media="all">
@stop

@section('home-content')
	<span>SUBMIT A REQUEST TO {{ $group->name }}</span>
@stop

@section('content')
<div class="col-lg-12 welcome-content welcone-pedding clearfix text-padding1">
	<h4>DESCRIPTION</h4>
	<p>Enter the details below to create a new need request. Keep in mind that a need could be fulfilled in ways other than financial, such as volunteering time, skills                       and services to recipients. If you do not have a specific deadine for the need, a good time period is 2 weeks to give group members time to respond.</p>

	<!-- start form part -->
	<!-- <form class="form-horizontal cust-control"> -->
	{{ html()->form('POST', route('issue.confirm'))->class('form-horizontal cust-control')->attribute('name', 'issue')->open() }}
		{{ html()->hidden('discussion_id', $discussion_id) }}
		{{ html()->hidden('group_id', $group->id) }}
		@include('issues.form')
		<!-- Submit buttons -->
		<div class="col-xs-12 col-md-offset-2 col-sm-offset-3 button-padding clearfix">
			<a href="#" title="View Request" class="btn btn-success small-button" role="button">BACK</a>&nbsp;
			<button type="submit" title="Create Need Request" class="btn btn-success big-button" role="button"><span><img src="/images/button-two.png"></span>CREATE REQUEST</a>
		</div>
	<!-- </form> -->
	{{ html()->form()->close() }}
	<!-- end form part -->
	<!-- end Submit Need Request -->
</div>
@stop

@section('scripts')
<script src="{{ asset('/js/bootstrap-datetimepicker.min.js') }}"></script>
<script src="{{ asset('/js/jquery-ui.js') }}"></script>
<script> 
	function payment_type_setup() {
		switch($("#payinfo_payment_type").val()) {
			case 'paypal':
				$("#paypal").show();
				$("#wire").hide();
				$("#uk_payment").hide();
				break;
			case 'check':
				$("#paypal").hide();
				$("#wire").hide();
				$("#uk_payment").hide();
				break;
			case 'wire':
				$("#paypal").hide();
				$("#wire").show();
				$("#uk_payment").hide();
				break;
			case 'uk_payment':
				$("#paypal").hide();
				$("#wire").hide();
				$("#uk_payment").show();
				break;
		}
	}

	$(document).ready(function() {
		$("#startDate").datetimepicker();
		
		$( "#payinfo_payment_type" ).change(payment_type_setup);
		payment_type_setup();
		
		$("#copy_values").click(function() {
			$("#payinfo_firstname").val($("#recipient_firstname").val());
			$("#payinfo_lastname").val($("#recipient_lastname").val());
			$("#payinfo_address_addr1").val($("#recipient_address_addr1").val());
			$("#payinfo_address_addr2").val($("#recipient_address_addr2").val());
			$("#payinfo_address_city").val($("#recipient_address_city").val());
			$("#payinfo_address_state").val($("#recipient_address_state").val());
			var selectBox = $("select#payinfo_address_state").data("selectBox-selectBoxIt");
			selectBox.selectOption($("#recipient_address_state").val());
			$("#payinfo_address_postcode").val($("#recipient_address_postcode").val());
			$("#payinfo_address_country").val($("#recipient_address_country").val());
			selectBox = $("select#payinfo_address_country").data("selectBox-selectBoxIt");
			selectBox.selectOption($("#recipient_address_country").val());
			$("#payinfo_phone").val($("#recipient_phone").val());
			$("#payinfo_email").val($("#recipient_email").val());
		});
	});
</script>
<script src="{{ asset('/js/selectBoxIt.js') }}"></script>
<script>
	$("select").not("#payinfo_payment_type").selectBoxIt({
		autoWidth: false,
		showFirstOption: false,
		similarSearch: true,
	});
	$("#payinfo_payment_type").selectBoxIt({
		autoWidth: false,
		showFirstOption: true
	});
</script>
@stop
