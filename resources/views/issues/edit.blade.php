@extends('layouts.master')

@section('title')
	Submit a Request
@stop

@section('head')
<link type="text/css" rel="stylesheet" href="{{ asset('/css/dark.css?version=1') }}" media="all">
<link type="text/css" rel="stylesheet" href="{{ asset('/css/jquery-ui1.css?version=1') }}" media="all">
<link type="text/css" rel="stylesheet" href="{{ asset('/css/selectboxit.css?version=1') }}" media="all">
@stop

@section('home-content')
	<span>EDIT REQUEST</span>
@stop

@section('content')
<div class="col-lg-12 welcome-content welcone-pedding clearfix text-padding1">
	<h4>DESCRIPTION</h4>
	<p>You can edit this request now. Change the title, description, deadline,
	or change the discussion from open to closed to end the conversation.<br />
	<strong>Note:</strong> if you change the Request Total amount, voting will start over.</p>

	<!-- start form part -->
	<!-- <form class="form-horizontal cust-control"> -->
	{{ html()->modelForm($issue, 'PUT', route('issue.update', $issue->id))->class('form-horizontal cust-control')->attribute('name', 'issue')->open() }}
		@include('issues.form')
		<div class="form-group cust-group ">
			<label for="input" class="col-sm-3 col-md-2 col-lg-2 control-label"> <span>Status:</span></label>
			<div class="col-sm-4">
				<!-- <input type="text" class="form-control cust-form" id="input" placeholder=""> -->
				@error('status')
					<span class="error">{{ $message }}</span>
				@enderror
				{{ html()->select('status', array(null, 'open' => 'Open', 'closed' => 'Closed'))->id('combobox4')->class('basic-example') }}
			</div>
		</div>
	<!-- </form> -->
	<!-- end form part -->
	<!-- Submit buttons -->
	<div class="col-lg-12 col-lg-12 col-lg-offset-2 button-padding clearfix">
		<div class="col-lg-1 col-md-1 col-xs-3 col-sm-1 no-padding">
		<a href="{{ URL::route('issue.show',array('issue'=>$issue->id)) }}" title="View Request" class="btn btn-success small-button" role="button">BACK</a> </div>
		<div class="col-lg-3 col-md-3 col-xs-4 col-sm-4 no-padding">
		<button type="submit" title="Create Need Request" class="btn btn-success big-button"><span><img src="/images/button-two.png"></span>SUBMIT CHANGES</button> </div>
	</div>
	{{ html()->closeModelForm() }}
	<!-- end Submit Need Request -->
</div>
@stop

@section('scripts')
<script src="{{ asset('/js/bootstrap-datetimepicker.min.js') }}"></script>
<script src="{{ asset('/js/jquery-ui.js') }}"></script>
<script>
	function payment_type_setup() {
		switch($("#payinfo_payment_type").val()) {
			case 'paypal':
				$("#paypal").show();
				$("#wire").hide();
				$("#uk_payment").hide();
				break;
			case 'check':
				$("#paypal").hide();
				$("#wire").hide();
				$("#uk_payment").hide();
				break;
			case 'wire':
				$("#paypal").hide();
				$("#wire").show();
				$("#uk_payment").hide();
				break;
			case 'uk_payment':
				$("#paypal").hide();
				$("#wire").hide();
				$("#uk_payment").show();
				break;
		}
	}

	$(document).ready(function() {
		$("#startDate").datetimepicker();

		$( "#payinfo_payment_type" ).change(payment_type_setup);
		payment_type_setup();

		$("#copy_values").click(function() {
			$("#payinfo_firstname").val($("#recipient_firstname").val());
			$("#payinfo_lastname").val($("#recipient_lastname").val());
			$("#payinfo_address_addr1").val($("#recipient_address_addr1").val());
			$("#payinfo_address_addr2").val($("#recipient_address_addr2").val());
			$("#payinfo_address_city").val($("#recipient_address_city").val());
			$("#payinfo_address_state").val($("#recipient_address_state").val());
			var selectBox = $("select#payinfo_address_state").data("selectBox-selectBoxIt");
			selectBox.selectOption($("#recipient_address_state").val());
			$("#payinfo_address_postcode").val($("#recipient_address_postcode").val());
			$("#payinfo_address_country").val($("#recipient_address_country").val());
			selectBox = $("select#payinfo_address_country").data("selectBox-selectBoxIt");
			selectBox.selectOption($("#recipient_address_country").val());
			$("#payinfo_phone").val($("#recipient_phone").val());
			$("#payinfo_email").val($("#recipient_email").val());
		});
	});
</script>
<script src="{{ asset('/js/selectBoxIt.js') }}"></script>
<script>
	console.log('did it');
	$("select").not("#payinfo_payment_type").selectBoxIt({
		autoWidth: false,
		showFirstOption: false
	});
	$("#payinfo_payment_type").selectBoxIt({
		autoWidth: false,
		showFirstOption: true
	});
</script>
@stop