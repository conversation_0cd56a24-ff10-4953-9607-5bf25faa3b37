@extends('layouts.master')
@inject('locationHelper', 'App\Models\LocationHelper')

@section('title')
	Confirm Request
@stop

@section('head')
<link type="text/css" rel="stylesheet" href="{{ asset('/css/dark.css?version=1') }}" media="all">
@stop

@section('home-content')
	<span>Submit a Need Request</span>
@stop

@section('content')
<div class="col-lg-12 col-xs-12 welcome-content welcone-pedding clearfix ">
	<h3>REVIEW DETAILS:</h3>
	<!-- Submitneedconfirmation contain -->
	<!-- <form class="form-horizontal cust-control"> -->
	{{ html()->modelForm($input, 'POST', route('issue.store'))->class('form-horizontal cust-control')->attribute('name', 'issue')->open() }}
		{{ html()->hidden('discussion_id') }}
		{{ html()->hidden('group_id') }}
		<div class="form-group cust-group">
			<label for="input" class="col-sm-3 col-md-2 col-lg-2 col-xs-12 control-label"><span>Request Title:</span></label>
			<div class="col-sm-9 col-xs-12">
				<div class="control-label">{{ $input['title'] }}</div>
				{{ html()->hidden('title') }}
			</div>
		</div>
		<div class="form-group cust-group">
			<label for="input" class="col-sm-3 col-md-2 col-lg-2 col-xs-12 control-label"> <span>Description: </span></label>
			<div class="col-sm-9 col-xs-12">
				<div class="control-label">{{ $input['description'] }}</div>
				{{ html()->hidden('description') }}
			</div>
		</div>
		<div class="form-group cust-group">
			<label for="input" class="col-sm-3 col-md-2 col-lg-2 col-xs-12 control-label"> <span>Request Total:</span></label>
			<div class="col-sm-9 col-xs-12">
				<div class="control-label">{{ $group->currency }}{{ $input['proposal']['amount'] }}</div>
				{{ html()->hidden('proposal[amount]') }}
			</div>
		</div>
			<div class="form-group cust-group">
			<label for="input" class="col-sm-3 col-md-2 col-lg-2 col-xs-12 control-label"> <span>Deadline:</span></label>
			<div class="col-sm-9 col-xs-12">
				<div class="control-label">{{ $input['proposal']['extend_deadline'] }} days</div>
				{{ html()->hidden('proposal[extend_deadline]') }}
			</div>
		</div>
		<div class="form-group cust-group">
			<label for="input" class="col-sm-3 col-md-2 col-lg-2 col-xs-12 control-label"> <span>Category</span>:</label>
			<div class="col-sm-9">
				<div class="control-label">{{ App\Models\IssueCategory::find($input['issue_category_id'])->description }}</div>
				{{ html()->hidden('issue_category_id') }}
			</div>
		</div>
		<div class="form-group cust-group">
			<label for="input" class="col-sm-3 col-md-2 col-lg-2 col-xs-12 control-label"> <span>Type of Request:</span></label>
			<div class="col-sm-9 col-xs-12">
				<div class="control-label">{{ App\Models\IssueType::find($input['issue_type_id'])->description }}</div>
				{{ html()->hidden('issue_type_id') }}
			</div>
		</div>
		<div class="form-group cust-group">
			<label for="input" class="col-sm-3 col-md-2 col-lg-2 col-xs-12 control-label"> <span>First Name:</span></label>
			<div class="col-sm-9 col-xs-12">
				<div class="control-label">{{ $input['recipient']['firstname'] }}</div>
				{{ html()->hidden('recipient[firstname]') }}
			</div>
		</div>
		<div class="form-group cust-group">
			<label for="input" class="col-sm-3 col-md-2 col-lg-2 col-xs-12 control-label"><span>Last Name:</span></label>
			<div class="col-sm-9 col-xs-12">
				<div class="control-label">{{ $input['recipient']['lastname'] }}</div>
				{{ html()->hidden('recipient[lastname]') }}
			</div>
		</div>
		<div class="form-group cust-group">
			<label for="input" class="col-sm-3 col-md-2 col-lg-2 col-xs-12 control-label"><span>Street Address:</span></label>
			<div class="col-sm-9 col-lg-9 col-xs-12">
				<div class="control-label">
					@if(isset($input['recipient']['address']))
						{{ $input['recipient']['address']['addr1'] ?? '' }}<br>
						{{ $input['recipient']['address']['addr2'] ?? '' }}
					@else
						<!-- No address provided -->
					@endif
				</div>
				{{ html()->hidden('recipient[address][addr1]', $input['recipient']['address']['addr1'] ?? '') }}
				{{ html()->hidden('recipient[address][addr2]', $input['recipient']['address']['addr2'] ?? '') }}
			</div>
		</div>
		<div class="form-group cust-group">
			<label for="input" class="col-sm-3 col-md-2 col-lg-2 col-xs-12 control-label"> <span>City:</span></label>
			<div class="col-sm-9 col-xs-12">
				<div class="control-label">
					@if(isset($input['recipient']['address']))
						{{ $input['recipient']['address']['city'] ?? '' }}
					@else
						<!-- No city provided -->
					@endif
				</div>
				{{ html()->hidden('recipient[address][city]', $input['recipient']['address']['city'] ?? '') }}
			</div>
		</div>
		<div class="form-group cust-group">
			<label for="input" class="col-sm-3 col-md-2 col-lg-2 col-xs-12 control-label"><span>State:</span></label>
			<div class="col-sm-9 col-xs-12">
				<div class="control-label">
					@if(isset($input['recipient']['address']))
						{{ $locationHelper->subdivisionName($input['recipient']['address']['state'] ?? '') }}
					@else
						<!-- No state provided -->
					@endif
				</div>
				{{ html()->hidden('recipient[address][state]', $input['recipient']['address']['state'] ?? '') }}
			</div>
		</div>
		<div class="form-group cust-group">
			<label for="input" class="col-sm-3 col-md-2 col-lg-2 col-xs-12 control-label"> <span>Postal Code:</span></label>
			<div class="col-sm-9 col-xs-12">
				<div class="control-label">
					@if(isset($input['recipient']['address']))
						{{ $input['recipient']['address']['postcode'] ?? '' }}
					@else
						<!-- No postal code provided -->
					@endif
				</div>
				{{ html()->hidden('recipient[address][postcode]', $input['recipient']['address']['postcode'] ?? '') }}
			</div>
		</div>
		<div class="form-group cust-group">
			<label for="input" class="col-sm-3 col-md-2 col-lg-2 col-xs-12 control-label"> <span>Country:</span></label>
			<div class="col-sm-9 col-xs-12">
				<div class="control-label">
					@if(isset($input['recipient']['address']))
						{{ $locationHelper->countryName($input['recipient']['address']['country'] ?? '') }}
					@else
						<!-- No country provided -->
					@endif
				</div>
				{{ html()->hidden('recipient[address][country]', $input['recipient']['address']['country'] ?? '') }}
			</div>
		</div>
		<div class="form-group cust-group">
			<label for="input" class="col-sm-3 col-md-2 col-lg-2 col-xs-12 control-label"> <span>Phone:</span></label>
			<div class="col-sm-9 col-xs-12 ">
				<div class="control-label">{{ $input['recipient']['phone'] }}</div>
				{{ html()->hidden('recipient[phone]') }}
			</div>
		</div>
		<div class="form-group cust-group">
			<label for="input" class="col-sm-3 col-md-2 col-lg-2 col-xs-12 control-label"> <span>Email:</span></label>
			<div class="col-sm-9 col-xs-12">
				<div class="control-label">{{ $input['recipient']['email'] }}</div>
				{{ html()->hidden('recipient[email]') }}
			</div>
		</div>

<br /><br />
<!-- =================== PAYMENT INFO =================== -->

		<div class="col-xs-12 no-padding">
			<h3>PAYMENT INFO: </h3>
		</div>
		<div class="form-group cust-group">
			<label for="payinfo_firstname" class="col-sm-3 col-md-2 col-lg-2 col-xs-12 control-label"><span>First Name:</span></label>
			<div class="col-sm-9 col-xs-12">
				<div class="control-label">
					@if(isset($input['proposal']['pay_info']))
						{{ $input['proposal']['pay_info']['firstname'] ?? '' }}
					@else
						<!-- No payment first name provided -->
					@endif
				</div>
				{{ html()->hidden('proposal[pay_info][firstname]', $input['proposal']['pay_info']['firstname'] ?? '') }}
			</div>
		</div>
		<div class="form-group cust-group">
			<label for="payinfo_lastname" class="col-sm-3 col-md-2 col-lg-2 col-xs-12 control-label"><span>Last Name:</span></label>
			<div class="col-sm-9 col-xs-12">
				<div class="control-label">
					@if(isset($input['proposal']['pay_info']))
						{{ $input['proposal']['pay_info']['lastname'] ?? '' }}
					@else
						<!-- No payment last name provided -->
					@endif
				</div>
				{{ html()->hidden('proposal[pay_info][lastname]', $input['proposal']['pay_info']['lastname'] ?? '') }}
			</div>
		</div>
		<div class="form-group cust-group">
			<label for="payinfo_address_addr1" class="col-sm-3 col-md-2 col-lg-2 col-xs-12 control-label"><span>Street Address:</span></label>
			<div class="col-sm-9 col-xs-12">
				<div class="control-label">
					{{ $input['proposal']['pay_info']['address']['addr1'] ?? '' }}<br />
					{{ $input['proposal']['pay_info']['address']['addr2'] ?? '' }}
				</div>
				{{ html()->hidden('proposal[pay_info][address][addr1]', $input['proposal']['pay_info']['address']['addr1'] ?? '') }}
				{{ html()->hidden('proposal[pay_info][address][addr2]', $input['proposal']['pay_info']['address']['addr2'] ?? '') }}
			</div>
		</div>
		<div class="form-group cust-group">
			<label for="payinfo_address_city" class="col-sm-3 col-md-2 col-lg-2 col-xs-12 control-label"> <span>City:</span></label>
			<div class="col-sm-9 col-xs-12">
				<div class="control-label">{{ $input['proposal']['pay_info']['address']['city'] ?? '' }}</div>
				{{ html()->hidden('proposal[pay_info][address][city]', $input['proposal']['pay_info']['address']['city'] ?? '') }}
			</div>
		</div>
		<div class="form-group cust-group">
			<label for="payinfo_address_state" class="col-sm-3 col-md-2 col-lg-2 col-xs-12 control-label"> <span>State:</span></label>
			<div class="col-sm-9 col-xs-12">
				<div class="control-label">{{ $locationHelper->subdivisionName($input['proposal']['pay_info']['address']['state'] ?? '') }}</div>
				{{ html()->hidden('proposal[pay_info][address][state]', $input['proposal']['pay_info']['address']['state'] ?? '') }}
			</div>
		</div>
		<div class="form-group cust-group">
			<label for="payinfo_address_postcode" class="col-sm-3 col-md-2 col-lg-2 col-xs-12 control-label"> <span>Postal Code:</span></label>
			<div class="col-sm-9 col-xs-12">
				<div class="control-label">{{ $input['proposal']['pay_info']['address']['postcode'] ?? '' }}</div>
				{{ html()->hidden('proposal[pay_info][address][postcode]', $input['proposal']['pay_info']['address']['postcode'] ?? '') }}
			</div>
		</div>
		<div class="form-group cust-group">
			<label for="payinfo_address_country" class="col-sm-3 col-md-2 col-lg-2 col-xs-12 control-label"> <span>Country:</span></label>
			<div class="col-sm-9 col-xs-12">
				<div class="control-label">{{ $locationHelper->countryName($input['proposal']['pay_info']['address']['country'] ?? '') }}</div>
				{{ html()->hidden('proposal[pay_info][address][country]', $input['proposal']['pay_info']['address']['country'] ?? '') }}
			</div>
		</div>
		<div class="form-group cust-group">
			<label for="payinfo_phone" class="col-sm-3 col-md-2 col-lg-2 col-xs-12 control-label"> <span>Phone:</span></label>
			<div class="col-sm-9 col-xs-12">
				<div class="control-label">{{ $input['proposal']['pay_info']['phone'] ?? '' }}</div>
				{{ html()->hidden('proposal[pay_info][phone]', $input['proposal']['pay_info']['phone'] ?? '') }}
			</div>
		</div>
		<div class="form-group cust-group">
			<label for="payinfo_email" class="col-sm-3 col-md-2 col-lg-2 col-xs-12 control-label"> <span>Email:</span></label>
			<div class="col-sm-9 col-xs-12">
				<div class="control-label">{{ $input['proposal']['pay_info']['email'] ?? '' }}</div>
				{{ html()->hidden('proposal[pay_info][email]', $input['proposal']['pay_info']['email'] ?? '') }}
			</div>
		</div>
		<div class="form-group cust-group">
			<label for="payinfo_notes" class="col-sm-3 col-md-2 col-lg-2 col-xs-12 control-label"> <span>Additional notes:</span></label>
			<div class="col-sm-9 col-xs-12">
				<div class="control-label">{{ $input['proposal']['pay_info']['notes'] ?? '' }}</div>
				{{ html()->hidden('proposal[pay_info][notes]', $input['proposal']['pay_info']['notes'] ?? '') }}
			</div>
		</div>

		<div class="form-group cust-group">
			<label for="payinfo_payment_type" class="col-sm-3 col-md-2 col-lg-2 col-xs-12 control-label"> <span>Payment Type:</span></label>
			<div class="col-sm-9 col-xs-12">
				<div class="control-label">{{ ($input['proposal']['pay_info']['payment_type'] == 'paypal') ? 'Cash App' : $input['proposal']['pay_info']['payment_type'] ?? '' }}</div>
				{{ html()->hidden('proposal[pay_info][payment_type]', $input['proposal']['pay_info']['payment_type'] ?? '') }}
			</div>
		</div>

		<div id="uk_payment" name="uk_payment" style="display:none">
			<div class="form-group cust-group">
				<label for="payinfo_uk_bank" class="col-sm-3 col-md-2 col-lg-2 control-label infoimg"> <span>Bank Name:</span></label>
				<div class="col-sm-9 col-xs-12">
					<div class="control-label">{{ $input['proposal']['pay_info']['uk_bank'] ?? '' }}</div>
					{{ html()->hidden('proposal[pay_info][uk_bank]', $input['proposal']['pay_info']['uk_bank'] ?? '') }}
				</div>
			</div>
			<div class="form-group cust-group">
				<label for="payinfo_uk_sortcode" class="col-sm-3 col-md-2 col-lg-2 control-label infoimg"> <span>Sort Code:</span></label>
				<div class="col-sm-9 col-xs-12">
					<div class="control-label">{{ $input['proposal']['pay_info']['uk_sortcode'] ?? '' }}</div>
					{{ html()->hidden('proposal[pay_info][uk_sortcode]', $input['proposal']['pay_info']['uk_sortcode'] ?? '') }}
				</div>
			</div>
			<div class="form-group cust-group">
				<label for="payinfo_uk_account" class="col-sm-3 col-md-2 col-lg-2 control-label infoimg"> <span>Account Number:</span></label>
				<div class="col-sm-9 col-xs-12">
					<div class="control-label">{{ $input['proposal']['pay_info']['uk_account'] ?? '' }}</div>
					{{ html()->hidden('proposal[pay_info][uk_account]', $input['proposal']['pay_info']['uk_account'] ?? '') }}
				</div>
			</div>
			<div class="form-group cust-group">
				<label for="payinfo_uk_account_name" class="col-sm-3 col-md-2 col-lg-2 control-label infoimg"> <span>Name on Account:</span></label>
				<div class="col-sm-9 col-xs-12">
					<div class="control-label">{{ $input['proposal']['pay_info']['uk_account_name'] ?? '' }}</div>
					{{ html()->hidden('proposal[pay_info][uk_account_name]', $input['proposal']['pay_info']['uk_account_name'] ?? '') }}
				</div>
			</div>
		</div>

		<div id="paypal" name="paypal" style="display:none">
			<div class="form-group cust-group">
				<label for="payinfo_paypal_email" class="col-sm-3 col-md-2 col-lg-2 col-xs-12 control-label"> <span>Cash App:</span></label>
				<div class="col-sm-9 col-xs-12">
					<div class="control-label">{{ $input['proposal']['pay_info']['paypal_email'] ?? '' }}</div>
					{{ html()->hidden('proposal[pay_info][paypal_email]', $input['proposal']['pay_info']['paypal_email'] ?? '') }}
				</div>
			</div>
		</div>

		<div id="wire" style="display:none">
			<div class="form-group cust-group">
				<label for="payinfo_wire_bank_name" class="col-sm-3 col-md-2 col-lg-2 col-xs-12 control-label"> <span>Bank Name:</span></label>
				<div class="col-sm-9 col-xs-12">
					<div class="control-label">{{ $input['proposal']['pay_info']['wire_bank_name'] ?? '' }}</div>
					{{ html()->hidden('proposal[pay_info][wire_bank_name]', $input['proposal']['pay_info']['wire_bank_name'] ?? '') }}
				</div>
			</div>
			<div class="form-group cust-group">
				<label for="payinfo_wire_bank_routing" class="col-sm-3 col-md-2 col-lg-2 col-xs-12 control-label"> <span>Bank Routing number / Swift code:</span></label>
				<div class="col-sm-9 col-xs-12">
					<div class="control-label">{{ $input['proposal']['pay_info']['wire_bank_routing'] ?? '' }}</div>
					{{ html()->hidden('proposal[pay_info][wire_bank_routing]', $input['proposal']['pay_info']['wire_bank_routing'] ?? '') }}
				</div>
			</div>
			<div class="form-group cust-group">
				<label for="payinfo_wire_bank_address" class="col-sm-3 col-md-2 col-lg-2 col-xs-12 control-label"> <span>Bank Address:</span></label>
				<div class="col-sm-9 col-xs-12">
					<div class="control-label">{{ $input['proposal']['pay_info']['wire_bank_address'] ?? '' }}</div>
					{{ html()->hidden('proposal[pay_info][wire_bank_address]', $input['proposal']['pay_info']['wire_bank_address'] ?? '') }}
				</div>
			</div>
			<div class="form-group cust-group">
				<label for="payinfo_wire_acct_number" class="col-sm-3 col-md-2 col-lg-2 col-xs-12 control-label"> <span>Account Number:</span></label>
				<div class="col-sm-9 col-xs-12">
					<div class="control-label">{{ $input['proposal']['pay_info']['wire_acct_number'] ?? '' }}</div>
					{{ html()->hidden('proposal[pay_info][wire_acct_number]', $input['proposal']['pay_info']['wire_acct_number'] ?? '') }}
				</div>
			</div>
		</div>
	<!-- </form> -->
	{{ html()->closeModelForm() }}
	<!-- End of Submitneedconfirmation contain -->
	<!-- submmmit buttons -->
	<div class="col-lg-1 col-md-1 col-sm-1 col-xs-3 no-padding">
		<a href="javascript:history.back()" title="View Request" class="btn btn-success small-button" role="button">EDIT</a> </div>
	<div class="col-lg-3 col-md-3 col-xs-9 no-padding">
		<a onclick="document.issue.submit();" title="Submit a need Request" class="btn btn-success big-button" role="button"><span><img src="/images/button-two.png"></span>SUBMIT REQUEST</a> </div>
	<!--End of submmmit buttons -->
</div>
@stop

@section('scripts')
<script>
	function payment_type_setup() {
		switch($("#payinfo_payment_type").val()) {
			case 'uk_payment':
				$("#uk_payment").show();
				$("#paypal").hide();
				$("#wire").hide();
				break;
			case 'paypal':
				$("#uk_payment").hide();
				$("#paypal").show();
				$("#wire").hide();
				break;
			case 'check':
				$("#uk_payment").hide();
				$("#paypal").hide();
				$("#wire").hide();
				break;
			case 'wire':
				$("#uk_payment").hide();
				$("#paypal").hide();
				$("#wire").show();
				break;
		}
	}

	$(document).ready(function() {
		$(".basic-example").heapbox();

		$( "#payinfo_payment_type" ).change(payment_type_setup);
		payment_type_setup();
	});
</script>
<script src="{{ asset('/js/bootstrap-toggle.js') }}"></script>
<script src="{{ asset('/js/jquery.heapbox-0.9.4.js') }}"></script>
@stop