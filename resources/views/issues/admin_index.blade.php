@extends('layouts.master')

@section('title')
	Admin: Requests
@stop

@section('head')
<link type="text/css" rel="stylesheet" href="{{ asset('/css/jqueryui.css?version=1') }}" media="all">
<link type="text/css" rel="stylesheet" href="{{ asset('/css/welcomenav.css?version=2') }}" media="all">
<link type="text/css" rel="stylesheet" href="{{ asset('/css/selectboxit.css?version=1') }}" media="all">
@stop

@section('content')
<section id="main-bg">
	<div class="container  no-padding">
		<div class="col-xs-12 no-padding margin-padding ">
		<div class="col-xs-12">
			@include('admin.bar',array('current_page'=>'issues'))
		</div>
		<div class="col-sm-3 col-xs-12 allgroup-contain "> <span>REQUESTS</span> </div>
		<div class="col-sm-9 col-xs-12 new-radius centerposition "><div>
		{{ html()->modelForm($input, 'GET', route('issue.admin_index'))->class('form-inline margin-mobile')->attribute('name', 'issue')->open() }}
			<div class="form-group">
				{{ html()->select('group', array('0' => '-- All Groups --') + App\Models\Group::withTrashed()->orderBy('name')->pluck('name', 'id')->toArray())->class('form-control')->attribute('onchange', 'document.issue.submit()') }}
			</div>
			<div class="form-group">
				{{ html()->select('vote_status', $vote_status_select)->class('form-control')->attribute('onchange', 'document.issue.submit()') }}
			</div>
			<div class="form-group">
				{{ html()->select('payment_status', $payment_status_select)->class('form-control')->attribute('onchange', 'document.issue.submit()') }}
			</div>
			<div class="form-group">
				{{ html()->select('sort', array('default' => 'Default Sort', 'title' => 'Title (A-Z)', 'deadline' => 'Expires', 'recent' => 'Recent Activity'))->class('form-control')->attribute('onchange', 'document.issue.submit()') }}
			</div>
			<div class="form-group">
				<div class="input-group">
				  <!-- <input type="text" class="form-control cust-form1" placeholder="" name=""> -->
				  {{ html()->text('search')->class('form-control cust-form1')->placeholder('Search Title') }}
				  <div class="input-group-btn">
					<button class="btn btn-success btn-success3" type="submit"><i class="glyphicon glyphicon-search"></i></button>
				  </div>
				</div>
			</div>
		{{ html()->closeModelForm() }}
		</div></div>
		</div>
	</div>

	<div class="container welcome-content no-padding">
		<div class="col-xs-12 clearfix">{{ $issues->links() }}</div>
		<div class="col-xs-12 discuss-container no-padding">
		<!--main contain part -->
		<div class="col-lg-12 col-sm-12 mobilescroll col-xs-12 no-padding">
			<table class="table table-responsive table-condensed">
				<thead class="tdclass">
					<tr >
						<th>GROUP</th>
						<th>TITLE</th>
						<th>AMOUNT</th>
						<th>EXPIRES</th>
						<th>VOTE STATUS</th>
						<th>PMT STATUS</th>
						<th>RECIPIENT</th>
						<th></th>
					</tr>
				</thead>
				<tbody>

				@forelse($issues as $issue)
					<tr>
						<td>{{ $issue->discussion->group->name }}</td>
						<td><a href="{{ URL::route('issue.show',array('issue'=>$issue->id)) }}" target="cc_issue_admin">{{ $issue->title }}</a></td>
						<td>{{ $issue->proposal->amount }}</td>
						<td><span title="{{ $issue->proposal->deadline_local.' '.$issue->proposal->deadline_local->tzName }}">{{ $issue->proposal->deadline_c->toDateString() }}</span></td>
						<td>{{ Str::studly($issue->proposal->vote_status) }}</td>
						<td>
							@if($issue->proposal->payment_status == 'awaiting_approval')
								@if(!$issue->proposal->voting_closed)
								Awaiting Approval
								@endif
							@else
							{{ html()->form('POST', route('issue.admin_update', $issue->id))->attribute('name', 'issue' . $issue->id)->open() }}
								{{ html()->select('payment_status', App\Models\Proposal::$payment_status_select, $issue->proposal->payment_status)->attribute('onchange', 'document.issue' . $issue->id . '.submit()') }}
							{{ html()->form()->close() }}
							@endif
						</td>
						<td>
							@if(isset($issue->recipient))
								{{ $issue->recipient->firstname }} {{ $issue->recipient->lastname }}
							@endif
						</td>
						<td>
							@if($issue->trashed())
								<a href="{{ URL::route('issue.admin_restore',array('id'=>$issue->id)) }}" title="Restore Request">
								<span style="color:red" title="Restore Request"><i class="glyphicon glyphicon-trash"></i></span>
								</a>
							@else
								<a href="{{ URL::route('issue.admin_delete',array('id'=>$issue->id)) }}" title="Delete Request">
								<i class="glyphicon glyphicon-trash"></i>
								</a>
							@endif
						</td>
					</tr>
				@empty
					<tr><td colspan="6">No matching requests.</td></tr>
				@endforelse

				</tbody>
			</table>
			</container>
			<div class="clearfix"></div>
		</div>
		</div>
	</div>
</section>
@stop

@section('scripts')
<script src="{{ asset('/js/bootstrap-toggle.js') }}"></script>
<script src="{{ asset('/js/jquery-ui.js') }}"></script>
<script src="{{ asset('/js/selectBoxIt.js') }}"></script>
<script>
/*
	$("select").selectBoxIt({
		autoWidth: false,
	});
*/
</script>
@stop