<!-- =================== REQUEST INFO =================== -->
@inject('locationHelper', 'App\Models\LocationHelper')

		<div class="form-group cust-group ">
			<label for="input" class="col-sm-3 col-md-2 col-lg-2 control-label infoimg"><span>Request Title:</span></label>
			<div class="col-sm-4">
				<!-- <input type="text" class="form-control cust-form" id="input" placeholder=""> -->
				@error('title')
					<span class="error">{{ $message }}</span>
				@enderror
				{{ html()->text('title')->class('form-control cust-form')->id('input')->placeholder('') }}
			</div>
		</div>
		<div class="form-group cust-group ">
			<label for="input" class="col-sm-3 col-md-2 col-lg-2 control-label infoimg"><span>Description:</span></label>
			<div class="col-sm-7">
				<!-- <textarea class="form-control cust-form" rows="4" placeholder="Enter a detailed description of the need request here. Summarize the reason for the need, your relationship to the recipient and what impact you think the need would make"></textarea> -->
				@error('description')
					<span class="error">{{ $message }}</span>
				@enderror
				{{ html()->textarea('description')->rows('6')->class('form-control cust-form')->placeholder('Enter a detailed description of the need request here. Summarize the reason for the need, your relationship to the recipient and what impact you think the need would make.') }}
			</div>
		</div>
		<div class="form-group cust-group ">
			<label for="input" class="col-sm-3 col-md-2 col-lg-2 control-label infoimg"><span>Request Total:</span></label>
			<div class="col-sm-4">
				<!-- <input type="text" class="form-control cust-form" id="input" placeholder="$"> -->
				@error('amount')
					<span class="error">{{ $message }}</span>
				@enderror
				{{ html()->text('proposal[amount]')->class('form-control cust-form')->id('input')->placeholder('') }}
			</div>
		</div>
		<div class="form-group cust-group">
			<label for="input" class="col-sm-3 col-md-2 col-lg-2 control-label infoimg">
			@if(isset($issue))
			<span>Extend Deadline:</span>
			@else
			<span>Deadline:</span>
			@endif
            <a href="#" data-toggle="modal" data-target=".bs-example-modal-sm7"><img src="/images/infoicon.png"></a></label>
			<div class="modal fade bs-example-modal-sm7" tabindex="-1" role="dialog" aria-labelledby="mySmallModalLabel" aria-hidden="true">
				<div class="modal-dialog modal-sm7">
					<div class="modal-content">
						<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
						<div class="shared-people welcome-content  clearfix">
							<span><blockquote>NOTE</blockquote></span>
							<P>If you do not have a specific deadine for the need, a good time period is 2 weeks to give group members time to respond.</P>
						</div>
					</div>
				</div>
			</div>

			<div class="col-sm-4">
				@error('extend_deadline')
					<span class="error">{{ $message }}</span>
				@enderror
				{{ html()->select(
					'proposal[extend_deadline]',
					$deadline_choices,
					array('class'=>'form-control cust-form', 'id'=>'input')
				  )->class('form-control cust-form')->id('input') }}
			</div>
		</div>
		<div class="form-group cust-group">
			<label for="input" class="col-sm-3 col-md-2 col-lg-2 control-label infoimg"><span>Category:</span></label>
			<div class="col-sm-4">
				<!--
				<select class="basic-example">
					<option value="option1">Philanthropy</option>
					<option value="option2"> Philanthropy</option>
					<option value="option1"> Philanthropy</option>
				</select>
				-->
				@error('issue_category_id')
					<span class="error">{{ $message }}</span>
				@enderror
				{{ html()->select('issue_category_id', App\Models\IssueCategory::select_options('Please Select'))->id('combobox') }}
			</div>
		</div>
		<div class="form-group cust-group">


			<label for="input" class="col-sm-3 col-md-2 col-lg-2 control-label infoimg"><span>Type of Request:</span></label>
			<div class="col-sm-4">
				<!--
				<select class="basic-example">
					<option value="option1">Financial Assistance</option>
					<option value="option2"> Financial Assistance</option>
					<option value="option1"> Financial Assistance</option>
				</select>
				-->
				@error('issue_type_id')
					<span class="error">{{ $message }}</span>
				@enderror
				{{ html()->select('issue_type_id', App\Models\IssueType::select_options('Please Select'))->id('combobox1') }}
			</div>
		</div>
<br /><br />
<!-- =================== RECIPIENT INFO =================== -->
		<div class="col-xs-12 no-padding">
			<h3><a name="recipient_info">RECIPIENT INFO: <small>Who are we helping?</small></a></h3>
		</div>
		<div class="form-group cust-group">
			<label for="input" class="col-sm-3 col-md-2 col-lg-2 control-label infoimg"><span>First Name:</span></label>
			<div class="col-sm-4">
				<!-- <input type="text" class="form-control cust-form" id="input" placeholder=""> -->
				@error('firstname')
					<span class="error">{{ $message }}</span>
				@enderror
				{{ html()->text('recipient[firstname]')->class('form-control cust-form')->id('recipient_firstname') }}
			</div>
		</div>
		<div class="form-group cust-group">
			<label for="input" class="col-sm-3 col-md-2 col-lg-2 control-label infoimg"><span>Last Name:</span></label>
			<div class="col-sm-4">
				<!-- <input type="text" class="form-control cust-form" id="input" placeholder=""> -->
				@error('lastname')
					<span class="error">{{ $message }}</span>
				@enderror
				{{ html()->text('recipient[lastname]')->class('form-control cust-form')->id('recipient_lastname') }}
			</div>
		</div>
		<div class="form-group cust-group">
			<label for="input" class="col-sm-3 col-md-2 col-lg-2 control-label infoimg"><span>Street Address:</span></label>
			<div class="col-sm-4">
				<!-- <input type="text" class="form-control cust-group cust-form" id="input" placeholder=""> -->
				@error('addr1')
					<span class="error">{{ $message }}</span>
				@enderror
				{{ html()->text('recipient[address][addr1]')->class('form-control cust-group cust-form')->id('recipient_address_addr1') }}
				<!-- <input type="text" class="form-control cust-form" id="input" placeholder=""> -->
				{{ html()->text('recipient[address][addr2]')->class('form-control cust-form')->id('recipient_address_addr2') }}
			</div>
		</div>
		<div class="form-group cust-group">
			<label for="input" class="col-sm-3 col-md-2 col-lg-2 control-label infoimg"> <span>City:</span></label>
			<div class="col-sm-4">
				<!-- <input type="text" class="form-control cust-form" id="input" placeholder=""> -->
				@error('city')
					<span class="error">{{ $message }}</span>
				@enderror
				{{ html()->text('recipient[address][city]')->class('form-control cust-form')->id('recipient_address_city') }}
			</div>
		</div>
		<div class="form-group cust-group">
			<label for="combobox3" class="col-sm-3 col-md-2 col-lg-2 control-label infoimg"> <span>State:</span></label>
			<div class="col-sm-4">
				<!-- <select class="basic-example">
					<option value="option1"> Not in the United States</option>
					<option value="option2"> Not in the United States</option>
					<option value="option1"> Not in the United States</option>
				</select> -->
				@error('state')
					<span class="error">{{ $message }}</span>
				@enderror
				{{ html()->select('recipient[address][state]', $locationHelper->subdivisionsForSelect('US'))->id('recipient_address_state') }}
			</div>
		</div>
		<div class="form-group cust-group">
			<label for="input" class="col-sm-3 col-md-2 col-lg-2 control-label infoimg"> <span>Postal Code:</span></label>
			<div class="col-sm-4">
				<!-- <input type="text" class="form-control cust-form" id="input" placeholder=""> -->
				@error('postcode')
					<span class="error">{{ $message }}</span>
				@enderror
				{{ html()->text('recipient[address][postcode]')->class('form-control cust-form')->id('recipient_address_postcode')->placeholder('Postal Code') }}
			</div>
		</div>
		<div class="form-group cust-group">
			<label for="combobox2" class="col-sm-3 col-md-2 col-lg-2 control-label infoimg"> <span>Country:</span></label>
			<div class="col-sm-4">
				<!-- <select class="basic-example">
					<option value="option1">USA</option>
					<option value="option2"> USA</option>
					<option value="option1"> USA</option>
				</select> -->
				@error('country')
					<span class="error">{{ $message }}</span>
				@enderror
				{{ html()->select('recipient[address][country]', $locationHelper->countriesForSelect())->id('recipient_address_country') }}
			</div>
		</div>
		<div class="form-group cust-group">
			<label for="input" class="col-sm-3 col-md-2 col-lg-2 control-label infoimg"> <span>Phone:</span></label>
			<div class="col-sm-4">
				<!-- <input type="text" class="form-control cust-form" id="input" placeholder=""> -->
				@error('phone')
					<span class="error">{{ $message }}</span>
				@enderror
				{{ html()->text('recipient[phone]')->class('form-control cust-form')->id('recipient_phone')->placeholder('Phone Number') }}
			</div>
		</div>
		<div class="form-group cust-group">
			<label for="input" class="col-sm-3 col-md-2 col-lg-2 control-label infoimg"> <span>Email:</span></label>
			<div class="col-sm-4">
				<!-- <input type="text" class="form-control cust-form" id="input" placeholder=""> -->
				@error('email')
					<span class="error">{{ $message }}</span>
				@enderror
				{{ html()->text('recipient[email]')->class('form-control cust-form')->id('recipient_email')->placeholder('Email Address') }}
			</div>
		</div>

<br /><br />

<!-- =================== PAYMENT INFO =================== -->
{{ ($errors_p = Session::has('errors_p') ? Session::get('errors_p') : new Illuminate\Support\MessageBag()) ? '' : '' }}
		<div class="col-xs-12 no-padding">
			<h3><a name="payment_info"></a>PAYMENT INFO: <small>Who will receive the money? <a id="copy_values">Copy Values from Recipient</a></small></h3>
		</div>
		<div class="form-group cust-group">
			<label for="payinfo_firstname" class="col-sm-3 col-md-2 col-lg-2 control-label infoimg"><span>First Name:</span></label>
			<div class="col-sm-4">
				<!-- <input type="text" class="form-control cust-form" id="input" placeholder=""> -->
				{{ $errors_p->first('firstname', '<span class="error">:message</span>') }}
				{{ html()->text('proposal[pay_info][firstname]')->class('form-control cust-form')->id('payinfo_firstname') }}
			</div>
		</div>
		<div class="form-group cust-group">
			<label for="payinfo_lastname" class="col-sm-3 col-md-2 col-lg-2 control-label infoimg"><span>Last Name:</span></label>
			<div class="col-sm-4">
				<!-- <input type="text" class="form-control cust-form" id="input" placeholder=""> -->
				{{ $errors_p->first('lastname', '<span class="error">:message</span>') }}
				{{ html()->text('proposal[pay_info][lastname]')->class('form-control cust-form')->id('payinfo_lastname') }}
			</div>
		</div>
		<div class="form-group cust-group">
			<label for="payinfo_address_addr1" class="col-sm-3 col-md-2 col-lg-2 control-label infoimg"><span>Street Address:</span></label>
			<div class="col-sm-4">
				<!-- <input type="text" class="form-control cust-group cust-form" id="input" placeholder=""> -->
				{{ $errors_p->first('addr1', '<span class="error">:message</span>') }}
				{{ html()->text('proposal[pay_info][address][addr1]')->class('form-control cust-group cust-form')->id('payinfo_address_addr1') }}
				<!-- <input type="text" class="form-control cust-form" id="input" placeholder=""> -->
				{{ html()->text('proposal[pay_info][address][addr2]')->class('form-control cust-form')->id('payinfo_address_addr2') }}
			</div>
		</div>
		<div class="form-group cust-group">
			<label for="payinfo_address_city" class="col-sm-3 col-md-2 col-lg-2 control-label infoimg"> <span>City:</span></label>
			<div class="col-sm-4">
				<!-- <input type="text" class="form-control cust-form" id="input" placeholder=""> -->
				{{ $errors_p->first('city', '<span class="error">:message</span>') }}
				{{ html()->text('proposal[pay_info][address][city]')->class('form-control cust-form')->id('payinfo_address_city') }}
			</div>
		</div>
		<div class="form-group cust-group">
			<label for="payinfo_address_state" class="col-sm-3 col-md-2 col-lg-2 control-label infoimg"> <span>State:</span></label>
			<div class="col-sm-4">
				<!-- <select class="basic-example">
					<option value="option1"> Not in the United States</option>
					<option value="option2"> Not in the United States</option>
					<option value="option1"> Not in the United States</option>
				</select> -->
				{{ $errors_p->first('state', '<span class="error">:message</span>') }}
				{{ html()->select('proposal[pay_info][address][state]', $locationHelper->subdivisionsForSelect('US'))->id('payinfo_address_state') }}
			</div>
		</div>
		<div class="form-group cust-group">
			<label for="payinfo_address_postcode" class="col-sm-3 col-md-2 col-lg-2 control-label infoimg"> <span>Postal Code:</span></label>
			<div class="col-sm-4">
				<!-- <input type="text" class="form-control cust-form" id="input" placeholder=""> -->
				{{ $errors_p->first('postcode', '<span class="error">:message</span>') }}
				{{ html()->text('proposal[pay_info][address][postcode]')->class('form-control cust-form')->id('payinfo_address_postcode')->placeholder('Postal Code') }}
			</div>
		</div>
		<div class="form-group cust-group">
			<label for="payinfo_address_country" class="col-sm-3 col-md-2 col-lg-2 control-label infoimg"> <span>Country:</span></label>
			<div class="col-sm-4">
				<!-- <select class="basic-example">
					<option value="option1">USA</option>
					<option value="option2"> USA</option>
					<option value="option1"> USA</option>
				</select> -->
				{{ $errors_p->first('country', '<span class="error">:message</span>') }}
				{{ html()->select('proposal[pay_info][address][country]', $locationHelper->countriesForSelect())->id('payinfo_address_country') }}
			</div>
		</div>
		<div class="form-group cust-group">
			<label for="payinfo_phone" class="col-sm-3 col-md-2 col-lg-2 control-label infoimg"> <span>Phone:</span></label>
			<div class="col-sm-4">
				<!-- <input type="text" class="form-control cust-form" id="input" placeholder=""> -->
				{{ $errors_p->first('phone', '<span class="error">:message</span>') }}
				{{ html()->text('proposal[pay_info][phone]')->class('form-control cust-form')->id('payinfo_phone')->placeholder('Phone Number') }}
			</div>
		</div>
		<div class="form-group cust-group">
			<label for="payinfo_email" class="col-sm-3 col-md-2 col-lg-2 control-label infoimg"> <span>Email:</span></label>
			<div class="col-sm-4">
				<!-- <input type="text" class="form-control cust-form" id="input" placeholder=""> -->
				{{ $errors_p->first('email', '<span class="error">:message</span>') }}
				{{ html()->text('proposal[pay_info][email]')->class('form-control cust-form')->id('payinfo_email')->placeholder('Email Address') }}
			</div>
		</div>
		<div class="form-group cust-group">
			<label for="payinfo_notes" class="col-sm-3 col-md-2 col-lg-2 control-label infoimg"> <span>Additional notes:</span></label>
			<div class="col-sm-4">
				<!-- <input type="text" class="form-control cust-form" id="input" placeholder=""> -->
				{{ $errors_p->first('notes', '<span class="error">:message</span>') }}
				{{ html()->textarea('proposal[pay_info][notes]')->rows('4')->class('form-control cust-form')->placeholder("Notes")->id('payinfo_notes') }}
			</div>
		</div>

		<br />

		<div class="form-group cust-group">
			<div class="clearfix"></div>
			<label for="payinfo_payment_type" class="col-sm-3 col-md-2 col-lg-2 control-label infoimg"> <span>Payment Type:</span></label>
			<div class="col-sm-4">
				<!-- <select class="basic-example">
					<option value="option1">USA</option>
					<option value="option2"> USA</option>
					<option value="option1"> USA</option>
				</select> -->
				{{ $errors_p->first('payment_type', '<span class="error">:message</span>') }}
				{{ html()->select('proposal[pay_info][payment_type]', array('check' => 'Check / Cheque (US / UK Groups)', 'paypal' => ' Zelle (US Groups)', 'wire' => 'EFT / Bank Wire (fees apply) (ZA & US Groups)', 'uk_payment' => 'Direct Bank Transfer (UK Groups)'))->id('payinfo_payment_type') }}
			</div>
		</div>

		<div id="uk_payment" name="uk_payment" style="display:none">
			<div class="form-group cust-group">
				<label for="payinfo_uk_bank" class="col-sm-3 col-md-2 col-lg-2 control-label infoimg"> <span>Bank Name:</span></label>
				<div class="col-sm-4">
					{{ $errors_p->first('uk_bank', '<span class="error">:message</span>') }}
					{{ html()->text('proposal[pay_info][uk_bank]')->class('form-control cust-form')->id('payinfo_uk_bank')->placeholder('Bank Name') }}
				</div>
			</div>
			<div class="form-group cust-group">
				<label for="payinfo_uk_sortcode" class="col-sm-3 col-md-2 col-lg-2 control-label infoimg"> <span>Sort Code:</span></label>
				<div class="col-sm-4">
					{{ $errors_p->first('uk_sortcode', '<span class="error">:message</span>') }}
					{{ html()->text('proposal[pay_info][uk_sortcode]')->class('form-control cust-form')->id('payinfo_uk_sortcode')->placeholder('Sort Code (include dashes)') }}
				</div>
			</div>
			<div class="form-group cust-group">
				<label for="payinfo_uk_account" class="col-sm-3 col-md-2 col-lg-2 control-label infoimg"> <span>Account Number:</span></label>
				<div class="col-sm-4">
					{{ $errors_p->first('uk_account', '<span class="error">:message</span>') }}
					{{ html()->text('proposal[pay_info][uk_account]')->class('form-control cust-form')->id('payinfo_uk_account')->placeholder('Bank Account Number') }}
				</div>
			</div>
			<div class="form-group cust-group">
				<label for="payinfo_uk_account_name" class="col-sm-3 col-md-2 col-lg-2 control-label infoimg"> <span>Name on Account:</span></label>
				<div class="col-sm-4">
					{{ $errors_p->first('uk_account_name', '<span class="error">:message</span>') }}
					{{ html()->text('proposal[pay_info][uk_account_name]')->class('form-control cust-form')->id('payinfo_uk_account_name')->placeholder('Name on Account') }}
				</div>
			</div>
		</div>

		<div id="paypal" name="paypal" style="display:none">
			<div class="form-group cust-group">
				<label for="payinfo_paypal_email" class="col-sm-3 col-md-2 col-lg-2 control-label infoimg"> <span>Zelle:</span></label>
				<div class="col-sm-4">
					<!-- <input type="text" class="form-control cust-form" id="input" placeholder=""> -->
					{{ $errors_p->first('paypal_email', '<span class="error">:message</span>') }}
					{{ html()->text('proposal[pay_info][paypal_email]')->class('form-control cust-form')->id('payinfo_paypal_email')->placeholder('Zelle Email') }}
				</div>
			</div>
		</div>

		<div id="wire" style="display:none">
			<div class="form-group cust-group">
				<label for="payinfo_wire_bank_name" class="col-sm-3 col-md-2 col-lg-2 control-label infoimg"> <span>Bank Name:</span></label>
				<div class="col-sm-4">
					<!-- <input type="text" class="form-control cust-form" id="input" placeholder=""> -->
					{{ $errors_p->first('email', '<span class="error">:message</span>') }}
					{{ html()->text('proposal[pay_info][wire_bank_name]')->class('form-control cust-form')->id('payinfo_wire_bank_name')->placeholder('Bank Name') }}
				</div>
			</div>
			<div class="form-group cust-group">
				<label for="payinfo_wire_bank_routing" class="col-sm-3 col-md-2 col-lg-2 control-label infoimg"> <span>Bank Routing number / Swift code:</span></label>
				<div class="col-sm-4">
					<!-- <input type="text" class="form-control cust-form" id="input" placeholder=""> -->
					{{ $errors_p->first('email', '<span class="error">:message</span>') }}
					{{ html()->text('proposal[pay_info][wire_bank_routing]')->class('form-control cust-form')->id('payinfo_wire_bank_routing')->placeholder('Routing number / Swift code') }}
				</div>
			</div>
			<div class="form-group cust-group">
				<label for="payinfo_wire_bank_address" class="col-sm-3 col-md-2 col-lg-2 control-label infoimg"> <span>Bank Address:</span></label>
				<div class="col-sm-4">
					<!-- <input type="text" class="form-control cust-form" id="input" placeholder=""> -->
					{{ $errors_p->first('email', '<span class="error">:message</span>') }}
					{{ html()->textarea('proposal[pay_info][wire_bank_address]')->rows('3')->class('form-control cust-form')->placeholder("Bank's Address")->id('payinfo_wire_bank_address') }}
				</div>
			</div>
			<div class="form-group cust-group">
				<label for="payinfo_wire_acct_number" class="col-sm-3 col-md-2 col-lg-2 control-label infoimg"> <span>Account Number:</span></label>
				<div class="col-sm-4">
					<!-- <input type="text" class="form-control cust-form" id="input" placeholder=""> -->
					{{ $errors_p->first('email', '<span class="error">:message</span>') }}
					{{ html()->text('proposal[pay_info][wire_acct_number]')->class('form-control cust-form')->id('payinfo_wire_acct_number')->placeholder('Account Number') }}
				</div>
			</div>
		</div>
