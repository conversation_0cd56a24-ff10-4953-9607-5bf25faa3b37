@extends('layouts.tabs')
@inject('navBar', 'App\Models\NavBar')

@section('title')
	Requests for {{ $group->name }}
@stop

@section('head')
<link type="text/css" rel="stylesheet" href="{{ asset('/css/ui1.css?version=1') }}" media="all">
<link type="text/css" rel="stylesheet" href="{{ asset('/css/welcomenav.css?version=2') }}" media="all">
<link type="text/css" rel="stylesheet" href="{{ asset('/css/bootstrap-datetimepicker.css?version=1') }}" media="all">
<link type="text/css" rel="stylesheet" href="{{ asset('/css/selectboxit.css?version=1') }}" media="all">
{{ $navBar->setActive('issue') }}
@stop

@section('home-content')
	<span>Requests for {{ $group->name }}</span>
@stop

@section('tabs-content')
	@include('common.group_tabs',array('route'=>'group.issues','current_group'=>$group))
@stop

@section('content')
<div class="col-lg-12 welcome-content clearfix">
	@if($group->canCreateRequest(Auth::User()))
		<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 no-padding contain-margin">
			<a href="{{ URL::route('group.create_issue',array('id'=>$group->id)) }}" title="Submit a need request" class="btn pull-right btn-success small-button " role="button">
				<span><img src="/images/button-three.png"></span>SUBMIT A NEED REQUEST
			</a>
		</div>
	@endif
	<div class="col-lg-12 col-sm-12 col-xs-12 no-padding clearfix tablebg">
		<div class="col-lg-12 col-sm-12  col-xs-12 datemargin">
	{{ html()->modelForm($input, 'GET', route('group.issues', $group->id))->class('form-horizontal margin-mobile')->attribute('name', 'discuss')->open() }}
	  <div class="col-lg-7 col-sm-5  col-md-7 col-xs-12 no-padding centerposition clearfix ">
		<!-- <form class="form-horizontal margin-mobile"> -->
		  <div class="control-group">
			<div class="controls form-inline">
				<label for="from_date">Date:</label>
				<!-- <input type="text" value="" name="from_date" id="from_date" readonly class="form-control cust-width cust-form form_datetime"> -->
				{{ html()->text('from_date')->id('from_date')->class('form-control cust-width cust-form form_datetime') }}
				<label for="to_date">to</label>
				<!-- <input type="text" value="" name="to_date" id="to_date" readonly class="form-control cust-width cust-form form_datetime"> -->
				{{ html()->text('to_date')->id('to_date')->class('form-control cust-width cust-form form_datetime') }}
				<!-- <a href="#" title="" class="btn btn-success small-button " role="button"> FILTER</a> -->
				<button type="submit" class="btn btn-success small-button">FILTER</button>
			</div>
		</div>
	  </div>
	  <div class="col-lg-5 col-sm-7  col-md-5 col-xs-12 centerposition ">
		<div class="col-lg-6 col-sm-6  col-md-6 col-xs-12">
			<!-- <div class="ui-widget"> -->
				<!--
				<select name="sort" id="combobox">
					<option value="">Select one...</option>
					<option value="option2"> Default</option>
					<option value="option3">Alphabetical (A-Z)</option>
					<option value="option4">Alphabetical (Z-A)</option>
					<option value="option5">State</option>
					<option value="option5">Country</option>
					<option value="option5">Recent (??)</option>
				</select>
				-->
				{{ html()->select('sort', array('newest' => 'Newest', 'oldest' => 'Oldest', 'alpha' => 'Alphabetical (A-Z)', 'revalpha' => 'Alphabetical (Z-A)', 'state' => 'State', 'country' => 'Country'))->attribute('onchange', 'document.discuss.submit()') }}
			<!-- </div> -->
		</div>
		<div class="col-lg-6 col-sm-6  col-md-6 col-xs-12 no-padding ">
		  <!-- <form class="navbar-form no-margin" role="search"> -->
			<div class="input-group">
			  <!-- <input type="text" class="form-control cust-form" placeholder="Search" name="q"> -->
			  {{ html()->text('search')->class('form-control cust-form')->placeholder('Search') }}
			  <div class="input-group-btn">
				<button class="btn but1-margin btn-success btn-default" type="submit"><i class="glyphicon glyphicon-search"></i></button>
			  </div>
			</div>
		  <!-- </form> -->
		</div>
	  </div>
	{{ html()->closeModelForm() }}
		</div>
		<div class="col-xs-12 clearfix">{{ $issues->links() }}</div>
		<!-- start of table -->
		<div class="col-xs-12 mobilescroll no-padding">
			<table class="table table-responsive table-condensed ">
				<thead class="tdclass">
					<tr >
						<th>BY</th>
						<th>TITLE</th>
						<th>TIME LEFT</th>
						<th>VOTE STATUS</th>
						<th>GIFT STATUS</th>
						<th>READ?</th>
						<th>WEIGHED IN?</th>
					</tr>
				</thead>
				<tbody>

				@forelse($issues as $issue)
					<tr>
						<th><span class="tinypix" title="{{$issue->creator->firstname}} {{$issue->creator->lastname}}" style="background-image: url('{{ $issue->creator->profile_pic }}')"></span></th>
						<td><a href="{{ URL::route('issue.show',array('issue'=>$issue->id)) }}">{{ $issue->title }}</a></td>
						<td title="{{ $issue->proposal->deadline_local.' '.$issue->proposal->deadline_local->tzName }}">{{ $issue->proposal->deadline_c->diffForHumans() }}</td>
						<td>{{ Str::studly($issue->proposal->vote_status) }}</td>
						<td>
							@if($issue->proposal->payment_status != 'awaiting_approval')
							{{ ucwords(preg_replace('/_/',' ',$issue->proposal->payment_status)) }}
							@endif
						</td>
						@if ($issue->is_unread)
							<td class="worning tdimg">Unread</td>
						@else
							<td>Read</td>
						@endif
						@if ($issue->proposal->has_voted(Auth::User()))
							<td>Weighed In</td>
						@else
							@if(!$issue->proposal->voting_closed)
								<td class="worning tdimg">Need Vote</td>
							@else
								<td></td>
							@endif
						@endif
					</tr>
				@empty
					<tr><td colspan="6">No requests have been created.</td></tr>
				@endforelse

				</tbody>
			</table>
		</div>
		<!-- end of table -->
	</div>
</div>
@stop

@section('scripts')
<script src="{{ asset('/js/jquery-ui.js') }}"></script>
<script src="{{ asset('/js/bootstrap-toggle.js') }}"></script>
<script src="{{ asset('/js/bootstrap-datetimepicker.min.js') }}"></script>
<script type="text/javascript">
$(document).ready(function() {
	$(".form_datetime").datetimepicker({ format: 'yyyy-mm-dd', pickTime: false });
});
</script>
<script src="{{ asset('/js/selectBoxIt.js') }}"></script>
<script>
	console.log('did it');
	$("select").selectBoxIt({
		autoWidth: false
	});
</script>
@stop