@extends('layouts.master')
@inject('locationHelper', 'App\Models\LocationHelper')

@section('title')
	View Request
@stop

@section('head')
<link type="text/css" rel="stylesheet" href="{{ asset('/css/dropdown.css?version=1') }}" media="all">
<link type="text/css" rel="stylesheet" href="{{ asset('/css/welcomenav.css?version=2') }}" media="all">
@stop

@section('home-content')
	<span>REQUEST</span>
@stop

@section('content')
<div class="col-xs-12 welcome-content contain-margin clearfix">
	<div class="col-xs-12 needs-content clearfix">
		<h2>{{ $issue->title }}
		@if($issue->canEdit(Auth::User()))&nbsp;&nbsp;&nbsp;<small><a href="{{ URL::route('issue.edit',$issue->id) }}">Edit Details</a></small> @endif
		</h2>

		@if(Auth::User()->is_admin)
			<div title="{{ $issue->creator->email }}" onclick="copyText('{{ $issue->creator->email }}')">
				Submitted by {{ $issue->creator->firstname }} {{ $issue->creator->lastname }}
				<i
					class="glyphicon glyphicon-envelope"
					style="cursor:pointer;"
					title="Click to copy email address"
				></i>
			</div>
			<script>
				function copyText(text) {
					if (window.isSecureContext && navigator.clipboard) {
						navigator.clipboard.writeText(text).then(
							function() {
								console.log('Copied to clipboard');
							},
							function(err) {
								console.error('Failed to copy: ', err);
							}
						)
					} else {
						console.log(text);
					}
				}
			</script>
		@else
			<div>Submitted by {{ $issue->creator->firstname }} {{ $issue->creator->lastname }}</div>
		@endif

		<hr>
		<h6>DESCRIPTION</h6>
		<p>{!! $issue->niceDescription() !!}</p>
		<div class="col-lg-5 col-sm-7 col-xs-12 no-padding ">
			<!-- start form part -->
			<form class="form-horizontal needs-group" role="form">
				<div class="form-group">
					<div class="col-sm-5 col-md-6 col-xs-7 no-padding">
						<p class="help-block"> <img src="/images/total.png">Request Total:</p>
					</div>
					<span> {{$issue->discussion->group->currency}} {{ number_format($proposal->amount, 2, '.', ',') }}</span>
				</div>
				<div class="form-group">
					<div class="col-sm-5 col-xs-6 col-md-6 no-padding">
						<p class="help-block"> <img src="/images/deadlineicon.png">Deadline:</p>
					</div>
					<span title="{{ $proposal->deadline_local.' '.$proposal->deadline_local->tzName }}">{{ $proposal->deadline_local->toFormattedDateString() }}</span>
				</div>
				<div class="form-group">
					<div class="col-sm-5 col-xs-8 col-md-6 no-padding">
						<p class="help-block"> <img src="/images/time.png">Time Remaining:</p>
					</div>
					<span title="{{ $proposal->deadline_local.' '.$proposal->deadline_local->tzName }}">{{ ($proposal->deadline_c->isPast()) ? 'Past Deadline' : $proposal->deadline_c->diffForHumans() }}</span>
				</div>
				<div class="form-group">
					<div class="col-sm-5 col-xs-7 col-md-6 no-padding">
						<p class="help-block"> <img src="/images/status.png">Status:</p>
					</div>
					<span>{{ ucwords(preg_replace('/_/',' ',$proposal->payment_status)) }}</span>
				</div>
				<div class="form-group">
					<div class="col-sm-5 col-xs-6 col-md-6 no-padding">
						<p class="help-block"> <img src="/images/category.png">Category:</p>
					</div>
					<span>{{ $issue->issue_category->description }}</span>
				</div>
				<div class="form-group">
					<div class="col-sm-5 col-xs-8 col-md-6 no-padding">
						<p class="help-block"> <img src="/images/type.png">Type of Request: </p>
					</div>
					<SPAN>{{ $issue->issue_type->description }}</SPAN>
				</div>
				<!-- Not implemented yet:
				<div class="form-group">
					<div class="col-sm-5 col-xs-8 col-md-6 no-padding">
						<p class="help-block"> <img src="/images/payment27.png">Payment Info:</p>
					</div>
					<SPAN>Check To Recipient</SPAN>
				</div>
				-->
			</form>
			<!-- End of form part -->
		</div>
		<div class="col-lg-4 col-sm-5 col-xs-12 no-padding ">
			<!-- start form part -->
			<form class="form-horizontal needs-group" role="form">
				<span>Recipient Information:</span>
				@if(isset($issue->recipient))
					@if($issue->canEdit(Auth::User()))
						<div class="form-group">
							<div class="col-sm-4 col-md-5 col-xs-6 no-padding">
								<p class="help-block">First Name:</p>
							</div>
							<span>{{ $issue->recipient->firstname }}</span>
						</div>
						<div class="form-group">
							<div class="col-sm-4 col-md-5 col-xs-6 no-padding">
								<p class="help-block">Last Name:</p>
							</div>
							<span>{{ $issue->recipient->lastname }}</span>
						</div>
						<div class="form-group">
							<div class="col-sm-4 col-md-5 col-xs-6 no-padding">
								<p class="help-block">City:</p>
							</div>
							<span>{{ $issue->recipient->address->city }}</span>
						</div>
						<div class="form-group">
							<div class="col-sm-4 col-md-5 col-xs-6 no-padding">
								<p class="help-block">State:</p>
							</div>
							<span>{{ $locationHelper->subdivisionName($issue->recipient->address->state) }}</span>
						</div>
						<div class="form-group">
							<div class="col-sm-4 col-md-5 col-xs-6 no-padding">
								<p class="help-block">Postal Code: </p>
							</div>
							<span>{{ $issue->recipient->address->postcode }}</span>
						</div>
						<div class="form-group">
							<div class="col-sm-4 col-md-5 col-xs-6 no-padding">
								<p class="help-block">Country: </p>
							</div>
							<span>{{ $locationHelper->countryName($issue->recipient->address->country) }}</span>
						</div>
					@else
						<div class="form-group">
							<div class="col-sm-4 col-md-5 col-xs-6 no-padding">
								<p class="help-block">First Name:</p>
							</div>
							<span>{{ $issue->recipient->firstname }}</span>
						</div>
						<div class="form-group">
							<div class="col-sm-4 col-md-5 col-xs-6 no-padding">
								<p class="help-block">Last Name:</p>
							</div>
							<span>{{ $issue->recipient->lastname }}</span>
						</div>
					@endif
				@else
					<div class="form-group">
						<div class="col-sm-4 col-md-5 col-xs-6 no-padding">
							<p class="help-block">To be defined</p>
						</div>
						<span></span>
					</div>
				@endif
			</form>
			<!-- End of form part -->
		</div>
	</div>
	<!-- voting part start -->
	<div class="col-xs-12 group-description no-padding need-top clearfix ">

		<!-- pari 1 start -->
		<div class="col-lg-6 col-md-6 col-xs-12  padd-one">
			<div class="needs-content cast clearfix">
				@if($proposal->my_vote(Auth::User()) === null)
					<h3>Cast Your Vote:</h3>
				@else
					<h3>Change Your Vote: <small>{{ $proposal->my_vote(Auth::User()) }}</small></h3>
				@endif
				<span> Do you support this request & want to use group money to fulfill the request?</span>
				<div class="col-lg-12 col-sm-12 col-xs-12 no-padding img-margin need-but clearfix">
					<!-- Standard button -->
					<div style="float:left;">
					{{ html()->form('POST', route('proposal.vote', [$proposal->id, 'concur']))->open() }}
						<button type="submit" class="btn butvote btn1  btn-default"> <span><img src="/images/agree.png"></span>I CONCUR</button>&nbsp;&nbsp;
					{{ html()->form()->close() }}
					</div>
					<!-- Standard button -->
					<div>
					{{ html()->form('POST', route('proposal.vote', [$proposal->id, 'open']))->open() }}
						<button type="submit" class="btn butvote btn2 btn-default"> <span><img src="/images/abstain.png"></span>I AM OPEN</button>
					{{ html()->form()->close() }}
					</div>
				</div>
				<div class="clearfix"></div>
				<div class="col-lg-12 col-sm-12 col-xs-12 need-but2 img-margin no-padding clearfix">
					<!-- Standard button -->
					<div style="float:left;">
					{{ html()->form('POST', route('proposal.vote', [$proposal->id, 'wonder']))->open() }}
						<button type="submit" class="btn butvote btn3 btn-default"> <span><img src="/images/disagree.png"></span>I WONDER</button>&nbsp;&nbsp;
					{{ html()->form()->close() }}
					</div>
					<!-- Standard button -->
					<div>
					{{ html()->form('POST', route('proposal.vote', [$proposal->id, 'disfavor']))->open() }}
						<button type="submit" class="btn butvote btn4 btn-default"> <span><img src="/images/block.png"></span>I DISFAVOR</button>
					{{ html()->form()->close() }}
					</div>
				</div>
			</div>
		</div>
		<!-- part 1 end  -->
		<!-- strat part 2 -->
		<div class="col-lg-6 col-md-6 col-xs-12  padd-three">
			<div class="needs-content voting clearfix">
				<h3>Voting:
					@if($proposal->voting_closed)
					  Closed
					  @if(!$proposal->vote_passed)
						(vote failed)
					  @endif
					@else
					  Open
				    @endif
				    @if($proposal->vote_passed)
				    	(vote passes)
			    	@endif
				</h3>
				<span></span>
				<!-- process bar  -->
				<div class="progress cust-progress2">
					<div class=" progress-bar2 " role="progressbar" aria-valuenow="{{ $currentVotes['concur'] }}" aria-valuemin="0" aria-valuemax="100" style="width: {{ $currentVotes['concur'] }}%;" data-toggle="tooltip" data-placement="top" title="" data-original-title="3"> </div>
					<div class="progress-bar progress-bar3" role="progressbar" aria-valuenow="{{ $currentVotes['open'] }}" aria-valuemin="0" aria-valuemax="100" style="width: {{ $currentVotes['open'] }}%;" data-toggle="tooltip" data-placement="top" title="" data-original-title="1"> </div>
					<div class="progress-bar progress-bar4 " role="progressbar" aria-valuenow="{{ $currentVotes['wonder'] }}" aria-valuemin="0" aria-valuemax="100" style="width: {{ $currentVotes['wonder'] }}%;" data-toggle="tooltip" data-placement="top" title="" data-original-title="2"> </div>
					<div class="progress-bar progress-bar5" role="progressbar" aria-valuenow="{{ $currentVotes['disfavor'] }}" aria-valuemin="0" aria-valuemax="100" style="width: {{ $currentVotes['disfavor'] }}%;" data-toggle="tooltip" data-placement="top" title="" data-original-title="1"> </div>
				</div>
				<!-- End of processbar -->
				<ul class="votinglist">
					<li><img src="/images/green.png"><span>{{ $tally['concur'] }} members Concur</span></li>
					<li><img src="/images/grey.png"><span>{{ $tally['open'] }} members Open</span></li>
					<li><img src="/images/pink.png"><span>{{ $tally['wonder'] }} members Wonder</span></li>
					<li><img src="/images/red.png"><span>{{ $tally['disfavor'] }} members Disfavor</span></li>
				</ul>
				<span class="text-watting" title="{{ $missingVoters }}">
					@if($tally['no_vote'])
						@if($tally['no_vote'] == 1)
							Awaiting 1 more response
						@else
							Awaiting {{ $tally['no_vote'] }} more responses
						@endif
					@endif

				</span>
				<span>
					<a href="#" data-toggle="modal" data-target=".bs-example-modal-sm7"><img src="/images/infoicon.png"></a></label>
					<div class="modal fade bs-example-modal-sm7" tabindex="-1" role="dialog" aria-labelledby="mySmallModalLabel" aria-hidden="true">
						<div class="modal-dialog modal-sm7">
							<div class="modal-content">
								<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
								<div class="shared-people welcome-content  clearfix">
									<span><blockquote>Yet to vote:</blockquote></span>
									<span><P>{{ $missingVoters }}</P></span>
								</div>
							</div>
						</div>
					</div>
				</span>
			</div>
		</div>
		<!-- End of part 2 -->
	</div>
	<!-- voting part end  -->
	<!-- start comment part -->
	<div class=" col-lg-12 col-sm-12 col-xs-12 need-bg need-top clearfix">
		<h2>Comments</h2>
		<ul class="comment-contain">
		@foreach($issue->discussion->posts as $post)
			@include('posts.display')
		@endforeach
			<li class="comment1 clearfix">
				<div class="col-lg-12 col-sm-12  col-xs-12 clearfix  no-padding">
					<div class="col-lg-1 col-sm-1 no-padding"> <span class="mediumpix" style="background-image: url('{{ Auth::User()->profile_pic }}')"></span></div>
					<div class="col-sm-8 cust-group">
						{{ html()->form('POST', route('issue.post', $issue->id))->attribute('name', 'post')->open() }}
						<!-- <input type="text" class="form-control cust-form" id="input" placeholder="Write a comment..."> -->
						<div class="col-sm-8 no-padding">{{ html()->textarea('comment')->class('form-control cust-form')->rows(3)->placeholder('Write a comment...') }}</div>
						<div class="col-sm-4"><button type="submit" class="btn butvote btn1  btn-default"> <span><img src="/images/arrowsend.png"></span>Submit</button></div>
						{{ html()->form()->close() }}
					</div>
				</div>
			</li>
			<li></li>
		</ul>
	</div>
	<!-- end of comment part -->
</div>
@stop

@section('scripts')
<script>
	$(document).ready(function() {
		$(".basic-example").heapbox();
		$('.delete_comment').click(function(event) {
		    event.preventDefault();
		    var r=confirm("Are you sure you want to delete this comment?");
		    if (r==true)   {
		       window.location = $(this).attr('href');
		    }
		});
	});
</script>
<script>
	$(function () {
		$('[data-toggle="tooltip"]').tooltip();
	});
</script>
<script src="{{ asset('/js/bootstrap-toggle.js') }}"></script>
<script src="{{ asset('/js/jquery.heapbox-0.9.4.js') }}"></script>
@stop