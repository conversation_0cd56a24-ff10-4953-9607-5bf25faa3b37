@extends('layouts.dashboard-new')

@push('styles')
<link rel="stylesheet" href="{{ asset('css/v2-requests.css') }}">
@endpush

@section('title', 'Requests for ' . $group->name)

@section('content')
<div class="v2-requests-topbar">
    <div class="v2-requests-tabs">
        <a href="#" class="v2-requests-tab active">Activity</a>
        <span class="v2-requests-tab-sep">|</span>
        <a href="#" class="v2-requests-tab">Current Requests</a>
    </div>

    @if($group->canCreateRequest(Auth::user()))
        <a href="{{ URL::route('group.create_issue', ['id' => $group->id]) }}" class="v2-btn v2-btn-primary">Post a Request</a>
    @endif
</div>
<div class="v2-requests-container">
    <div class="v2-requests-header">
        <div class="v2-requests-title">Current Requests</div>
    </div>
    <div class="v2-requests-list">
        @if($issues->count() > 0)
            @foreach($issues as $issue)
                <div class="v2-request-card">
                    <div class="v2-request-card-header">
                        <div class="v2-request-avatar">
                            @if($issue->creator && $issue->creator->profile_pic)
                                <img src="{{ $issue->creator->profile_pic }}" alt="{{ $issue->creator->firstname }} {{ $issue->creator->lastname }}">
                            @else
                                <img src="{{ asset('images/profile-placeholder.png') }}" alt="User">
                            @endif
                        </div>
                        <div class="v2-request-meta">
                            <div class="v2-request-title">{{ $issue->title }}</div>
                            <div class="v2-request-by">
                                Request by <span class="v2-request-author">{{ $issue->creator ? $issue->creator->firstname . ' ' . $issue->creator->lastname : 'Unknown User' }}</span>
                                in <span class="v2-request-group">{{ $issue->discussion->group->name ?? '' }}</span>
                                <span class="v2-request-time">{{ $issue->created_at->diffForHumans() }}</span>
                            </div>
                        </div>
                        <div class="v2-request-fav">
                            <i class="fa fa-heart v2-fav-icon"></i>
                        </div>
                    </div>
                    <div class="v2-request-card-body">
                        <div class="v2-request-amount">Request Amount: <span>${{ number_format($issue->proposal->amount ?? 0, 2) }}</span></div>
                        <div class="v2-request-description">{{ $issue->description }}</div>
                        <div class="v2-request-actions">
                            <button class="v2-action-btn v2-action-concur @if($issue->proposal->my_vote(Auth::user()) === 'concur') active @endif">
                                <i class="fa fa-check"></i> I Concur
                            </button>
                            <button class="v2-action-btn v2-action-open @if($issue->proposal->my_vote(Auth::user()) === 'open') active @endif">
                                <i class="fa fa-door-open"></i> I Am Open
                            </button>
                            <button class="v2-action-btn v2-action-wonder @if($issue->proposal->my_vote(Auth::user()) === 'wonder') active @endif">
                                <i class="fa fa-question"></i> I Wonder
                            </button>
                            <button class="v2-action-btn v2-action-disfavor @if($issue->proposal->my_vote(Auth::user()) === 'disfavor') active @endif">
                                <i class="fa fa-times"></i> I Disfavor
                            </button>
                        </div>
                        <a href="{{ URL::route('issue.show', ['issue' => $issue->id]) }}" class="v2-btn v2-btn-outline">Request Details</a>
                    </div>
                    <div class="v2-request-comments">
                        <form class="v2-comment-form" action="{{ route('issue.post', $issue->id) }}" method="POST">
                            @csrf
                            <input type="text" name="comment" class="v2-comment-input" placeholder="Type Comment...">
                            <button type="submit" class="v2-comment-send"><i class="fa fa-arrow-right"></i></button>
                        </form>
                        @if($issue->discussion && $issue->discussion->posts)
                            @foreach($issue->discussion->posts->take(1) as $post)
                                <div class="v2-comment-item">
                                    <div class="v2-comment-avatar">
                                        <img src="{{ $post->author->profile_pic ?? asset('images/profile-placeholder.png') }}" alt="{{ $post->author->firstname ?? '' }}">
                                    </div>
                                    <div class="v2-comment-content">
                                        <div class="v2-comment-author">{{ $post->author->firstname ?? '' }} {{ $post->author->lastname ?? '' }}</div>
                                        <div class="v2-comment-time">{{ $post->created_at->diffForHumans() }}</div>
                                        <div class="v2-comment-text">{{ $post->comment }}</div>
                                    </div>
                                </div>
                            @endforeach
                        @endif
                    </div>
                </div>
            @endforeach
            <div class="v2-pagination">{{ $issues->links() }}</div>
        @else
            <div class="v2-empty-state">
                <p>No requests have been created.</p>
                @if($group->canCreateRequest(Auth::user()))
                    <a href="{{ URL::route('group.create_issue', ['id' => $group->id]) }}" class="v2-btn v2-btn-primary mt-2">
                        <i class="fas fa-plus"></i> Create Request
                    </a>
                @endif
            </div>
        @endif
    </div>
</div>
@endsection

@push('scripts')
<script src="{{ asset('js/bootstrap-datetimepicker.min.js') }}"></script>
<script>
    $(document).ready(function() {
        $('#from_date, #to_date').datetimepicker({ format: 'yyyy-mm-dd', pickTime: false });
    });
</script>
@endpush