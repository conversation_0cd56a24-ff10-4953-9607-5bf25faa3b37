@extends('layouts.dashboard-new')

@inject('locationHelper', 'App\\Models\\LocationHelper')

@push('styles')
<link rel="stylesheet" href="{{ asset('css/v2-requests.css') }}">
<style>
  .issue-grid {
    display: grid;
    grid-template-columns: 1fr 370px;
    gap: 30px;
    align-items: flex-start;
    width: 100%;
    max-width: 1242px;
    margin: 0 auto;
  }
  .issue-main-col { display: flex; flex-direction: column; gap: 30px; }
  .issue-side-col { display: flex; flex-direction: column; gap: 30px; }
  .issue-card {
    background: #fff;
    border: 2px solid #F5F6F7;
    box-shadow: 0px 2px 5px rgba(38, 51, 77, 0.03);
    border-radius: 5px;
    padding: 24px 30px;
    display: flex;
    flex-direction: column;
    gap: 18px;
  }
  .issue-title-row {
    display: flex;
    align-items: center;
    gap: 16px;
  }
  .issue-avatar {
    width: 50px; height: 50px;
    border-radius: 5px;
    object-fit: cover;
    background: #F5F6F7;
  }
  .issue-title-block { display: flex; flex-direction: column; gap: 4px; }
  .issue-title {
    font-family: 'Inter', sans-serif;
    font-weight: 600;
    font-size: 20px;
    color: #4D5E80;
  }
  .issue-meta {
    font-family: 'Inter', sans-serif;
    font-weight: 700;
    font-size: 14px;
    color: #8347CC;
    display: flex;
    gap: 8px;
    align-items: center;
  }
  .issue-meta .author { color: #4D5E80; }
  .issue-meta .time { color: #7D8FB3; font-weight: 500; }
  .issue-desc {
    font-family: 'Inter', sans-serif;
    font-weight: 400;
    font-size: 16px;
    line-height: 24px;
    color: #4D5E80;
  }
  .issue-details-list {
    display: flex;
    flex-direction: column;
    gap: 6px;
    font-size: 15px;
  }
  .issue-details-list .label {
    color: #7D8FB3;
    font-weight: 700;
    margin-right: 6px;
  }
  .issue-details-list .value {
    color: #4D5E80;
    font-weight: 700;
  }
  .recipient-card {
    background: #8347CC08;
    border: 2px solid #8347CC;
    box-shadow: 0px 2px 5px 0px #26334D08;
    border-radius: 5px;
    padding: 24px 30px;
    display: flex;
    flex-direction: column;
    gap: 10px;
  }
  .recipient-title {
    font-size: 18px;
    font-weight: 700;
    color: #4D5E80;
    margin-bottom: 6px;
  }
  .recipient-list .label { color: #4D5E80; font-weight: 700; }
  .recipient-list .value { color: #4D5E80; font-weight: 700; }
  .weighin-card {
    background: #fff;
    border: 2px solid #F5F6F7;
    box-shadow: 0px 2px 5px rgba(38, 51, 77, 0.03);
    border-radius: 5px;
    padding: 24px 30px;
    display: flex;
    flex-direction: column;
    gap: 18px;
  }
  .weighin-title {
    font-size: 18px;
    font-weight: 700;
    color: #4D5E80;
  }
  .weighin-question {
    color: #4D5E80;
    font-size: 16px;
    margin-bottom: 8px;
  }
  .weighin-btn-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 14px 18px;
    width: 100%;
  }
  .vote-btn {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 16px 0 16px 16px;
    border-radius: 8px;
    border: 2px solid #E3EAFD;
    background: #fff;
    color: #7D8FB3;
    font-family: 'Inter', sans-serif;
    font-weight: 700;
    font-size: 16px;
    cursor: pointer;
    transition: background 0.2s, color 0.2s, border 0.2s;
    min-width: 140px;
    min-height: 56px;
    box-sizing: border-box;
    justify-content: flex-start;
  }
  .vote-btn.active, .vote-btn:focus {
    background: #F5FBEF;
    color: #7D8FB3;
    border: 2px solid #9ACB48;
    outline: none;
  }
  .vote-btn .vote-icon {
    font-size: 24px;
    display: flex;
    align-items: center;
  }
  .vote-btn.v2-concur.active {
    background: #F5FBEF;
    border: 2px solid #9ACB48;
  }
  .vote-btn.v2-open.active,
  .vote-btn.v2-wonder.active,
  .vote-btn.v2-disfavor.active {
    background: #F7F8FD;
    border: 2px solid #C3CAD9;
  }
  .voting-card {
    background: #fff;
    border: 2px solid #F5F6F7;
    box-shadow: 0px 2px 5px rgba(38, 51, 77, 0.03);
    border-radius: 5px;
    padding: 24px 30px;
    display: flex;
    flex-direction: column;
    gap: 18px;
  }
  .voting-title {
    font-size: 18px;
    font-weight: 700;
    color: #4D5E80;
  }
  .voting-bar {
    width: 100%;
    height: 28px;
    background: #F5F6F7;
    border-radius: 500px;
    display: flex;
    overflow: hidden;
    margin-bottom: 10px;
  }
  .voting-bar-concur { background: #9ACB48; height: 100%; }
  .voting-bar-open { background: #C3CAD9; height: 100%; }
  .voting-bar-wonder { background: #C3CAD9; height: 100%; }
  .voting-bar-disfavor { background: #CB4867; height: 100%; }
  .voting-tally {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    font-size: 16px;
    color: #7D8FB3;
    font-weight: 700;
  }
  .voting-avatars {
    display: flex;
    flex-direction: row;
    gap: 10px;
    margin-bottom: 10px;
  }
  .voting-avatar {
    width: 40px; height: 40px;
    border-radius: 5px;
    overflow: hidden;
    border: 2px solid #F5F6F7;
    background: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .voting-avatar img { width: 100%; height: 100%; object-fit: cover; }
  .voting-awaiting {
    color: #7D8FB3;
    font-size: 15px;
    margin-top: 8px;
  }
  
  .comments-card {
    background: rgba(43, 62, 80, 0.05);
    border-radius: 5px;
    padding: 24px 30px;
    display: flex;
    flex-direction: column;
    gap: 20px;
    width: 100%;
    margin-top: 30px;
  }
  .comments-title {
    font-size: 18px;
    font-weight: 700;
    color: #4D5E80;
    margin-bottom: 10px;
  }
  .comment-form {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 10px;
    width: 100%;
  }
  .comment-input {
    flex: 1;
    border: none;
    outline: none;
    font-family: 'Inter', sans-serif;
    font-size: 16px;
    color: #7D8FB3;
    background: #FFF;
    padding: 8px 8px;
  }
  .comment-send {
    background: #5144A1;
    color: #fff;
    border: none;
    border-radius: 5px;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
  }
  .comment-item {
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    gap: 15px;
    margin-bottom: 10px;
  }
  .comment-avatar img {
    width: 30px;
    height: 30px;
    border-radius: 5px;
    object-fit: cover;
  }
  .comment-content {
    display: flex;
    flex-direction: column;
    gap: 2px;
  }
  .comment-author {
    font-family: 'Inter', sans-serif;
    font-weight: 700;
    font-size: 12px;
    color: #4D5E80;
  }
  .comment-time {
    font-family: 'Inter', sans-serif;
    font-weight: 400;
    font-size: 12px;
    color: #7D8FB3;
  }
  .comment-text {
    font-family: 'Inter', sans-serif;
    font-weight: 400;
    font-size: 14px;
    color: #4D5E80;
  }
  @media (max-width: 1100px) {
    .issue-grid { grid-template-columns: 1fr; }
    .issue-side-col { flex-direction: row; gap: 20px; }
    .comments-card { width: 100%; }
  }
  @media (max-width: 700px) {
    .issue-card, .recipient-card, .weighin-card, .voting-card, .comments-card { padding: 16px 8px; }
    .issue-grid { gap: 16px; }
  }
</style>
@endpush

@section('title', $issue->title)

@section('content')
<div class="issue-grid">
  <div class="issue-main-col">
    <div class="issue-card">
      <div class="issue-title-row">
        <img class="issue-avatar" src="{{ $issue->creator && $issue->creator->profile_pic ? $issue->creator->profile_pic : asset('images/profile-placeholder.png') }}" alt="{{ $issue->creator ? $issue->creator->firstname . ' ' . $issue->creator->lastname : 'User' }}">
        <div class="issue-title-block">
          <div class="issue-title">{{ $issue->title }}</div>
          <div class="issue-meta">
            Request by <span class="author">{{ $issue->creator ? $issue->creator->firstname . ' ' . $issue->creator->lastname : 'Unknown User' }}</span>
            <span class="time">{{ $issue->created_at->diffForHumans() }}</span>
          </div>
        </div>
      </div>
      <div class="issue-desc">{!! $issue->niceDescription() !!}</div>
      <div class="issue-details-list">
        <div><span class="label">Request Amount:</span> <span class="value">{{ $issue->discussion->group->currency ?? '$' }}{{ number_format($proposal->amount, 2, '.', ',') }}</span></div>
        <div><span class="label">Deadline:</span> <span class="value">{{ $proposal->deadline_local->toFormattedDateString() }}</span></div>
        <div><span class="label">Time Remaining:</span> <span class="value">{{ ($proposal->deadline_c->isPast()) ? 'Past Deadline' : $proposal->deadline_c->diffForHumans() }}</span></div>
        <div><span class="label">Status:</span> <span class="value">{{ ucwords(preg_replace('/_/',' ',$proposal->payment_status)) }}</span></div>
        <div><span class="label">Category:</span> <span class="value">{{ $issue->issue_category->description }}</span></div>
        <div><span class="label">Type of Request:</span> <span class="value">{{ $issue->issue_type->description }}</span></div>
      </div>
    </div>
    <div class="weighin-card">
      <div class="weighin-title">Weigh In: Decision Required</div>
      <div class="weighin-question">Do you support this request & want to use group money to fulfill the request?</div>
      <div class="weighin-btn-grid">
        @foreach([
          'concur' => ['I Concur', '<svg width="24" height="24" fill="none" xmlns="http://www.w3.org/2000/svg"><rect width="24" height="24" rx="8" fill="none"/><path d="M7 13.5l3.5 3L17 9" stroke="#9ACB48" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round"/></svg>'],
          'open' => ['I Am Open', '<svg width="24" height="24" fill="none" xmlns="http://www.w3.org/2000/svg"><rect width="24" height="24" rx="8" fill="none"/><path d="M8 15V9m0 0h8m-8 0l4 6" stroke="#7D8FB3" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/><circle cx="12" cy="12" r="11" stroke="#C3CAD9" stroke-width="2"/></svg>'],
          'wonder' => ['I Wonder', '<svg width="24" height="24" fill="none" xmlns="http://www.w3.org/2000/svg"><rect width="24" height="24" rx="8" fill="none"/><path d="M8 15V9m0 0h8m-8 0l4 6" stroke="#C3CAD9" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/><circle cx="12" cy="12" r="11" stroke="#C3CAD9" stroke-width="2"/><path d="M16 16l-2-2m0 0l-2-2m2 2l2-2m-2 2l-2-2" stroke="#C3CAD9" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>'],
          'disfavor' => ['I Disfavor', '<svg width="24" height="24" fill="none" xmlns="http://www.w3.org/2000/svg"><rect width="24" height="24" rx="8" fill="none"/><path d="M8 16l8-8M8 8l8 8" stroke="#CB4867" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round"/></svg>'],
        ] as $voteType => [$label, $icon])
          <form method="POST" action="{{ route('proposal.vote', [$proposal->id, $voteType]) }}" style="margin:0;">
            @csrf
            <button type="submit" class="vote-btn v2-{{ $voteType }} @if($proposal->my_vote(Auth::user()) === $voteType) active @endif">
              <span class="vote-icon">{!! $icon !!}</span>
              {{ $label }}
            </button>
          </form>
        @endforeach
      </div>
    </div>

  </div>
  <div class="issue-side-col">
    <div class="recipient-card">
      <div class="recipient-title">Recipient Details</div>
      <div class="recipient-list issue-details-list">
        <div><span class="label">Recipient:</span> <span class="value">{{ $issue->recipient ? $issue->recipient->firstname . ' ' . $issue->recipient->lastname : 'To be defined' }}</span></div>
        @if(isset($issue->recipient) && $issue->recipient->address)
          <div><span class="label">City:</span> <span class="value">{{ $issue->recipient->address->city }}</span></div>
          <div><span class="label">State:</span> <span class="value">{{ $locationHelper->subdivisionName($issue->recipient->address->state) }}</span></div>
          <div><span class="label">Postal Code:</span> <span class="value">{{ $issue->recipient->address->postcode }}</span></div>
          <div><span class="label">Country:</span> <span class="value">{{ $locationHelper->countryName($issue->recipient->address->country) }}</span></div>
        @endif
        <div><span class="label">Payment Status:</span> <span class="value">Payment directed to an authorized third party</span></div>
      </div>
    </div>
    <div class="voting-card">
      <div class="voting-title">Voting Status</div>
      <div class="voting-bar">
        <div class="voting-bar-concur" style="width: {{ $currentVotes['concur'] }}%"></div>
        <div class="voting-bar-open" style="width: {{ $currentVotes['open'] }}%"></div>
        <div class="voting-bar-wonder" style="width: {{ $currentVotes['wonder'] }}%"></div>
        <div class="voting-bar-disfavor" style="width: {{ $currentVotes['disfavor'] }}%"></div>
      </div>
      <div class="voting-tally">
        <span>{{ $tally['concur'] }} Members Concur</span>
        <span>{{ $tally['open'] }} Members Open</span>
        <span>{{ $tally['wonder'] }} Members Wonder</span>
        <span>{{ $tally['disfavor'] }} Members Disfavor</span>
      </div>
      <div class="voting-avatars">
        @foreach($issue->discussion->group->active_members as $member)
          <div class="voting-avatar">
            <img src="{{ $member->profile_pic ?? asset('images/profile-placeholder.png') }}" alt="{{ $member->firstname }} {{ $member->lastname }}">
          </div>
        @endforeach
      </div>
      <div class="voting-awaiting">
        @if($tally['no_vote'])
          Awaiting {{ $tally['no_vote'] }} responses
        @endif
      </div>
    </div>
  </div>
</div>
<div class="comments-card">
      <div class="comments-title">Comments</div>
      <form class="comment-form" action="{{ route('issue.post', $issue->id) }}" method="POST">
        @csrf
        <input type="text" name="comment" class="comment-input" placeholder="Type Comment...">
        <button type="submit" class="comment-send"><i class="fa fa-arrow-right"></i></button>
      </form>
      @foreach($issue->discussion->posts as $post)
        <div class="comment-item">
          <div class="comment-avatar">
            <img src="{{ $post->author->profile_pic ?? asset('images/profile-placeholder.png') }}" alt="{{ $post->author->firstname ?? '' }}">
          </div>
          <div class="comment-content">
            <div class="comment-author">{{ $post->author->firstname ?? '' }} {{ $post->author->lastname ?? '' }}</div>
            <div class="comment-time">{{ $post->created_at->diffForHumans() }}</div>
            <div class="comment-text">{!! $post->niceComment() !!}</div>
          </div>
        </div>
      @endforeach
    </div>
@endsection
