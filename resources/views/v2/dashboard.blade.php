@extends('layouts.dashboard-new')

@push('styles')
<link rel="stylesheet" href="{{ asset('css/v2-requests.css') }}">
<style>
    .activity-card { background: #fff; border-radius: 12px; box-shadow: 0 2px 8px rgba(0,0,0,0.04); margin-bottom: 32px; padding: 32px; }
    .card-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 18px; }
    .card-title { font-size: 1.3rem; font-weight: 600; color: #5144A1; }
    .activity-item { display: flex; gap: 24px; border-bottom: 1px solid #F5F6F7; padding: 24px 0; }
    .activity-avatar img { width: 50px; height: 50px; border-radius: 5px; object-fit: cover; background: #F5F6F7; }
    .activity-content { flex: 1; display: flex; flex-direction: column; gap: 8px; }
    .activity-header { display: flex; align-items: center; gap: 12px; }
    .activity-title { font-size: 1.1rem; font-weight: 600; color: #4D5E80; margin: 0; }
    .activity-time { color: #7D8FB3; font-size: 14px; font-weight: 500; }
    .activity-group { color: #8347CC; font-size: 14px; font-weight: 700; }
    .activity-description { color: #4D5E80; font-size: 15px; }
    .activity-actions { display: flex; gap: 16px; margin-top: 8px; }
    .activity-action { color: #7D8FB3; font-size: 14px; text-decoration: none; display: flex; align-items: center; gap: 6px; }
    .activity-action i { font-size: 16px; }
    .comment-form { margin-top: 10px; }
    .comment-box { display: flex; gap: 8px; }
    .comment-input { flex: 1; border: 1px solid #e5e7eb; border-radius: 6px; padding: 8px 12px; font-size: 15px; }
    .comment-button { background: #6c47ff; color: #fff; border: none; border-radius: 6px; padding: 0 16px; font-size: 16px; cursor: pointer; }
    .empty-state { color: #7D8FB3; text-align: center; margin: 32px 0; }
</style>
@endpush

@section('title', 'All Recent Activity')

@section('content')
<div class="content-body">
    <h1 class="page-title">All Recent Activity</h1>

    <div class="activity-card">
        <div class="card-header">
            <h3 class="card-title">Recent Requests</h3>
            <a href="{{ route('issue.index') }}" class="btn btn-primary">View All</a>
        </div>
        <div class="card-body">
            @if(isset($recentIssues) && $recentIssues->count() > 0)
                @foreach($recentIssues as $issue)
                    <div class="activity-item">
                        <div class="activity-avatar">
                            @if($issue->user && $issue->user->avatar_url)
                                <img src="{{ $issue->user->avatar_url }}" alt="{{ $issue->user->firstname }} {{ $issue->user->lastname }}">
                            @else
                                <img src="{{ asset('images/profile-placeholder.png') }}" alt="User">
                            @endif
                        </div>
                        <div class="activity-content">
                            <div class="activity-header">
                                <h3 class="activity-title">{{ $issue->title }}</h3>
                                <span class="activity-time">{{ $issue->created_at->diffForHumans() }}</span>
                            </div>
                            <p class="activity-group">
                                Request by {{ $issue->user ? $issue->user->firstname . ' ' . $issue->user->lastname : 'Unknown User' }}
                                in {{ $issue->group ? $issue->group->name : 'Unknown Group' }}
                            </p>
                            @if($issue->amount)
                                <p class="activity-group"><strong>Amount: ${{ number_format($issue->amount, 2) }}</strong></p>
                            @endif
                            <p class="activity-description">
                                {{ \Illuminate\Support\Str::limit($issue->description ?? '', 300) }}
                            </p>
                            <div class="activity-actions">
                                <a href="{{ route('issue.show', $issue->id) }}" class="activity-action">
                                    <i class="fas fa-eye"></i>
                                    <span>View Details</span>
                                </a>
                                <a href="#" class="activity-action">
                                    <i class="fas fa-check"></i>
                                    <span>I Concur</span>
                                </a>
                                <a href="#" class="activity-action">
                                    <i class="fas fa-door-open"></i>
                                    <span>I Am Open</span>
                                </a>
                                <a href="#" class="activity-action">
                                    <i class="fas fa-question"></i>
                                    <span>I Wonder</span>
                                </a>
                            </div>
                            <form class="comment-form" action="{{ route('issue.post', $issue->id) }}" method="POST">
                                @csrf
                                <div class="comment-box">
                                    <input type="text" name="body" class="comment-input" placeholder="Type Comment...">
                                    <button type="submit" class="comment-button">
                                        <i class="fas fa-arrow-right"></i>
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                @endforeach
            @else
                <div class="empty-state">
                    <p>No recent requests found.</p>
                    <a href="{{ route('issue.create') }}" class="btn btn-primary">Create Request</a>
                </div>
            @endif
        </div>
    </div>

    <div class="activity-card">
        <div class="card-header">
            <h3 class="card-title">Recent Discussions</h3>
            <a href="{{ route('discussion.index') }}" class="btn btn-primary">View All</a>
        </div>
        <div class="card-body">
            @if(isset($recentDiscussions) && $recentDiscussions->count() > 0)
                @foreach($recentDiscussions as $discussion)
                    <div class="activity-item">
                        <div class="activity-avatar">
                            @if($discussion->user && $discussion->user->avatar_url)
                                <img src="{{ $discussion->user->avatar_url }}" alt="{{ $discussion->user->firstname }} {{ $discussion->user->lastname }}">
                            @else
                                <img src="{{ asset('images/profile-placeholder.png') }}" alt="User">
                            @endif
                        </div>
                        <div class="activity-content">
                            <div class="activity-header">
                                <h3 class="activity-title">{{ $discussion->title }}</h3>
                                <span class="activity-time">{{ $discussion->created_at->diffForHumans() }}</span>
                            </div>
                            <p class="activity-group">
                                Discussion by {{ $discussion->user ? $discussion->user->firstname . ' ' . $discussion->user->lastname : 'Unknown User' }}
                                in {{ $discussion->group ? $discussion->group->name : 'Unknown Group' }}
                            </p>
                            <p class="activity-description">
                                {{ \Illuminate\Support\Str::limit($discussion->body ?? '', 300) }}
                            </p>
                            <div class="activity-actions">
                                <a href="{{ route('discussion.show', $discussion->id) }}" class="activity-action">
                                    <i class="fas fa-eye"></i>
                                    <span>View Details</span>
                                </a>
                                <a href="#" class="activity-action">
                                    <i class="fas fa-comment"></i>
                                    <span>Comment</span>
                                </a>
                            </div>
                        </div>
                    </div>
                @endforeach
            @else
                <div class="empty-state">
                    <p>No recent discussions found.</p>
                    <a href="{{ route('discussion.create') }}" class="btn btn-primary">Start Discussion</a>
                </div>
            @endif
        </div>
    </div>
</div>
@endsection 