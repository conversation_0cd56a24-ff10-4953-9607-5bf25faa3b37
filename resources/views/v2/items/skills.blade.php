@extends('v2.layouts.dashboard')

@section('title', 'Add New Skill')

@push('styles')
<style>
    .v2-form-container {
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
    }

    .v2-form-header {
        margin-bottom: 30px;
        padding-bottom: 20px;
        border-bottom: 2px solid #e5e7eb;
    }

    .v2-form-title {
        font-size: 32px;
        font-weight: 700;
        color: #4D5E80;
        margin: 0;
    }

    .v2-form-subtitle {
        color: #6b7280;
        margin-top: 8px;
        font-size: 16px;
    }

    .v2-form {
        background: white;
        padding: 30px;
        border-radius: 8px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .v2-form-group {
        margin-bottom: 20px;
    }

    .v2-form-label {
        display: block;
        font-weight: 600;
        color: #374151;
        margin-bottom: 8px;
        font-size: 14px;
    }

    .v2-form-input,
    .v2-form-select,
    .v2-form-textarea {
        width: 100%;
        padding: 12px;
        border: 1px solid #d1d5db;
        border-radius: 6px;
        font-size: 14px;
        transition: border-color 0.2s;
    }

    .v2-form-input:focus,
    .v2-form-select:focus,
    .v2-form-textarea:focus {
        outline: none;
        border-color: #5144A1;
        box-shadow: 0 0 0 3px rgba(81, 68, 161, 0.1);
    }

    .v2-form-textarea {
        resize: vertical;
        min-height: 120px;
    }

    .v2-checkbox-group {
        display: flex;
        align-items: center;
        gap: 10px;
        margin-top: 10px;
    }

    .v2-checkbox {
        width: auto;
    }

    .v2-checkbox-label {
        font-weight: normal;
        margin: 0;
        cursor: pointer;
    }

    .v2-form-actions {
        display: flex;
        gap: 15px;
        justify-content: flex-end;
        margin-top: 30px;
        padding-top: 20px;
        border-top: 1px solid #e5e7eb;
    }

    .v2-btn {
        padding: 12px 24px;
        border-radius: 6px;
        font-weight: 600;
        text-decoration: none;
        border: none;
        cursor: pointer;
        transition: all 0.2s;
    }

    .v2-btn-primary {
        background: #5144A1;
        color: white;
    }

    .v2-btn-primary:hover {
        background: #4338ca;
    }

    .v2-btn-secondary {
        background: #6b7280;
        color: white;
    }

    .v2-btn-secondary:hover {
        background: #4b5563;
    }

    .v2-error {
        color: #ef4444;
        font-size: 12px;
        margin-top: 4px;
    }

    .v2-form-help {
        font-size: 12px;
        color: #6b7280;
        margin-top: 4px;
    }

    .v2-rate-group {
        display: grid;
        grid-template-columns: 1fr auto;
        gap: 10px;
        align-items: end;
    }

    .v2-currency-select {
        width: 80px;
    }

    #hourly-rate-group {
        transition: opacity 0.3s ease;
    }

    #hourly-rate-group.disabled {
        opacity: 0.5;
        pointer-events: none;
    }
</style>
@endpush

@section('content')
<div class="v2-form-container">
    <div class="v2-form-header">
        <h1 class="v2-form-title">Add New Skill</h1>
        <p class="v2-form-subtitle">Share your skills and expertise with your community.</p>
    </div>

    <form method="POST" action="{{ route('v2.skills.store') }}" class="v2-form">
        @csrf

        <div class="v2-form-group">
            <label for="title" class="v2-form-label">Skill Title *</label>
            <input type="text" id="title" name="title" class="v2-form-input" 
                   value="{{ old('title') }}" required 
                   placeholder="e.g., Web Development, Carpentry, Graphic Design">
            @error('title')
                <div class="v2-error">{{ $message }}</div>
            @enderror
        </div>

        <div class="v2-form-group">
            <label for="description" class="v2-form-label">Description *</label>
            <textarea id="description" name="description" class="v2-form-textarea" 
                      required placeholder="Describe your skill, experience, and what you can help with...">{{ old('description') }}</textarea>
            @error('description')
                <div class="v2-error">{{ $message }}</div>
            @enderror
        </div>

        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
            <div class="v2-form-group">
                <label for="category" class="v2-form-label">Category *</label>
                <select id="category" name="category" class="v2-form-select" required>
                    <option value="">Select category</option>
                    <option value="technical" {{ old('category') == 'technical' ? 'selected' : '' }}>Technical</option>
                    <option value="creative" {{ old('category') == 'creative' ? 'selected' : '' }}>Creative</option>
                    <option value="manual" {{ old('category') == 'manual' ? 'selected' : '' }}>Manual Labor</option>
                    <option value="professional" {{ old('category') == 'professional' ? 'selected' : '' }}>Professional Services</option>
                    <option value="educational" {{ old('category') == 'educational' ? 'selected' : '' }}>Educational</option>
                    <option value="health" {{ old('category') == 'health' ? 'selected' : '' }}>Health & Wellness</option>
                    <option value="domestic" {{ old('category') == 'domestic' ? 'selected' : '' }}>Domestic</option>
                    <option value="other" {{ old('category') == 'other' ? 'selected' : '' }}>Other</option>
                </select>
                @error('category')
                    <div class="v2-error">{{ $message }}</div>
                @enderror
            </div>

            <div class="v2-form-group">
                <label for="experience_level" class="v2-form-label">Experience Level *</label>
                <select id="experience_level" name="experience_level" class="v2-form-select" required>
                    <option value="">Select level</option>
                    <option value="beginner" {{ old('experience_level') == 'beginner' ? 'selected' : '' }}>Beginner</option>
                    <option value="intermediate" {{ old('experience_level') == 'intermediate' ? 'selected' : '' }}>Intermediate</option>
                    <option value="advanced" {{ old('experience_level') == 'advanced' ? 'selected' : '' }}>Advanced</option>
                    <option value="expert" {{ old('experience_level') == 'expert' ? 'selected' : '' }}>Expert</option>
                </select>
                @error('experience_level')
                    <div class="v2-error">{{ $message }}</div>
                @enderror
            </div>
        </div>

        <div class="v2-form-group">
            <div class="v2-checkbox-group">
                <input type="checkbox" id="is_free" name="is_free" value="1" class="v2-checkbox" 
                       {{ old('is_free', true) ? 'checked' : '' }}>
                <label for="is_free" class="v2-checkbox-label">I offer this skill for free</label>
            </div>
        </div>

        <div id="hourly-rate-group" class="v2-form-group {{ old('is_free', true) ? 'disabled' : '' }}">
            <label for="hourly_rate" class="v2-form-label">Hourly Rate (Optional)</label>
            <div class="v2-rate-group">
                <input type="number" id="hourly_rate" name="hourly_rate" class="v2-form-input" 
                       value="{{ old('hourly_rate') }}" step="0.01" min="0" placeholder="0.00">
                <select id="currency" name="currency" class="v2-form-select v2-currency-select">
                    <option value="USD" {{ old('currency', 'USD') == 'USD' ? 'selected' : '' }}>USD</option>
                    <option value="GBP" {{ old('currency') == 'GBP' ? 'selected' : '' }}>GBP</option>
                    <option value="EUR" {{ old('currency') == 'EUR' ? 'selected' : '' }}>EUR</option>
                </select>
            </div>
            @error('hourly_rate')
                <div class="v2-error">{{ $message }}</div>
            @enderror
        </div>

        <div class="v2-form-group">
            <label for="requirements" class="v2-form-label">Requirements or Materials Needed</label>
            <textarea id="requirements" name="requirements" class="v2-form-textarea" 
                      placeholder="Any tools, materials, or requirements needed for this skill...">{{ old('requirements') }}</textarea>
            <div class="v2-form-help">Let people know what they might need to provide or prepare</div>
            @error('requirements')
                <div class="v2-error">{{ $message }}</div>
            @enderror
        </div>

        <div class="v2-form-actions">
            <a href="{{ route('v2.items.index') }}" class="v2-btn v2-btn-secondary">Cancel</a>
            <button type="submit" class="v2-btn v2-btn-primary">Add Skill</button>
        </div>
    </form>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const isFreeCheckbox = document.getElementById('is_free');
    const hourlyRateGroup = document.getElementById('hourly-rate-group');
    const hourlyRateInput = document.getElementById('hourly_rate');

    function toggleHourlyRate() {
        if (isFreeCheckbox.checked) {
            hourlyRateGroup.classList.add('disabled');
            hourlyRateInput.value = '';
        } else {
            hourlyRateGroup.classList.remove('disabled');
        }
    }

    isFreeCheckbox.addEventListener('change', toggleHourlyRate);
    
    // Initialize on page load
    toggleHourlyRate();
});
</script>
@endsection
