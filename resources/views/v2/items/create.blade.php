@extends('v2.layouts.dashboard')

@section('title', 'Add New Item')

@push('styles')
<style>
    .v2-form-container {
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
    }

    .v2-form-header {
        margin-bottom: 30px;
        padding-bottom: 20px;
        border-bottom: 2px solid #e5e7eb;
    }

    .v2-form-title {
        font-size: 32px;
        font-weight: 700;
        color: #4D5E80;
        margin: 0;
    }

    .v2-form-subtitle {
        color: #6b7280;
        margin-top: 8px;
        font-size: 16px;
    }

    .v2-form {
        background: white;
        padding: 30px;
        border-radius: 8px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .v2-form-group {
        margin-bottom: 20px;
    }

    .v2-form-label {
        display: block;
        font-weight: 600;
        color: #374151;
        margin-bottom: 8px;
        font-size: 14px;
    }

    .v2-form-input,
    .v2-form-select,
    .v2-form-textarea {
        width: 100%;
        padding: 12px;
        border: 1px solid #d1d5db;
        border-radius: 6px;
        font-size: 14px;
        transition: border-color 0.2s;
    }

    .v2-form-input:focus,
    .v2-form-select:focus,
    .v2-form-textarea:focus {
        outline: none;
        border-color: #5144A1;
        box-shadow: 0 0 0 3px rgba(81, 68, 161, 0.1);
    }

    .v2-form-textarea {
        resize: vertical;
        min-height: 100px;
    }

    .v2-amazon-search {
        position: relative;
    }

    .v2-search-results {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: white;
        border: 1px solid #d1d5db;
        border-top: none;
        border-radius: 0 0 6px 6px;
        max-height: 300px;
        overflow-y: auto;
        z-index: 10;
        display: none;
    }

    .v2-search-result {
        padding: 12px;
        border-bottom: 1px solid #f3f4f6;
        cursor: pointer;
        display: flex;
        gap: 12px;
        align-items: center;
    }

    .v2-search-result:hover {
        background: #f9fafb;
    }

    .v2-search-result img {
        width: 50px;
        height: 50px;
        object-fit: cover;
        border-radius: 4px;
    }

    .v2-search-result-info h4 {
        margin: 0 0 4px 0;
        font-size: 14px;
        font-weight: 600;
        color: #111827;
    }

    .v2-search-result-info p {
        margin: 0;
        font-size: 12px;
        color: #6b7280;
    }

    .v2-form-actions {
        display: flex;
        gap: 15px;
        justify-content: flex-end;
        margin-top: 30px;
        padding-top: 20px;
        border-top: 1px solid #e5e7eb;
    }

    .v2-btn {
        padding: 12px 24px;
        border-radius: 6px;
        font-weight: 600;
        text-decoration: none;
        border: none;
        cursor: pointer;
        transition: all 0.2s;
    }

    .v2-btn-primary {
        background: #5144A1;
        color: white;
    }

    .v2-btn-primary:hover {
        background: #4338ca;
    }

    .v2-btn-secondary {
        background: #6b7280;
        color: white;
    }

    .v2-btn-secondary:hover {
        background: #4b5563;
    }

    .v2-error {
        color: #ef4444;
        font-size: 12px;
        margin-top: 4px;
    }

    .v2-form-help {
        font-size: 12px;
        color: #6b7280;
        margin-top: 4px;
    }

    .v2-preview-image {
        max-width: 200px;
        max-height: 200px;
        border-radius: 6px;
        margin-top: 10px;
        display: none;
    }
</style>
@endpush

@section('content')
<div class="v2-form-container">
    <div class="v2-form-header">
        <h1 class="v2-form-title">Add New Item</h1>
        <p class="v2-form-subtitle">Share an item with your community. Search Amazon products or add manually.</p>
    </div>

    <form method="POST" action="{{ route('v2.items.store') }}" class="v2-form">
        @csrf

        <!-- Amazon Search -->
        <div class="v2-form-group">
            <label class="v2-form-label">Search Amazon Products</label>
            <div class="v2-amazon-search">
                <input type="text" id="amazon-search" class="v2-form-input" 
                       placeholder="Type to search Amazon products..." autocomplete="off">
                <div id="search-results" class="v2-search-results"></div>
            </div>
            <div class="v2-form-help">Start typing to search for products on Amazon</div>
        </div>

        <!-- Manual Entry Fields -->
        <div class="v2-form-group">
            <label for="title" class="v2-form-label">Item Title *</label>
            <input type="text" id="title" name="title" class="v2-form-input" 
                   value="{{ old('title') }}" required>
            @error('title')
                <div class="v2-error">{{ $message }}</div>
            @enderror
        </div>

        <div class="v2-form-group">
            <label for="description" class="v2-form-label">Description</label>
            <textarea id="description" name="description" class="v2-form-textarea" 
                      placeholder="Describe your item...">{{ old('description') }}</textarea>
            @error('description')
                <div class="v2-error">{{ $message }}</div>
            @enderror
        </div>

        <div class="v2-form-group">
            <label for="image_url" class="v2-form-label">Image URL</label>
            <input type="url" id="image_url" name="image_url" class="v2-form-input" 
                   value="{{ old('image_url') }}" placeholder="https://example.com/image.jpg">
            <img id="preview-image" class="v2-preview-image" alt="Preview">
            @error('image_url')
                <div class="v2-error">{{ $message }}</div>
            @enderror
        </div>

        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
            <div class="v2-form-group">
                <label for="price" class="v2-form-label">Price</label>
                <input type="number" id="price" name="price" class="v2-form-input" 
                       value="{{ old('price') }}" step="0.01" min="0" placeholder="0.00">
                @error('price')
                    <div class="v2-error">{{ $message }}</div>
                @enderror
            </div>

            <div class="v2-form-group">
                <label for="condition" class="v2-form-label">Condition *</label>
                <select id="condition" name="condition" class="v2-form-select" required>
                    <option value="">Select condition</option>
                    <option value="new" {{ old('condition') == 'new' ? 'selected' : '' }}>New</option>
                    <option value="like_new" {{ old('condition') == 'like_new' ? 'selected' : '' }}>Like New</option>
                    <option value="good" {{ old('condition') == 'good' ? 'selected' : '' }}>Good</option>
                    <option value="fair" {{ old('condition') == 'fair' ? 'selected' : '' }}>Fair</option>
                    <option value="poor" {{ old('condition') == 'poor' ? 'selected' : '' }}>Poor</option>
                </select>
                @error('condition')
                    <div class="v2-error">{{ $message }}</div>
                @enderror
            </div>
        </div>

        <div class="v2-form-group">
            <label for="notes" class="v2-form-label">Additional Notes</label>
            <textarea id="notes" name="notes" class="v2-form-textarea" 
                      placeholder="Any additional information about this item...">{{ old('notes') }}</textarea>
            @error('notes')
                <div class="v2-error">{{ $message }}</div>
            @enderror
        </div>

        <!-- Hidden fields for Amazon data -->
        <input type="hidden" id="amazon_asin" name="amazon_asin" value="{{ old('amazon_asin') }}">
        <input type="hidden" id="amazon_url" name="amazon_url" value="{{ old('amazon_url') }}">

        <div class="v2-form-actions">
            <a href="{{ route('v2.items.index') }}" class="v2-btn v2-btn-secondary">Cancel</a>
            <button type="submit" class="v2-btn v2-btn-primary">Add Item</button>
        </div>
    </form>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('amazon-search');
    const searchResults = document.getElementById('search-results');
    const previewImage = document.getElementById('preview-image');
    const imageUrlInput = document.getElementById('image_url');
    let searchTimeout;

    // Amazon product search
    searchInput.addEventListener('input', function() {
        const query = this.value.trim();
        
        clearTimeout(searchTimeout);
        
        if (query.length < 2) {
            searchResults.style.display = 'none';
            return;
        }

        searchTimeout = setTimeout(() => {
            fetch(`{{ route('v2.items.search') }}?q=${encodeURIComponent(query)}`)
                .then(response => response.json())
                .then(data => {
                    displaySearchResults(data.results);
                })
                .catch(error => {
                    console.error('Search error:', error);
                });
        }, 300);
    });

    function displaySearchResults(results) {
        if (results.length === 0) {
            searchResults.style.display = 'none';
            return;
        }

        searchResults.innerHTML = results.map(product => `
            <div class="v2-search-result" data-product='${JSON.stringify(product)}'>
                <img src="${product.image_url}" alt="${product.title}" onerror="this.style.display='none'">
                <div class="v2-search-result-info">
                    <h4>${product.title}</h4>
                    <p>${product.price ? '$' + product.price : ''} - ${product.description}</p>
                </div>
            </div>
        `).join('');

        searchResults.style.display = 'block';

        // Add click handlers
        searchResults.querySelectorAll('.v2-search-result').forEach(result => {
            result.addEventListener('click', function() {
                const product = JSON.parse(this.dataset.product);
                selectProduct(product);
            });
        });
    }

    function selectProduct(product) {
        document.getElementById('title').value = product.title;
        document.getElementById('description').value = product.description || '';
        document.getElementById('image_url').value = product.image_url || '';
        document.getElementById('price').value = product.price || '';
        document.getElementById('amazon_asin').value = product.asin || '';
        document.getElementById('amazon_url').value = product.url || '';
        
        searchResults.style.display = 'none';
        searchInput.value = product.title;
        
        // Show preview image
        if (product.image_url) {
            previewImage.src = product.image_url;
            previewImage.style.display = 'block';
        }
    }

    // Image preview
    imageUrlInput.addEventListener('input', function() {
        const url = this.value.trim();
        if (url) {
            previewImage.src = url;
            previewImage.style.display = 'block';
        } else {
            previewImage.style.display = 'none';
        }
    });

    // Hide search results when clicking outside
    document.addEventListener('click', function(e) {
        if (!searchInput.contains(e.target) && !searchResults.contains(e.target)) {
            searchResults.style.display = 'none';
        }
    });
});
</script>
@endsection
