@extends('layouts.dashboard-new')

@push('styles')
<link rel="stylesheet" href="{{ asset('css/v2-accounts.css') }}">
<link rel="stylesheet" href="{{ asset('css/v2-finances.css') }}">
@endpush

@section('title', 'Finances')

@section('content')
<div class="v2-account-container">
    <div class="v2-account-header">
        <h1 class="v2-account-title">Finances</h1>
    </div>

    <div class="v2-account-content">
        @if(session('success'))
            <div class="v2-alert v2-alert-success">
                {{ session('success') }}
            </div>
        @endif

        @if($errors->any())
            <div class="v2-alert v2-alert-error">
                <ul style="margin: 0; padding-left: 20px;">
                    @foreach($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
            </div>
        @endif

        <!-- Tab Navigation -->
        <div class="v2-finances-tabs">
            <button class="v2-tab-button {{ $activeTab === 'us' ? 'active' : '' }}" data-tab="us">
                United States
            </button>
            <button class="v2-tab-button {{ $activeTab === 'uk' ? 'active' : '' }}" data-tab="uk">
                United Kingdom
            </button>
            <button class="v2-tab-button {{ $activeTab === 'sa' ? 'active' : '' }}" data-tab="sa">
                South Africa
            </button>
        </div>

        <!-- Tab Content -->
        <div class="v2-tab-content">
            <!-- United States Tab -->
            <div id="us-tab" class="v2-tab-pane {{ $activeTab === 'us' ? 'active' : '' }}">
                @include('v2.finances.partials.united-states')
            </div>

            <!-- United Kingdom Tab -->
            <div id="uk-tab" class="v2-tab-pane {{ $activeTab === 'uk' ? 'active' : '' }}">
                @include('v2.finances.partials.united-kingdom')
            </div>

            <!-- South Africa Tab -->
            <div id="sa-tab" class="v2-tab-pane {{ $activeTab === 'sa' ? 'active' : '' }}">
                @include('v2.finances.partials.south-africa')
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Tab switching functionality
    const tabButtons = document.querySelectorAll('.v2-tab-button');
    const tabPanes = document.querySelectorAll('.v2-tab-pane');

    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            const targetTab = this.getAttribute('data-tab');

            // Remove active class from all buttons and panes
            tabButtons.forEach(btn => btn.classList.remove('active'));
            tabPanes.forEach(pane => pane.classList.remove('active'));

            // Add active class to clicked button and corresponding pane
            this.classList.add('active');
            document.getElementById(targetTab + '-tab').classList.add('active');
        });
    });
});
</script>
@endpush
