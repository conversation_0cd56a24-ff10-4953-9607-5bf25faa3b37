@extends('layouts.dashboard-new')

@section('title')
    Discussions - {{ $group->name }}
@stop

@push('styles')
<style>
    :root {
        --primary-purple: #8347CC;
        --primary-navy: #5144A1;
        --primary-grey: #CECECE;
        --light-lavender: #F0F0F9;
        --light-white: #FFFFFF;
        --light-gray: #E5E7EB;
        --text-gray: #6B7280;
        --border-color: #E0E0E0;
        --success-green: #10B981;
        --warning-orange: #F59E0B;
        --danger-red: #EF4444;
    }

    .discussions-container {
        padding: 2rem;
    }

    .page-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 2rem;
        flex-wrap: wrap;
        gap: 1rem;
    }

    .page-title-section h1 {
        font-size: 2rem;
        font-weight: 700;
        color: var(--primary-navy);
        margin: 0 0 0.5rem 0;
    }

    .page-subtitle {
        color: var(--text-gray);
        font-size: 1rem;
    }

    .header-actions {
        display: flex;
        gap: 1rem;
        align-items: center;
    }

    .btn {
        padding: 0.75rem 1.5rem;
        border-radius: 8px;
        font-weight: 600;
        font-size: 0.95rem;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        cursor: pointer;
        transition: all 0.2s ease;
        border: none;
    }

    .btn-primary {
        background: var(--primary-purple);
        color: var(--light-white);
        border: 1px solid var(--primary-purple);
    }

    .btn-primary:hover {
        background: var(--primary-navy);
        border-color: var(--primary-navy);
        text-decoration: none;
        color: var(--light-white);
    }

    .btn-secondary {
        background: var(--light-white);
        color: var(--primary-navy);
        border: 1px solid var(--border-color);
    }

    .btn-secondary:hover {
        background: var(--light-lavender);
        text-decoration: none;
        color: var(--primary-navy);
    }

    .filters-card {
        background: var(--light-white);
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        padding: 1.5rem;
        margin-bottom: 2rem;
    }

    .filters-form {
        display: grid;
        grid-template-columns: 1fr 1fr auto;
        gap: 1rem;
        align-items: end;
    }

    .filter-group {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }

    .filter-label {
        font-weight: 600;
        color: var(--primary-navy);
        font-size: 0.9rem;
    }

    .filter-row {
        display: flex;
        gap: 0.5rem;
        align-items: center;
    }

    .form-control {
        padding: 0.75rem;
        border: 1px solid var(--border-color);
        border-radius: 8px;
        font-size: 0.9rem;
        transition: border-color 0.2s ease, box-shadow 0.2s ease;
        background: var(--light-white);
    }

    .form-control:focus {
        border-color: var(--primary-purple);
        box-shadow: 0 0 0 3px rgba(131, 71, 204, 0.1);
        outline: none;
    }

    .form-control.small {
        padding: 0.5rem;
        font-size: 0.85rem;
    }

    .search-group {
        display: flex;
        gap: 0.5rem;
    }

    .search-input {
        flex: 1;
    }

    .discussions-card {
        background: var(--light-white);
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        overflow: hidden;
    }

    .card-header {
        background: var(--light-lavender);
        padding: 1.5rem 2rem;
        border-bottom: 1px solid var(--border-color);
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .card-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: var(--primary-navy);
        margin: 0;
    }

    .discussion-item {
        padding: 1.5rem 2rem;
        border-bottom: 1px solid var(--light-gray);
        transition: background-color 0.2s ease;
    }

    .discussion-item:last-child {
        border-bottom: none;
    }

    .discussion-item:hover {
        background-color: var(--light-lavender);
    }

    .discussion-content {
        display: flex;
        gap: 1rem;
    }

    .discussion-avatar {
        flex-shrink: 0;
    }

    .discussion-avatar img {
        width: 48px;
        height: 48px;
        border-radius: 50%;
        object-fit: cover;
    }

    .discussion-body {
        flex: 1;
        min-width: 0;
    }

    .discussion-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 0.5rem;
        gap: 1rem;
    }

    .discussion-title {
        font-size: 1.1rem;
        font-weight: 600;
        color: var(--primary-navy);
        text-decoration: none;
        margin: 0;
    }

    .discussion-title:hover {
        color: var(--primary-purple);
        text-decoration: none;
    }

    .discussion-time {
        color: var(--text-gray);
        font-size: 0.875rem;
        white-space: nowrap;
    }

    .discussion-meta {
        color: var(--text-gray);
        font-size: 0.9rem;
        margin-bottom: 0.75rem;
    }

    .discussion-badges {
        display: flex;
        gap: 0.5rem;
        margin-bottom: 0.75rem;
        flex-wrap: wrap;
    }

    .badge {
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.025em;
    }

    .badge-open {
        background: #DCFCE7;
        color: #166534;
    }

    .badge-closed {
        background: #FEE2E2;
        color: #991B1B;
    }

    .badge-unread {
        background: #FEF3C7;
        color: #92400E;
    }

    .badge-read {
        background: #DCFCE7;
        color: #166534;
    }

    .discussion-description {
        color: var(--text-gray);
        line-height: 1.5;
        margin-bottom: 1rem;
    }

    .discussion-actions {
        display: flex;
        gap: 1rem;
        flex-wrap: wrap;
    }

    .action-link {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        color: var(--primary-purple);
        text-decoration: none;
        font-size: 0.9rem;
        font-weight: 500;
        transition: color 0.2s ease;
    }

    .action-link:hover {
        color: var(--primary-navy);
        text-decoration: none;
    }

    .action-link.danger {
        color: var(--danger-red);
    }

    .action-link.danger:hover {
        color: #DC2626;
    }

    .empty-state {
        padding: 4rem 2rem;
        text-align: center;
        color: var(--text-gray);
    }

    .empty-state i {
        font-size: 4rem;
        color: var(--primary-grey);
        margin-bottom: 1rem;
    }

    .empty-state h3 {
        font-size: 1.5rem;
        color: var(--primary-navy);
        margin-bottom: 0.5rem;
    }

    .empty-state p {
        margin-bottom: 2rem;
        font-size: 1.1rem;
    }

    .pagination-wrapper {
        padding: 1.5rem 2rem;
        border-top: 1px solid var(--light-gray);
        background: var(--light-lavender);
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .discussions-container {
            padding: 1rem;
        }

        .page-header {
            flex-direction: column;
            align-items: stretch;
        }

        .filters-form {
            grid-template-columns: 1fr;
            gap: 1rem;
        }

        .filter-row {
            flex-direction: column;
            align-items: stretch;
        }

        .search-group {
            flex-direction: column;
        }

        .discussion-content {
            flex-direction: column;
        }

        .discussion-header {
            flex-direction: column;
            align-items: stretch;
            gap: 0.5rem;
        }

        .discussion-actions {
            justify-content: center;
        }

        .card-header {
            flex-direction: column;
            gap: 1rem;
            align-items: stretch;
        }
    }

    @media (max-width: 480px) {
        .discussions-container {
            padding: 0.5rem;
        }

        .discussion-item {
            padding: 1rem;
        }

        .card-header {
            padding: 1rem;
        }

        .discussion-badges {
            justify-content: center;
        }
    }
</style>
@endpush

@section('content')
<div class="discussions-container">
    <!-- Page Header -->
    <div class="page-header">
        <div class="page-title-section">
            <h1>Discussions</h1>
            <p class="page-subtitle">{{ $group->name }}</p>
        </div>
        <div class="header-actions">
            <a href="{{ route('group.show', ['group' => $group->id]) }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i>
                Back to Group
            </a>
            <a href="{{ route('group.create_discussion', ['id' => $group->id]) }}" class="btn btn-primary">
                <i class="fas fa-plus"></i>
                Start Discussion
            </a>
        </div>
    </div>

    <!-- Filters -->
    <div class="filters-card">
        {{ html()->modelForm($input, 'GET', route('group.discussions', ['id' => $group->id]))->id('filtersForm')->open() }}
        <div class="filters-form">
            <div class="filter-group">
                <label class="filter-label">Date Range</label>
                <div class="filter-row">
                    {{ html()->text('from_date')
                        ->class('form-control small')
                        ->placeholder('From date')
                        ->id('from_date') }}
                    <span>to</span>
                    {{ html()->text('to_date')
                        ->class('form-control small')
                        ->placeholder('To date')
                        ->id('to_date') }}
                </div>
            </div>
            
            <div class="filter-group">
                <label class="filter-label">Sort & Search</label>
                <div class="filter-row">
                    {{ html()->select('sort', [
                        'newest' => 'Newest',
                        'oldest' => 'Oldest',
                        'alpha' => 'A-Z',
                        'revalpha' => 'Z-A'
                    ])->class('form-control small') }}
                    <div class="search-group">
                        {{ html()->text('search')
                            ->class('form-control small search-input')
                            ->placeholder('Search discussions...') }}
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        {{ html()->closeModelForm() }}
    </div>

    <!-- Discussions List -->
    <div class="discussions-card">
        <div class="card-header">
            <h2 class="card-title">
                {{ $discussions->total() }} Discussion{{ $discussions->total() !== 1 ? 's' : '' }}
            </h2>
        </div>

        @if($discussions->count() > 0)
            @foreach($discussions as $discussion)
                <div class="discussion-item">
                    <div class="discussion-content">
                        <div class="discussion-avatar">
                            @if($discussion->creator && $discussion->creator->profile_pic)
                                <img src="{{ $discussion->creator->profile_pic }}" alt="{{ $discussion->creator->firstname }} {{ $discussion->creator->lastname }}">
                            @else
                                <img src="{{ asset('images/profile-placeholder.png') }}" alt="User">
                            @endif
                        </div>
                        
                        <div class="discussion-body">
                            <div class="discussion-header">
                                <a href="{{ route('discussion.show', ['discussion' => $discussion->id]) }}" class="discussion-title">
                                    {{ $discussion->title }}
                                </a>
                                <span class="discussion-time">{{ $discussion->created_at->diffForHumans() }}</span>
                            </div>
                            
                            <p class="discussion-meta">
                                by {{ $discussion->creator ? $discussion->creator->firstname . ' ' . $discussion->creator->lastname : 'Unknown User' }}
                            </p>
                            
                            <div class="discussion-badges">
                                <span class="badge badge-{{ $discussion->status === 'open' ? 'open' : 'closed' }}">
                                    {{ ucfirst($discussion->status ?? 'open') }}
                                </span>
                                @if($discussion->posts_count ?? 0 > 0)
                                    <span class="badge badge-read">
                                        {{ $discussion->posts_count }} Comment{{ $discussion->posts_count !== 1 ? 's' : '' }}
                                    </span>
                                @endif
                            </div>
                            
                            @if($discussion->posts && $discussion->posts->first())
                                <p class="discussion-description">
                                    {{ \Illuminate\Support\Str::limit(strip_tags($discussion->posts->first()->comment ?? ''), 200) }}
                                </p>
                            @endif
                            
                            <div class="discussion-actions">
                                <a href="{{ route('discussion.show', ['discussion' => $discussion->id]) }}" class="action-link">
                                    <i class="fas fa-eye"></i>
                                    View Discussion
                                </a>
                                
                                @if($discussion->canEdit(Auth::User()))
                                    <a href="{{ route('discussion.edit', ['discussion' => $discussion->id]) }}" class="action-link">
                                        <i class="fas fa-edit"></i>
                                        Edit
                                    </a>
                                @endif
                                
                                @if($discussion->canDelete(Auth::User()))
                                    <a href="{{ route('discussion.delete', ['discussion' => $discussion->id]) }}" 
                                       class="action-link danger delete-discussion" 
                                       onclick="return confirm('Are you sure you want to delete this discussion? This action cannot be undone.')">
                                        <i class="fas fa-trash"></i>
                                        Delete
                                    </a>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            @endforeach
            
            @if($discussions->hasPages())
                <div class="pagination-wrapper">
                    {{ $discussions->links() }}
                </div>
            @endif
        @else
            <div class="empty-state">
                <i class="fas fa-comments"></i>
                <h3>No Discussions Yet</h3>
                <p>Be the first to start a discussion in this group!</p>
                <a href="{{ route('group.create_discussion', ['id' => $group->id]) }}" class="btn btn-primary">
                    <i class="fas fa-plus"></i>
                    Start First Discussion
                </a>
            </div>
        @endif
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize date pickers if available
    if (typeof flatpickr !== 'undefined') {
        flatpickr('#from_date', {
            dateFormat: 'Y-m-d',
            placeholder: 'From date'
        });
        
        flatpickr('#to_date', {
            dateFormat: 'Y-m-d',
            placeholder: 'To date'
        });
    }
    
    // Auto-submit form on sort change
    const sortSelect = document.querySelector('select[name="sort"]');
    if (sortSelect) {
        sortSelect.addEventListener('change', function() {
            document.getElementById('filtersForm').submit();
        });
    }
    
    // Handle delete confirmations
    const deleteLinks = document.querySelectorAll('.delete-discussion');
    deleteLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            if (!confirm('Are you sure you want to delete this discussion? This action cannot be undone.')) {
                e.preventDefault();
            }
        });
    });
});
</script>
@endpush 