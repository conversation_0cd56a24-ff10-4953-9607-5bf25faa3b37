@extends('layouts.dashboard-new')

@section('title')
    Edit Discussion
@stop

@push('styles')
<style>
    :root {
        --primary-purple: #8347CC;
        --primary-navy: #5144A1;
        --primary-grey: #CECECE;
        --light-lavender: #F0F0F9;
        --light-white: #FFFFFF;
        --light-gray: #E5E7EB;
        --text-gray: #6B7280;
        --border-color: #E0E0E0;
    }

    .edit-discussion-container {
        max-width: 800px;
        margin: 0 auto;
        padding: 2rem;
    }

    .back-link {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        color: var(--primary-purple);
        text-decoration: none;
        margin-bottom: 2rem;
        font-weight: 500;
        transition: color 0.2s ease;
    }

    .back-link:hover {
        color: var(--primary-navy);
        text-decoration: none;
    }

    .page-header {
        margin-bottom: 2rem;
    }

    .page-title {
        font-size: 2rem;
        font-weight: 700;
        color: var(--primary-navy);
        margin-bottom: 0.5rem;
    }

    .breadcrumb {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        color: var(--text-gray);
        font-size: 0.875rem;
        margin-bottom: 1rem;
    }

    .breadcrumb a {
        color: var(--primary-purple);
        text-decoration: none;
    }

    .breadcrumb a:hover {
        text-decoration: underline;
    }

    .discussion-form-card {
        background: var(--light-white);
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        overflow: hidden;
    }

    .form-header {
        background: var(--light-lavender);
        padding: 1.5rem 2rem;
        border-bottom: 1px solid var(--border-color);
    }

    .form-header h2 {
        font-size: 1.25rem;
        font-weight: 600;
        color: var(--primary-navy);
        margin: 0;
    }

    .form-body {
        padding: 2rem;
    }

    .description-text {
        color: var(--text-gray);
        line-height: 1.6;
        margin-bottom: 2rem;
        font-size: 0.95rem;
    }

    .form-group {
        margin-bottom: 1.5rem;
    }

    .form-label {
        display: block;
        font-weight: 600;
        color: var(--primary-navy);
        margin-bottom: 0.5rem;
        font-size: 0.95rem;
    }

    .form-control {
        width: 100%;
        padding: 0.75rem;
        border: 1px solid var(--border-color);
        border-radius: 8px;
        font-size: 0.95rem;
        transition: border-color 0.2s ease, box-shadow 0.2s ease;
        background: var(--light-white);
    }

    .form-control:focus {
        border-color: var(--primary-purple);
        box-shadow: 0 0 0 3px rgba(131, 71, 204, 0.1);
        outline: none;
    }

    .form-control.select {
        cursor: pointer;
    }

    .error-message {
        color: #DC2626;
        font-size: 0.875rem;
        margin-top: 0.25rem;
        display: block;
    }

    .form-actions {
        display: flex;
        gap: 1rem;
        justify-content: flex-end;
        margin-top: 2rem;
        padding-top: 1.5rem;
        border-top: 1px solid var(--light-gray);
    }

    .btn {
        padding: 0.75rem 1.5rem;
        border-radius: 8px;
        font-weight: 600;
        font-size: 0.95rem;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        cursor: pointer;
        transition: all 0.2s ease;
        border: none;
    }

    .btn-secondary {
        background: var(--light-gray);
        color: var(--primary-navy);
        border: 1px solid var(--border-color);
    }

    .btn-secondary:hover {
        background: #D1D5DB;
        text-decoration: none;
        color: var(--primary-navy);
    }

    .btn-primary {
        background: var(--primary-purple);
        color: var(--light-white);
        border: 1px solid var(--primary-purple);
    }

    .btn-primary:hover {
        background: var(--primary-navy);
        border-color: var(--primary-navy);
    }

    .btn-warning {
        background: #F59E0B;
        color: var(--light-white);
        border: 1px solid #F59E0B;
    }

    .btn-warning:hover {
        background: #D97706;
        border-color: #D97706;
    }

    .attach-request-section {
        background: var(--light-lavender);
        border-radius: 8px;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        border: 1px solid var(--border-color);
    }

    .attach-request-section h3 {
        font-size: 1.1rem;
        font-weight: 600;
        color: var(--primary-navy);
        margin-bottom: 0.5rem;
    }

    .attach-request-section p {
        color: var(--text-gray);
        margin-bottom: 1rem;
        font-size: 0.9rem;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .edit-discussion-container {
            padding: 1rem;
        }

        .form-body {
            padding: 1.5rem;
        }

        .form-header {
            padding: 1rem 1.5rem;
        }

        .page-title {
            font-size: 1.5rem;
        }

        .form-actions {
            flex-direction: column-reverse;
        }

        .btn {
            width: 100%;
            justify-content: center;
        }
    }

    @media (max-width: 480px) {
        .edit-discussion-container {
            padding: 0.5rem;
        }

        .form-body {
            padding: 1rem;
        }

        .form-header {
            padding: 1rem;
        }

        .breadcrumb {
            flex-wrap: wrap;
        }
    }
</style>
@endpush

@section('content')
<div class="edit-discussion-container">
    <!-- Back Navigation -->
    <a href="{{ route('discussion.show', ['discussion' => $discussion->id]) }}" class="back-link">
        <i class="fas fa-arrow-left"></i>
        Back to Discussion
    </a>

    <!-- Page Header -->
    <div class="page-header">
        <div class="breadcrumb">
            <a href="{{ route('group.discussions', ['id' => $group->id]) }}">{{ $group->name }}</a>
            <i class="fas fa-chevron-right"></i>
            <a href="{{ route('discussion.show', ['discussion' => $discussion->id]) }}">{{ $discussion->title }}</a>
            <i class="fas fa-chevron-right"></i>
            <span>Edit</span>
        </div>
        <h1 class="page-title">Edit Discussion</h1>
    </div>

    <!-- Discussion Form -->
    <div class="discussion-form-card">
        <div class="form-header">
            <h2>Discussion Details</h2>
        </div>

        <div class="form-body">
            <p class="description-text">
                Update the details below to modify this group discussion. You can change the title and status of the discussion.
            </p>

            {{ html()->modelForm($discussion, 'PUT', route('discussion.update', $discussion->id))->id('discussionForm')->open() }}

                @if( !$discussion->hasIssue() && $group->canCreateRequest(Auth::User()) )
                <div class="attach-request-section">
                    <h3>Attach a Request</h3>
                    <p>You can convert this discussion into a formal request that your group can vote on.</p>
                    <a href="{{ route('discussion.new_issue', ['discussion' => $discussion->id]) }}" class="btn btn-warning">
                        <i class="fas fa-link"></i>
                        Attach a Request
                    </a>
                </div>
                @endif

                <div class="form-group">
                    <label for="title" class="form-label">Discussion Title</label>
                    {{ html()->text('title')
                        ->class('form-control' . ($errors->has('title') ? ' is-invalid' : ''))
                        ->id('title')
                        ->placeholder('Enter a clear, descriptive title for your discussion')
                        ->required() }}
                    @error('title')
                        <span class="error-message">{{ $message }}</span>
                    @enderror
                </div>

                <div class="form-group">
                    <label for="status" class="form-label">Discussion Status</label>
                    {{ html()->select('status', [
                        '' => 'Please Select',
                        'open' => 'Open',
                        'closed' => 'Closed'
                    ])
                        ->class('form-control select' . ($errors->has('status') ? ' is-invalid' : ''))
                        ->id('status')
                        ->required() }}
                    @error('status')
                        <span class="error-message">{{ $message }}</span>
                    @enderror
                </div>

                <div class="form-actions">
                    <a href="{{ route('discussion.show', ['discussion' => $discussion->id]) }}" class="btn btn-secondary">
                        <i class="fas fa-times"></i>
                        Cancel
                    </a>
                    <button type="submit" class="btn btn-primary" id="submitBtn">
                        <i class="fas fa-save"></i>
                        Save Changes
                    </button>
                </div>
            {{ html()->closeModelForm() }}
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('discussionForm');
    const submitBtn = document.getElementById('submitBtn');
    
    // Form submission handling
    form.addEventListener('submit', function(e) {
        // Add loading state
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Saving...';
        
        // Basic validation
        const title = document.getElementById('title').value.trim();
        const status = document.getElementById('status').value;
        
        if (!title || !status) {
            e.preventDefault();
            submitBtn.disabled = false;
            submitBtn.innerHTML = '<i class="fas fa-save"></i> Save Changes';
            
            // Focus on first empty field
            if (!title) {
                document.getElementById('title').focus();
            } else if (!status) {
                document.getElementById('status').focus();
            }
        }
    });
});
</script>
@endpush 