@extends('layouts.dashboard-new')

@section('title')
    Start a Discussion
@stop

@push('styles')
<style>
    :root {
        --primary-purple: #8347CC;
        --primary-navy: #5144A1;
        --primary-grey: #CECECE;
        --light-lavender: #F0F0F9;
        --light-white: #FFFFFF;
        --light-gray: #E5E7EB;
        --text-gray: #6B7280;
        --border-color: #E0E0E0;
    }

    .create-discussion-container {
        max-width: 800px;
        margin: 0 auto;
        padding: 2rem;
    }

    .back-link {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        color: var(--primary-purple);
        text-decoration: none;
        margin-bottom: 2rem;
        font-weight: 500;
        transition: color 0.2s ease;
    }

    .back-link:hover {
        color: var(--primary-navy);
        text-decoration: none;
    }

    .page-header {
        margin-bottom: 2rem;
    }

    .page-title {
        font-size: 2rem;
        font-weight: 700;
        color: var(--primary-navy);
        margin-bottom: 0.5rem;
    }

    .breadcrumb {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        color: var(--text-gray);
        font-size: 0.875rem;
        margin-bottom: 1rem;
    }

    .breadcrumb a {
        color: var(--primary-purple);
        text-decoration: none;
    }

    .breadcrumb a:hover {
        text-decoration: underline;
    }

    .discussion-form-card {
        background: var(--light-white);
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        overflow: hidden;
    }

    .form-header {
        background: var(--light-lavender);
        padding: 1.5rem 2rem;
        border-bottom: 1px solid var(--border-color);
    }

    .form-header h2 {
        font-size: 1.25rem;
        font-weight: 600;
        color: var(--primary-navy);
        margin: 0;
    }

    .form-body {
        padding: 2rem;
    }

    .description-text {
        color: var(--text-gray);
        line-height: 1.6;
        margin-bottom: 2rem;
        font-size: 0.95rem;
    }

    .form-group {
        margin-bottom: 1.5rem;
    }

    .form-label {
        display: block;
        font-weight: 600;
        color: var(--primary-navy);
        margin-bottom: 0.5rem;
        font-size: 0.95rem;
    }

    .form-control {
        width: 100%;
        padding: 0.75rem;
        border: 1px solid var(--border-color);
        border-radius: 8px;
        font-size: 0.95rem;
        transition: border-color 0.2s ease, box-shadow 0.2s ease;
        background: var(--light-white);
    }

    .form-control:focus {
        border-color: var(--primary-purple);
        box-shadow: 0 0 0 3px rgba(131, 71, 204, 0.1);
        outline: none;
    }

    .form-control.textarea {
        min-height: 120px;
        resize: vertical;
        font-family: inherit;
    }

    .form-control::placeholder {
        color: var(--text-gray);
    }

    .error-message {
        color: #DC2626;
        font-size: 0.875rem;
        margin-top: 0.25rem;
        display: block;
    }

    .form-actions {
        display: flex;
        gap: 1rem;
        justify-content: flex-end;
        margin-top: 2rem;
        padding-top: 1.5rem;
        border-top: 1px solid var(--light-gray);
    }

    .btn {
        padding: 0.75rem 1.5rem;
        border-radius: 8px;
        font-weight: 600;
        font-size: 0.95rem;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        cursor: pointer;
        transition: all 0.2s ease;
        border: none;
    }

    .btn-secondary {
        background: var(--light-gray);
        color: var(--primary-navy);
        border: 1px solid var(--border-color);
    }

    .btn-secondary:hover {
        background: #D1D5DB;
        text-decoration: none;
        color: var(--primary-navy);
    }

    .btn-primary {
        background: var(--primary-purple);
        color: var(--light-white);
        border: 1px solid var(--primary-purple);
    }

    .btn-primary:hover {
        background: var(--primary-navy);
        border-color: var(--primary-navy);
    }

    .btn-primary:disabled {
        background: var(--primary-grey);
        border-color: var(--primary-grey);
        cursor: not-allowed;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .create-discussion-container {
            padding: 1rem;
        }

        .form-body {
            padding: 1.5rem;
        }

        .form-header {
            padding: 1rem 1.5rem;
        }

        .page-title {
            font-size: 1.5rem;
        }

        .form-actions {
            flex-direction: column-reverse;
        }

        .btn {
            width: 100%;
            justify-content: center;
        }
    }

    @media (max-width: 480px) {
        .create-discussion-container {
            padding: 0.5rem;
        }

        .form-body {
            padding: 1rem;
        }

        .form-header {
            padding: 1rem;
        }

        .breadcrumb {
            flex-wrap: wrap;
        }
    }

    /* Loading state */
    .btn-primary.loading {
        position: relative;
        color: transparent;
    }

    .btn-primary.loading::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 20px;
        height: 20px;
        border: 2px solid transparent;
        border-top: 2px solid var(--light-white);
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: translate(-50%, -50%) rotate(0deg); }
        100% { transform: translate(-50%, -50%) rotate(360deg); }
    }
</style>
@endpush

@section('content')
<div class="create-discussion-container">
    <!-- Back Navigation -->
    <a href="{{ route('group.discussions', ['id' => $group->id]) }}" class="back-link">
        <i class="fas fa-arrow-left"></i>
        Back to Discussions
    </a>

    <!-- Page Header -->
    <div class="page-header">
        <div class="breadcrumb">
            <a href="{{ route('group.discussions', ['id' => $group->id]) }}">{{ $group->name }}</a>
            <i class="fas fa-chevron-right"></i>
            <span>Start a Discussion</span>
        </div>
        <h1 class="page-title">Start a Discussion</h1>
    </div>

    <!-- Discussion Form -->
    <div class="discussion-form-card">
        <div class="form-header">
            <h2>Discussion Details</h2>
        </div>

        <div class="form-body">
            <p class="description-text">
                Enter the details below to create a new group discussion. Discussions can be used to 
                communicate with your group. You may want to discuss group goals, size, new members 
                and objectives.
            </p>

            {{ html()->form('POST', route('discussion.store'))->id('discussionForm')->open() }}
                {{ html()->hidden('group_id', $group->id) }}

                <div class="form-group">
                    <label for="title" class="form-label">Discussion Title</label>
                    {{ html()->text('title')
                        ->class('form-control' . ($errors->has('title') ? ' is-invalid' : ''))
                        ->id('title')
                        ->placeholder('Enter a clear, descriptive title for your discussion')
                        ->value(old('title'))
                        ->required() }}
                    @error('title')
                        <span class="error-message">{{ $message }}</span>
                    @enderror
                </div>

                <div class="form-group">
                    <label for="comment" class="form-label">Discussion Description</label>
                    {{ html()->textarea('comment')
                        ->class('form-control textarea' . ($errors->has('comment') ? ' is-invalid' : ''))
                        ->id('comment')
                        ->placeholder('Enter details about what you want the group to discuss. Summarize the reason for the discussion and give background information. Include an invitation for group dialogue: e.g. "Do you have a suggestion for how we can best help?" "How might we shape our goals?" "Please share your thoughts."')
                        ->value(old('comment'))
                        ->rows(6)
                        ->required() }}
                    @error('comment')
                        <span class="error-message">{{ $message }}</span>
                    @enderror
                </div>

                <div class="form-actions">
                    <a href="{{ route('group.discussions', ['id' => $group->id]) }}" class="btn btn-secondary">
                        <i class="fas fa-times"></i>
                        Cancel
                    </a>
                    <button type="submit" class="btn btn-primary" id="submitBtn">
                        <i class="fas fa-plus"></i>
                        Create Discussion
                    </button>
                </div>
            {{ html()->form()->close() }}
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('discussionForm');
    const submitBtn = document.getElementById('submitBtn');
    
    // Form submission handling
    form.addEventListener('submit', function(e) {
        // Add loading state
        submitBtn.classList.add('loading');
        submitBtn.disabled = true;
        
        // Basic validation
        const title = document.getElementById('title').value.trim();
        const comment = document.getElementById('comment').value.trim();
        
        if (!title || !comment) {
            e.preventDefault();
            submitBtn.classList.remove('loading');
            submitBtn.disabled = false;
            
            // Focus on first empty field
            if (!title) {
                document.getElementById('title').focus();
            } else if (!comment) {
                document.getElementById('comment').focus();
            }
        }
    });
    
    // Auto-resize textarea
    const textarea = document.getElementById('comment');
    textarea.addEventListener('input', function() {
        this.style.height = 'auto';
        this.style.height = Math.max(120, this.scrollHeight) + 'px';
    });
    
    // Character count for title (optional enhancement)
    const titleInput = document.getElementById('title');
    titleInput.addEventListener('input', function() {
        if (this.value.length > 100) {
            this.style.borderColor = '#DC2626';
        } else {
            this.style.borderColor = '';
        }
    });
});
</script>
@endpush 