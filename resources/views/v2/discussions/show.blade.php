@extends('layouts.dashboard-new')

@section('title')
    {{ $discussion->title }}
@stop

@push('styles')
<style>
    .discussion-header {
        background: var(--light-white);
        border-radius: 12px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }

    .discussion-meta {
        display: flex;
        align-items: center;
        gap: 1rem;
        margin-bottom: 1rem;
        color: #6b7280;
        font-size: 0.875rem;
    }

    .discussion-actions {
        display: flex;
        gap: 1rem;
        margin-top: 1.5rem;
        flex-wrap: wrap;
    }

    .comments-section {
        background: var(--light-white);
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        overflow: hidden;
    }

    .comments-header {
        padding: 1.5rem;
        border-bottom: 1px solid var(--light-gray);
        background: var(--light-lavender);
    }

    .comments-header h3 {
        margin: 0;
        color: var(--primary-navy);
        font-size: 1.25rem;
        font-weight: 600;
    }

    .comment-item {
        padding: 1.5rem;
        border-bottom: 1px solid var(--light-gray);
        transition: background-color 0.2s ease;
    }

    .comment-item:last-child {
        border-bottom: none;
    }

    .comment-item:hover {
        background-color: var(--light-lavender);
    }

    .comment-content {
        display: flex;
        gap: 1rem;
    }

    .comment-avatar {
        flex-shrink: 0;
    }

    .comment-avatar img {
        width: 48px;
        height: 48px;
        border-radius: 50%;
        object-fit: cover;
    }

    .comment-body {
        flex: 1;
        min-width: 0;
    }

    .comment-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 0.5rem;
    }

    .comment-author {
        font-weight: 600;
        color: var(--primary-navy);
        margin-bottom: 0.25rem;
    }

    .comment-time {
        color: #6b7280;
        font-size: 0.875rem;
    }

    .comment-actions {
        display: flex;
        gap: 0.5rem;
    }

    .comment-text {
        color: var(--primary-navy);
        line-height: 1.6;
        margin: 0;
    }

    .comment-form {
        padding: 1.5rem;
        background: var(--light-gray);
    }

    .comment-form-content {
        display: flex;
        gap: 1rem;
        align-items: flex-start;
    }

    .comment-form-avatar {
        flex-shrink: 0;
    }

    .comment-form-avatar img {
        width: 48px;
        height: 48px;
        border-radius: 50%;
        object-fit: cover;
    }

    .comment-form-body {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }

    .comment-textarea {
        width: 100%;
        min-height: 100px;
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        padding: 0.75rem;
        font-size: 0.875rem;
        resize: vertical;
        transition: border-color 0.2s ease, box-shadow 0.2s ease;
    }

    .comment-textarea:focus {
        border-color: var(--primary-purple);
        box-shadow: 0 0 0 3px rgba(81, 68, 161, 0.1);
        outline: none;
    }

    .comment-form-actions {
        display: flex;
        justify-content: flex-end;
    }

    .btn-delete {
        background: none;
        border: none;
        color: #dc2626;
        padding: 0.25rem;
        border-radius: 4px;
        cursor: pointer;
        transition: background-color 0.2s ease;
    }

    .btn-delete:hover {
        background: #fee2e2;
    }

    .empty-comments {
        padding: 3rem;
        text-align: center;
        color: #6b7280;
    }

    .empty-comments i {
        font-size: 3rem;
        color: var(--primary-grey);
        margin-bottom: 1rem;
    }

    .back-link {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        color: var(--primary-purple);
        text-decoration: none;
        margin-bottom: 1.5rem;
        font-weight: 500;
        transition: color 0.2s ease;
    }

    .back-link:hover {
        color: #4a3d91;
        text-decoration: none;
    }

    @media (max-width: 768px) {
        .discussion-header {
            padding: 1.5rem;
        }

        .discussion-actions {
            flex-direction: column;
        }

        .comment-content {
            flex-direction: column;
            gap: 0.75rem;
        }

        .comment-form-content {
            flex-direction: column;
            gap: 0.75rem;
        }
    }
</style>
@endpush

@section('content')
<div class="content-body">
    <div class="container">
        <!-- Back Link -->
        <a href="{{ url()->previous() }}" class="back-link">
            <i class="fas fa-arrow-left"></i>
            Back to Discussions
        </a>

        <!-- Discussion Header -->
        <div class="discussion-header">
            <h1>{{ $discussion->title }}</h1>
            
            <div class="discussion-meta">
                <div class="discussion-author">
                    <i class="fas fa-user"></i>
                    by {{ $discussion->creator->firstname }} {{ $discussion->creator->lastname }}
                </div>
                <div class="discussion-date">
                    <i class="fas fa-calendar"></i>
                    {{ $discussion->created_at->format('M j, Y \a\t g:i A') }}
                </div>
                <div class="discussion-group">
                    <i class="fas fa-users"></i>
                    {{ $discussion->group->name }}
                </div>
            </div>

            @if($discussion->canEdit(Auth::User()) || $discussion->canDelete(Auth::User()))
                <div class="discussion-actions">
                    @if($discussion->canEdit(Auth::User()))
                        <a href="{{ URL::route('discussion.edit', $discussion->id) }}" class="btn btn-secondary">
                            <i class="fas fa-edit"></i>
                            Edit Discussion
                        </a>
                    @endif
                    
                    @if($discussion->canDelete(Auth::User()))
                        <button type="button" class="btn btn-delete" id="delete_discussion" data-url="{{ URL::route('discussion.delete', $discussion->id) }}">
                            <i class="fas fa-trash"></i>
                            Delete Discussion
                        </button>
                    @endif
                </div>
            @endif
        </div>

        <!-- Comments Section -->
        <div class="comments-section">
            <div class="comments-header">
                <h3>
                    <i class="fas fa-comments"></i>
                    Comments ({{ $discussion->posts->count() }})
                </h3>
            </div>

            @if($discussion->posts->count() > 0)
                @foreach($discussion->posts as $post)
                    <div class="comment-item">
                        <div class="comment-content">
                            <div class="comment-avatar">
                                @if($post->author && $post->author->profile_pic)
                                    <img src="{{ $post->author->profile_pic }}" alt="{{ $post->author->firstname }} {{ $post->author->lastname }}">
                                @else
                                    <img src="{{ asset('images/profile-placeholder.png') }}" alt="User">
                                @endif
                            </div>
                            <div class="comment-body">
                                <div class="comment-header">
                                    <div>
                                        <div class="comment-author">
                                            @if($post->author)
                                                {{ $post->author->firstname }} {{ $post->author->lastname }}
                                            @else
                                                Deleted User
                                            @endif
                                        </div>
                                        <div class="comment-time" title="{{ $post->created_local.' '.$post->created_local->tzName }}">
                                            {{ $post->created_at->diffForHumans() }}
                                        </div>
                                    </div>
                                    @if($post->canDelete(Auth::User()))
                                        <div class="comment-actions">
                                            <button type="button" class="btn-delete delete_comment" data-url="{{ URL::route('post.delete', $post->id) }}" title="Delete this comment">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    @endif
                                </div>
                                <div class="comment-text">
                                    {!! $post->niceComment() !!}
                                </div>
                            </div>
                        </div>
                    </div>
                @endforeach
            @else
                <div class="empty-comments">
                    <i class="fas fa-comment-slash"></i>
                    <h4>No comments yet</h4>
                    <p>Be the first to share your thoughts on this discussion.</p>
                </div>
            @endif

            <!-- Comment Form -->
            <div class="comment-form">
                {{ html()->form('POST', route('discussion.post', $discussion->id))->attribute('name', 'post')->open() }}
                    <div class="comment-form-content">
                        <div class="comment-form-avatar">
                            @if(Auth::User()->profile_pic)
                                <img src="{{ Auth::User()->profile_pic }}" alt="{{ Auth::User()->firstname }} {{ Auth::User()->lastname }}">
                            @else
                                <img src="{{ asset('images/profile-placeholder.png') }}" alt="You">
                            @endif
                        </div>
                        <div class="comment-form-body">
                            {{ html()->textarea('comment')->class('comment-textarea')->placeholder('Write a comment...')->required() }}
                            <div class="comment-form-actions">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-paper-plane"></i>
                                    Post Comment
                                </button>
                            </div>
                        </div>
                    </div>
                {{ html()->form()->close() }}
            </div>
        </div>
    </div>
</div>
@stop

@section('scripts')
<script>
$(document).ready(function() {
    // Delete comment confirmation
    $('.delete_comment').click(function(event) {
        event.preventDefault();
        var url = $(this).data('url');
        var r = confirm("Are you sure you want to delete this comment?");
        if (r == true) {
            window.location = url;
        }
    });

    // Delete discussion confirmation
    $('#delete_discussion').click(function(event) {
        event.preventDefault();
        var url = $(this).data('url');
        var r = confirm("Are you sure you want to delete this discussion?");
        if (r == true) {
            window.location = url;
        }
    });

    // Auto-resize textarea
    $('.comment-textarea').on('input', function() {
        this.style.height = 'auto';
        this.style.height = (this.scrollHeight) + 'px';
    });
});
</script>
@stop 