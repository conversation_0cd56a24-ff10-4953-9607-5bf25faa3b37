@extends('layouts.dashboard-new')

@inject('locationHelper', 'App\\Models\\LocationHelper')

@push('styles')
<style>
    /* Groups discovery page styles */
    .content-body .container {
        margin: 0 auto;
        padding: 20px;
        background-color: transparent !important;
    }

    .content-body .page-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 30px;
    }

    .content-body .page-title {
        font-size: 28px;
        font-weight: 600;
        color: #4D5E80;
        margin: 0;
    }

    .content-body .page-subtitle {
        color: #7D8FB3;
        font-size: 16px;
        margin-top: 4px;
    }

    .content-body .filters-section {
        background: white;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 30px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .content-body .filters {
        display: flex;
        gap: 20px;
        align-items: center;
        flex-wrap: wrap;
    }

    .content-body .filter-group {
        display: flex;
        flex-direction: column;
        gap: 8px;
    }

    .content-body .filter-label {
        font-weight: 500;
        color: #4D5E80;
        font-size: 14px;
    }

    .content-body .filter-select {
        padding: 8px 12px;
        border: 1px solid #E5E7EB;
        border-radius: 6px;
        background: #FAFBFC;
        color: #4D5E80;
        font-size: 14px;
        min-width: 150px;
    }

    .content-body .filter-select:focus {
        outline: none;
        border-color: #5144A1;
    }

    .content-body .search-container {
        flex: 1;
        max-width: 300px;
    }

    .content-body .search-input {
        width: 100%;
        padding: 8px 12px 8px 35px;
        border: 1px solid #E5E7EB;
        border-radius: 6px;
        font-size: 14px;
        background: #FAFBFC url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23999' stroke-width='2'%3E%3Ccircle cx='11' cy='11' r='8'/%3E%3Cpath d='m21 21-4.35-4.35'/%3E%3C/svg%3E") no-repeat 10px center;
    }

    .content-body .search-input:focus {
        outline: none;
        border-color: #5144A1;
    }

    .content-body .groups-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
        gap: 24px;
        margin-bottom: 30px;
    }

    .content-body .group-card {
        background: white;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        transition: transform 0.2s, box-shadow 0.2s;
        border: 1px solid #F5F6F7;
    }

    .content-body .group-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
    }

    .content-body .group-image {
        height: 180px;
        background-size: cover;
        background-position: center;
        position: relative;
        display: flex;
        align-items: flex-end;
        padding: 20px;
    }

    .content-body .group-title {
        color: white;
        font-size: 18px;
        font-weight: 600;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.6);
        margin: 0;
    }

    .content-body .group-content {
        padding: 20px;
    }

    .content-body .group-badge {
        display: inline-block;
        padding: 4px 8px;
        background-color: #F0F0FF;
        color: #5144A1;
        font-size: 12px;
        font-weight: 500;
        border-radius: 4px;
        text-transform: uppercase;
        margin-bottom: 12px;
    }

    .content-body .group-meta {
        margin-bottom: 16px;
    }

    .content-body .group-location {
        color: #7D8FB3;
        font-size: 14px;
        margin-bottom: 8px;
    }

    .content-body .group-description {
        color: #4D5E80;
        font-size: 14px;
        line-height: 1.5;
        margin-bottom: 16px;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    .content-body .group-stats {
        display: flex;
        gap: 16px;
        margin-bottom: 16px;
        font-size: 14px;
        color: #7D8FB3;
    }

    .content-body .group-stat {
        display: flex;
        align-items: center;
        gap: 4px;
    }

    .content-body .group-actions {
        display: flex;
        gap: 12px;
    }

    .content-body .btn {
        padding: 8px 16px;
        border-radius: 6px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s;
        text-decoration: none;
        display: inline-block;
        text-align: center;
        border: none;
    }

    .content-body .btn-primary {
        background-color: #5144A1;
        color: white;
    }

    .content-body .btn-primary:hover {
        background-color: #3d3380;
        color: white;
        text-decoration: none;
    }

    .content-body .btn-outline {
        background-color: transparent;
        color: #5144A1;
        border: 1px solid #5144A1;
    }

    .content-body .btn-outline:hover {
        background-color: #5144A1;
        color: white;
        text-decoration: none;
    }

    .content-body .pagination-wrapper {
        display: flex;
        justify-content: center;
        margin-top: 30px;
    }

    .content-body .empty-state {
        text-align: center;
        padding: 60px 20px;
        background: #F9FAFB;
        border-radius: 12px;
        border: 2px dashed #E5E7EB;
    }

    .content-body .empty-state h3 {
        color: #4D5E80;
        margin-bottom: 8px;
    }

    .content-body .empty-state p {
        color: #7D8FB3;
        margin-bottom: 20px;
    }

    @media (max-width: 768px) {
        .content-body .groups-grid {
            grid-template-columns: 1fr;
        }

        .content-body .filters {
            flex-direction: column;
            align-items: stretch;
        }

        .content-body .search-container {
            max-width: none;
        }

        .content-body .page-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 16px;
        }
    }
</style>
@endpush

@section('content')
<div class="container">
    <div class="page-header">
        <div>
            <h1 class="page-title">Discover Groups</h1>
            <p class="page-subtitle">Find and join groups in your community</p>
        </div>
    </div>

    <div class="filters-section">
        <form method="GET" action="{{ route('group.index') }}" class="filters">
            <div class="filter-group">
                <label class="filter-label">Sort by</label>
                <select name="sort" class="filter-select" onchange="this.form.submit()">
                    <option value="alpha" {{ request('sort') == 'alpha' ? 'selected' : '' }}>Alphabetical (A-Z)</option>
                    <option value="revalpha" {{ request('sort') == 'revalpha' ? 'selected' : '' }}>Alphabetical (Z-A)</option>
                    <option value="state" {{ request('sort') == 'state' ? 'selected' : '' }}>By State</option>
                    <option value="country" {{ request('sort') == 'country' ? 'selected' : '' }}>By Country</option>
                </select>
            </div>

            <div class="search-container">
                <input type="text" name="search" class="search-input" placeholder="Search groups..." value="{{ request('search') }}">
            </div>

            <button type="submit" class="btn btn-primary">Search</button>
        </form>
    </div>

    <div class="groups-grid">
        @forelse($groups as $duple)
            @foreach($duple as $group)
                <div class="group-card">
                    <div class="group-image" style="background-image: linear-gradient(rgba(0,0,0,0.3), rgba(0,0,0,0.5)), url('{{ $group->group_banner_image ? asset('storage/' . $group->group_banner_image) : asset('images/group-placeholder.jpg') }}');">
                        <h3 class="group-title">{{ $group->name }}</h3>
                    </div>
                    <div class="group-content">
                        <div class="group-badge">
                            {{ $group->type == 'public' ? 'Public Group' : ($group->type == 'private' ? 'Private Group' : 'Secret Group') }}
                        </div>
                        <div class="group-meta">
                            <div class="group-location">
                                <strong>Location:</strong>
                                @if($group->city)
                                    {{ $group->city }}{{ $group->state && $group->state != 'zzzz' ? ', ' . $locationHelper->subdivisionName($group->state) : '' }}{{ $group->country ? ', ' . $locationHelper->countryName($group->country) : '' }}
                                @else
                                    Not specified
                                @endif
                            </div>
                            <div class="group-description">
                                {{ $group->description ?: 'This group needs a description.' }}
                            </div>
                        </div>
                        <div class="group-stats">
                            <div class="group-stat">
                                <i class="fas fa-users"></i>
                                {{ $group->members->count() }} {{ Str::plural('member', $group->members->count()) }}
                            </div>
                            <div class="group-stat">
                                <i class="fas fa-calendar"></i>
                                {{ $group->created_at->format('M Y') }}
                            </div>
                        </div>
                        <div class="group-actions">
                            @if($group->isMember(Auth::user()))
                                <a href="{{ route('group.show', $group->id) }}" class="btn btn-primary">View Group</a>
                            @else
                                @if($group->type == 'public')
                                    <a href="{{ route('group.join', $group->id) }}" class="btn btn-primary">Join Group</a>
                                @elseif($group->type == 'private')
                                    <a href="{{ route('group.join', $group->id) }}" class="btn btn-outline">Request to Join</a>
                                @endif
                                <a href="{{ route('group.show', $group->id) }}" class="btn btn-outline">View Details</a>
                            @endif
                        </div>
                    </div>
                </div>
            @endforeach
        @empty
            <div class="empty-state">
                <h3>No groups found</h3>
                <p>Try adjusting your search criteria or create a new group to get started.</p>
                <a href="{{ route('group.create') }}" class="btn btn-primary">Create a Group</a>
            </div>
        @endforelse
    </div>

    @if(isset($pGroups) && $pGroups->hasPages())
        <div class="pagination-wrapper">
            {{ $pGroups->links() }}
        </div>
    @endif
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Search functionality with debounce
    const searchInput = document.querySelector('.search-input');
    const form = searchInput.closest('form');
    let searchTimeout;

    if (searchInput) {
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                form.submit();
            }, 500); // 500ms debounce
        });
    }

    // Add loading state to buttons
    const actionButtons = document.querySelectorAll('.btn-primary, .btn-outline');
    actionButtons.forEach(button => {
        button.addEventListener('click', function() {
            if (this.href && !this.href.includes('#')) {
                this.style.opacity = '0.7';
                this.style.pointerEvents = 'none';

                // Reset after 3 seconds in case navigation fails
                setTimeout(() => {
                    this.style.opacity = '';
                    this.style.pointerEvents = '';
                }, 3000);
            }
        });
    });

    // Smooth scroll for pagination
    const paginationLinks = document.querySelectorAll('.pagination a');
    paginationLinks.forEach(link => {
        link.addEventListener('click', function() {
            setTimeout(() => {
                document.querySelector('.page-header').scrollIntoView({
                    behavior: 'smooth'
                });
            }, 100);
        });
    });
});
</script>
@endpush
