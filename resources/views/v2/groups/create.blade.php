@extends('layouts.dashboard-new')

@inject('locationHelper', 'App\\Models\\LocationHelper')

@push('styles')
<style>
    .content-body .container {
        margin: 0 20px;
        padding: 40px 0 0 0;
        /* max-width: 700px; */
        background: transparent;
    }
    .content-body .v2-create-title {
        font-size: 2rem;
        font-weight: 600;
        margin-bottom: 16px;
        color: #333;
    }
    .content-body .v2-create-desc {
        color: #444;
        font-size: 16px;
        margin-bottom: 24px;
    }
    .content-body .v2-create-note {
        background: #f5f3ff;
        border: 1px solid #e0e7ff;
        border-radius: 8px;
        padding: 18px 20px;
        margin-bottom: 32px;
        color: #5b4b8a;
        font-size: 15px;
    }
    .content-body .v2-create-form {
        background: white;
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.04);
        padding: 32px 32px 24px 32px;
        display: flex;
        flex-direction: column;
        gap: 24px;
    }
    .content-body .v2-form-group {
        display: flex;
        flex-direction: column;
        gap: 8px;
    }
    .content-body .v2-form-label {
        font-weight: 500;
        color: #333;
        font-size: 15px;
    }
    .content-body .v2-form-input, .content-body .v2-form-select, .content-body .v2-form-textarea {
        border: 1px solid #e5e7eb;
        border-radius: 6px;
        padding: 10px 14px;
        font-size: 15px;
        background: #fafbfc;
        color: #222;
        width: 100%;
        resize: none;
    }
    .content-body .v2-form-textarea {
        min-height: 90px;
    }
    .content-body .v2-form-select {
        appearance: none;
        background: #fafbfc url('data:image/svg+xml;utf8,<svg fill="none" height="20" viewBox="0 0 20 20" width="20" xmlns="http://www.w3.org/2000/svg"><path d="M6 8L10 12L14 8" stroke="%23999" stroke-width="2"/></svg>') no-repeat right 12px center/18px 18px;
    }
    .content-body .v2-form-hint {
        color: #888;
        font-size: 13px;
    }
    .content-body .v2-form-row {
        display: flex;
        gap: 20px;
    }
    .content-body .v2-form-row > .v2-form-group {
        flex: 1;
    }
    .content-body .v2-create-btn {
        background: #5144A1;
        color: #fff;
        border: none;
        border-radius: 6px;
        padding: 12px 0;
        font-size: 16px;
        font-weight: 600;
        margin-top: 12px;
        cursor: pointer;
        transition: background 0.2s;
    }
    .content-body .v2-create-btn:hover {
        background: #3d3380;
    }
</style>
@endpush

@section('content')
<div class="container">
    @if ($errors->any())
        <div class="alert alert-danger" style="margin-bottom: 24px;">
            <ul style="margin:0; padding-left: 20px;">
                @foreach ($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
        </div>
    @endif
    <div class="v2-create-title">Create a Group</div>
    <div class="v2-create-desc">
        Thank you for choosing to create a new Common Change group. The form below will guide you through creating a group. You can "chat" with us at any time if you want help, by clicking on the Chat window on the bottom right of the screen.
    </div>
    <div class="v2-create-note">
        <strong>IMPORTANT NOTE</strong><br>
        Groups cannot limit their purpose to the support of a single individual (e.g. fundraising for a single person with cancer, fundraising for a trip, etc). Many other crowd-funding platforms are already available for this type of fundraising. Common Change groups are to be used to pool resources which are then distributed to a variety of needy individuals (not organizations) based on the collective input of group members.<br>
        <a href="#" style="color:#5144A1;text-decoration:underline;">Read the Full Group Fund Policy document</a>
    </div>
    <form class="v2-create-form" method="POST" action="{{ route('group.store') }}">
        @csrf
        <div class="v2-form-group">
            <label class="v2-form-label" for="name">Group Name</label>
            <input class="v2-form-input" type="text" id="name" name="name" placeholder="Group Name" required>
        </div>
        <div class="v2-form-group" style="display:none;">
            <input type="hidden" id="shortname" name="shortname">
        </div>
        <div class="v2-form-group">
            <label class="v2-form-label" for="url_display">Group URL <span class="v2-form-hint">(automatic based on name)</span></label>
            <input class="v2-form-input" type="text" id="url_display" name="url_display" placeholder="Group URL" readonly>
        </div>
        <div class="v2-form-row">
            <div class="v2-form-group">
                <label class="v2-form-label" for="country">Your Location Country</label>
                <select class="v2-form-select" id="country" name="country" required>
                    <option value="">Please select...</option>
                    @foreach($locationHelper->countriesForSelect() as $code => $country)
                        <option value="{{ $code }}">{{ $country }}</option>
                    @endforeach
                </select>
            </div>
            <div class="v2-form-group">
                <label class="v2-form-label" for="state">Your Location State</label>
                <select class="v2-form-select" id="state" name="state">
                    <option value="">Please select...</option>
                    @foreach($locationHelper->subdivisionsForSelect('US') as $code => $state)
                        <option value="{{ $code }}">{{ $state }}</option>
                    @endforeach
                </select>
            </div>
        </div>
        <div class="v2-form-row">

            <div class="v2-form-group">
                <label class="v2-form-label" for="city">Your Location City</label>
                <input class="v2-form-input" type="text" id="city" name="city" placeholder="City">
            </div>
            <div class="v2-form-group">
                <label class="v2-form-label" for="postcode">Postcode</label>
                <input class="v2-form-input" type="text" id="postcode" name="postcode" placeholder="Postcode" required>
            </div>
        </div>
        <div class="v2-form-group">
            <label class="v2-form-label" for="description">Group Description</label>
            <textarea class="v2-form-textarea" id="description" name="description" placeholder="Is your group based on geographic proximity, a shared interest, or an existing relational connection or community? If so, be sure to include that in your description. What do you hope to accomplish, what is your purpose, who can join?" required></textarea>
        </div>
        <div class="v2-form-group">
            <label class="v2-form-label" for="covenant">Member Covenant <span class="v2-form-hint">See tooltip for help.</span></label>
            <textarea class="v2-form-textarea" id="covenant" name="covenant" placeholder="Member Covenant." required></textarea>
        </div>
        <div class="v2-form-row">
            <div class="v2-form-group">
                <label class="v2-form-label" for="type">Privacy Settings</label>
                <select class="v2-form-select" id="type" name="type" required>
                    <option value="">Please select...</option>
                    <option value="public">Public Group</option>
                    <option value="private">Private Group</option>
                    <option value="secret">Secret Group</option>
                </select>
                <div class="v2-form-hint">Set the privacy level of your group now. <b>Public</b> Group can be joined without approval; <b>Private</b> needs contact or invite; <b>Secret</b> is by invitation only.</div>
            </div>
            <div class="v2-form-group">
                <label class="v2-form-label" for="inviter">Who Can Invite New Users</label>
                <select class="v2-form-select" id="inviter" name="inviter" required>
                    <option value="">Please select...</option>
                    <option value="owner">Owner Only</option>
                    <option value="admin">Admins</option>
                    <option value="member">All Members</option>
                </select>
            </div>
        </div>
        <div class="v2-form-row">
            <div class="v2-form-group">
                <label class="v2-form-label" for="min_donation">Minimum Donation</label>
                <input class="v2-form-input" type="number" id="min_donation" name="min_donation" placeholder="$25" min="0">
            </div>
            <div class="v2-form-group">
                <label class="v2-form-label" for="donation_frequency">Donation Frequency</label>
                <select class="v2-form-select" id="donation_frequency" name="donation_frequency" required>
                    <option value="">Please select...</option>
                    <option value="monthly">Monthly</option>
                    <option value="quarterly">Quarterly</option>
                    <option value="yearly">Yearly</option>
                </select>
            </div>
        </div>
        <button class="v2-create-btn" type="submit">Create Group</button>
    </form>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    var nameInput = document.getElementById('name');
    var urlInput = document.getElementById('url_display');
    var shortnameInput = document.getElementById('shortname');
    var baseUrl = window.location.origin + '/';
    if (nameInput && urlInput && shortnameInput) {
        nameInput.addEventListener('input', function() {
            // Only letters and numbers for shortname
            let shortname = nameInput.value
                .toLowerCase()
                .replace(/[^a-z0-9]/g, '');
            shortnameInput.value = shortname;
            // For display, show the full URL with hyphens for spaces/special chars
            let slug = nameInput.value
                .toLowerCase()
                .replace(/[^a-z0-9\s-]/g, '')
                .replace(/\s+/g, '-')
                .replace(/-+/g, '-')
                .replace(/^-+|-+$/g, '');
            urlInput.value = baseUrl + slug;
        });
    }
});
</script>
@endpush