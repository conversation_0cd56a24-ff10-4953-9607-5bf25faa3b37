@extends('layouts.dashboard-new')

@push('styles')
<style>
    .content-body .container {
        margin: 0 auto;
        padding: 20px;
        background-color: transparent !important;
    }
    .content-body .group-header {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        gap: 24px;
        margin-bottom: 32px;
    }
    .content-body .group-header .group-title {
        font-size: 2rem;
        font-weight: 700;
        color: #333;
    }
    .content-body .group-header .group-status {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 1rem;
        color: #6366f1;
        background: #e0e7ff;
        border-radius: 6px;
        padding: 4px 12px;
        font-weight: 500;
    }
    .content-body .group-header .leave-btn {
        margin-left: auto;
    }
    .content-body .group-image {
        width: 100%;
        height: 220px;
        background-size: cover;
        background-position: center;
        border-radius: 12px;
        margin-bottom: 32px;
        position: relative;
        cursor: pointer;
    }
    .content-body .group-image:hover::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .content-body .group-image:hover .change-banner-btn {
        display: flex;
    }
    .content-body .change-banner-btn {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: #5144A1;
        color: white;
        padding: 8px 16px;
        border-radius: 6px;
        font-weight: 500;
        z-index: 1;
        display: none;
        align-items: center;
        gap: 8px;
        border: none;
        cursor: pointer;
    }
    .content-body .change-banner-btn:hover {
        background: #4338a3;
    }
    .content-body .banner-modal {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        z-index: 1000;
        align-items: center;
        justify-content: center;
    }
    .content-body .banner-modal.active {
        display: flex;
    }
    .content-body .banner-modal-content {
        background: white;
        padding: 24px;
        border-radius: 12px;
        width: 100%;
        max-width: 500px;
        position: relative;
    }
    .content-body .banner-modal-close {
        position: absolute;
        top: 16px;
        right: 16px;
        background: none;
        border: none;
        font-size: 24px;
        cursor: pointer;
        color: #666;
    }
    .content-body .banner-modal-title {
        font-size: 1.25rem;
        font-weight: 600;
        margin-bottom: 16px;
        color: #333;
    }
    .content-body .banner-modal-guidelines {
        background: #f3f4f6;
        padding: 12px;
        border-radius: 6px;
        margin-bottom: 16px;
        font-size: 0.875rem;
        color: #666;
    }
    .content-body .banner-upload-area {
        border: 2px dashed #ddd;
        border-radius: 8px;
        padding: 24px;
        text-align: center;
        margin-bottom: 16px;
        cursor: pointer;
    }
    .content-body .banner-upload-area:hover {
        border-color: #5144A1;
    }
    .content-body .banner-upload-area input[type="file"] {
        display: none;
    }
    .content-body .banner-upload-area-text {
        color: #666;
        margin-bottom: 8px;
    }
    .content-body .banner-upload-area-subtext {
        font-size: 0.875rem;
        color: #999;
    }
    .content-body .banner-modal-actions {
        display: flex;
        justify-content: flex-end;
        gap: 12px;
    }
    .content-body .banner-modal-actions button {
        padding: 8px 16px;
        border-radius: 6px;
        font-weight: 500;
        cursor: pointer;
    }
    .content-body .banner-modal-actions .cancel-btn {
        background: #f3f4f6;
        border: 1px solid #ddd;
        color: #666;
    }
    .content-body .banner-modal-actions .save-btn {
        background: #5144A1;
        border: none;
        color: white;
    }
    .content-body .banner-modal-actions .save-btn:hover {
        background: #4338a3;
    }
    .content-body .banner-modal-actions .save-btn:disabled {
        background: #a5a5a5;
        cursor: not-allowed;
    }
    .content-body .banner-preview {
        max-width: 100%;
        max-height: 200px;
        margin: 16px 0;
        display: none;
        border-radius: 6px;
    }
    .content-body .group-stats-row {
        display: flex;
        gap: 10px;
        margin-bottom: 32px;
        justify-content: center;
        flex-wrap: wrap;
    }
    .content-body .stat-card {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        padding: 20px;
        gap: 10px;
        width: 378px;
        height: 130px;
        background: linear-gradient(0deg, #F7F8FD, #F7F8FD), #FFFFFF;
        box-shadow: 0px 2px 5px rgba(38, 51, 77, 0.1);
        border-radius: 6px;
        flex: none;
        align-self: stretch;
        flex-grow: 0;
    }
    .content-body .stat-card.available {
        background: linear-gradient(0deg, rgba(154, 203, 72, 0.07), rgba(154, 203, 72, 0.07)), #FFFFFF;
    }
    .content-body .stat-card.pending {
        width: 328px;
        background: linear-gradient(0deg, rgba(131, 71, 204, 0.07), rgba(131, 71, 204, 0.07)), #FFFFFF;
    }
    .content-body .stat-label {
        font-size: 14px;
        color: #666;
        margin-bottom: 8px;
    }
    .content-body .stat-value {
        font-size: 1.5rem;
        font-weight: 700;
        color: #333;
    }
    .content-body .group-tabs {
        display: flex;
        gap: 16px;
        margin-bottom: 24px;
        border-bottom: none;
        justify-content: flex-start;
        margin-top: 40px;
    }
    .content-body .group-tab {
        display: flex;
        align-items: center;
        justify-content: center;
        min-width: 147px;
        height: 38px;
        padding: 0 24px;
        border-radius: 8px;
        background: #fff;
        color: #5144A1;
        font-weight: 600;
        font-size: 1rem;
        border: 1.5px solid #5144A1;
        cursor: pointer;
        transition: background 0.2s, color 0.2s;
        box-shadow: 0px 2px 5px rgba(38, 51, 77, 0.05);
        margin-bottom: 0;
        position: relative;
    }
    .content-body .group-tab.active {
        background: #5144A1;
        color: #fff;
        border: 1.5px solid #5144A1;
        box-shadow: 0px 2px 5px rgba(38, 51, 77, 0.10);
    }
    .content-body .group-details-grid {
        display: grid;
        grid-template-columns: 2fr 1fr;
        gap: 32px;
        margin-bottom: 0;
    }
    .content-body .group-overview, .content-body .group-members, .content-body .group-covenant {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        padding: 20px;
        gap: 10px;
        width: 100%;
        height: auto;
        min-height: auto;
        background: linear-gradient(0deg, #FFFFFF, #FFFFFF), #FFFFFF;
        border: 2px solid #F5F6F7;
        box-shadow: 0px 2px 5px rgba(38, 51, 77, 0.03);
        border-radius: 5px;
        flex: none;
        order: 0;
        flex-grow: 0;
        z-index: 0;
        margin-bottom: 32px;
    }
    .content-body .group-overview h3, .content-body .group-covenant h3 {
        font-family: 'Inter', sans-serif;
        font-weight: 600;
        font-size: 20px;
        line-height: 30px;
        color: #4D5E80;
        margin-bottom: 16px;
    }
    .content-body .group-overview p, .content-body .group-covenant p {
        font-family: 'Inter', sans-serif;
        font-weight: 400;
        font-size: 16px;
        line-height: 24px;
        letter-spacing: 0px;
        color: #4D5E80;
        margin-bottom: 8px;
    }
    .content-body .group-members h3 {
        font-size: 1.2rem;
        font-weight: 600;
        margin-bottom: 16px;
    }
    .content-body .members-list {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 24px;
        width: 100%;
    }
    .content-body .member-avatar {
        width: 100%;
        aspect-ratio: 1;
        border-radius: 12px;
        background-color: #e5e7eb;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 36px;
        font-weight: 600;
        color: #666;
        position: relative;
        flex-direction: column;
        margin-bottom: 8px;
        box-shadow: 0px 2px 5px rgba(38, 51, 77, 0.08);
        overflow: hidden;
    }
    .content-body .member-avatar .admin-badge {
        position: absolute;
        bottom: 8px;
        left: 50%;
        transform: translateX(-50%);
        background: #6366f1;
        color: white;
        font-size: 12px;
        padding: 2px 8px;
        border-radius: 3px;
        margin-top: 2px;
    }
    .content-body .member-name {
        font-size: 15px;
        color: #333;
        text-align: center;
        margin-top: 4px;
        max-width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
    .content-body .group-covenant h3 {
        font-size: 1.2rem;
        font-weight: 600;
        margin-bottom: 16px;
    }
    .content-body .group-covenant p {
        font-family: 'Inter', sans-serif;
        font-weight: 400;
        font-size: 16px;
        line-height: 24px;
        letter-spacing: 0px;
        color: #4D5E80;
        margin-bottom: 8px;
    }
    @media (max-width: 1200px) {
        .content-body .group-stats-row {
            flex-direction: column;
            gap: 16px;
        }
        .content-body .stat-card, .content-body .stat-card.available, .content-body .stat-card.pending {
            width: 100%;
            min-width: 0;
            max-width: 100%;
        }
        .content-body .members-list {
            grid-template-columns: repeat(2, 1fr);
        }
    }
    @media (max-width: 768px) {
        .content-body .group-details-grid {
            grid-template-columns: 1fr;
        }
        .content-body .members-list {
            grid-template-columns: repeat(2, 1fr);
            gap: 16px;
        }
        .content-body .member-avatar {
            font-size: 24px;
        }
    }
    @media (max-width: 480px) {
        .content-body .members-list {
            grid-template-columns: 1fr;
            gap: 12px;
        }
    }
</style>
@endpush

@section('content')
<div class="container">
    <div class="group-header">
        <div class="group-title">{{ $group->name }}</div>
        <div class="group-status">
            <span>Status: {{ ucfirst($group->type) }}</span>
            @if($group->type == 'public')
                <span class="badge bg-success">Active</span>
            @elseif($group->type == 'private')
                <span class="badge bg-warning">Private</span>
            @else
                <span class="badge bg-secondary">Secret</span>
            @endif
        </div>
        @if(!$public && !$is_nonmember && $group->owner()->id != Auth::User()->id)
            <a href="{{ URL::route('group.leave',array('id'=>$group->id)) }}" class="btn btn-outline leave-btn">Leave Group</a>
        @endif
    </div>
    <div class="group-image" style="background-image: url('{{ $group->group_banner_image ? asset('storage/' . $group->group_banner_image) : asset('images/group-placeholder.jpg') }}');">
        @if($group->canEdit(Auth::user()))
        <button class="change-banner-btn">
            <i class="fas fa-camera"></i>
            Change Banner
        </button>
        @endif
    </div>
    <div class="group-stats-row">
        <div class="stat-card">
            <div class="stat-label">Total Shared</div>
            <div class="stat-value">${{ number_format($group->shared_funds ?? 0, 2) }}</div>
        </div>
        <div class="stat-card available">
            <div class="stat-label">Available</div>
            <div class="stat-value">${{ number_format($group->current_funds ?? 0, 2) }}</div>
        </div>
        <div class="stat-card pending">
            <div class="stat-label">Pending Requests</div>
            <div class="stat-value">0</div>
        </div>
    </div>
    <div class="group-tabs">
        <div class="group-tab active">Group Activity</div>
        <a href="{{ route('group.issues', $group->id) }}" class="group-tab" style="text-decoration: none;">View Requests</a>
        <div class="group-tab">Donate</div>
        <div class="group-tab">Transaction History</div>
    </div>
    <div class="group-details-grid">
        <div class="group-overview">
            <h3>Overview</h3>
            <p><strong>Location:</strong> {{ $group->city }}, {{ $group->state != 'zzzz' ? $group->state . ',' : '' }} {{ $group->country }}</p>
            <p><strong>Members:</strong> {{ $group->members->count() }}</p>
            <p><strong>Date Started:</strong> {{ $group->created_at->format('M j, Y') }}</p>
            <p><strong>Status:</strong> {{ ucfirst($group->type) }} Group (anyone can join)</p>
            <p><strong>Donation Requirements to Join:</strong> @if($group->min_donation) ${{ $group->min_donation }} on a {{ $group->donation_frequency }} basis. @else No requirement @endif</p>
            <p>{{ $group->description ?: 'No description provided.' }}</p>
            <div class="mt-auto">
                @if($group->canEdit(Auth::user()))
                <a href="{{ route('group.edit', $group->id) }}" class="btn btn-primary">Update</a>
                @endif
                <button class="btn btn-outline">Cancel</button>
            </div>
        </div>
        <div class="group-members">
            <h3>Members</h3>
            <div class="members-list">
                @foreach($group->members->take(8) as $member)
                    <div class="member-avatar">
                        {{ strtoupper(substr($member->firstname, 0, 1) . substr($member->lastname, 0, 1)) }}
                        @if(isset($member->pivot) && $member->pivot->role == 'owner')
                            <span class="admin-badge">Admin</span>
                        @endif
                        <div class="member-name">{{ $member->firstname }}</div>
                    </div>
                @endforeach
                @if($group->members->count() > 8)
                    <div class="member-avatar">+{{ $group->members->count() - 8 }}</div>
                @endif
            </div>
        </div>
    </div>
    <div class="group-covenant">
        <h3>Member Covenant</h3>
        @if($group->covenant)
            @foreach(preg_split('/\r?\n/', $group->covenant) as $line)
                <p>{{ $line }}</p>
            @endforeach
        @else
            <p>As members of the Vital Connections Network, we commit to the following principles, which guide our actions and intentions as we work to strengthen the conditions for collective well-being and resilience:</p>
            <p><strong>Foster Connection:</strong> We believe in the power of relationships. We commit to building trust, deepening understanding, and valuing diverse perspectives as we connect with one another in meaningful ways.</p>
            <p><strong>Promote Equity and Justice:</strong> We recognize that true community thrives when everyone has the opportunity to flourish. We pledge to challenge inequities, support inclusion, and advocate for systemic change that fosters fairness and justice for all.</p>
            <p><strong>Embrace Collaboration:</strong> We are stronger together. We commit to sharing resources, knowledge, and expertise openly, and to supporting each other in pursuit of our shared goals. Together, we can achieve more than any one of us can alone.</p>
            <p><strong>Sustainability and Stewardship:</strong> We honor our responsibility to care for the environment and one another. We will make decisions that prioritize sustainability, conscious of the impact on future generations and the health of our planet.</p>
            <p><strong>Commitment to Growth and Learning:</strong> We acknowledge that personal and collective growth is an ongoing process. We commit to continual learning, remaining open to new ideas, and adapting as we gain new insights.</p>
            <p><strong>Act with Integrity and Accountability:</strong> We hold ourselves and each other to the highest standards of honesty and transparency. We pledge to take responsibility for our actions and to fulfill our commitments to the best of our abilities.</p>
            <p><strong>Cultivate Compassion and Empathy:</strong> We recognize the inherent dignity of every person and commit to engaging with kindness, empathy, and respect. We seek to understand and support each other in times of need, celebrating our shared humanity.</p>
            <p>In joining the Vital Connections Network, we affirm our dedication to these values as we work together to build a thriving, just, and resilient world.</p>
        @endif
    </div>
</div>

<div class="banner-modal" id="bannerModal">
    <div class="banner-modal-content">
        <button class="banner-modal-close" onclick="closeBannerModal()">&times;</button>
        <h3 class="banner-modal-title">Change Group Banner</h3>
        <div class="banner-modal-guidelines">
            <p>For the best look, upload an image with dimensions 580x150 pixels.</p>
        </div>
        <form id="bannerUploadForm" action="{{ route('group.update-banner', $group->id) }}" method="POST" enctype="multipart/form-data">
            @csrf
            @method('PUT')
            <div class="banner-upload-area" onclick="document.getElementById('bannerFile').click()">
                <input type="file" id="bannerFile" name="banner_image" accept="image/*" onchange="previewBanner(this)">
                <div class="banner-upload-area-text">Click to upload or drag and drop</div>
                <div class="banner-upload-area-subtext">PNG, JPG up to 5MB</div>
            </div>
            <img id="bannerPreview" class="banner-preview" src="" alt="Banner preview">
            <div class="banner-modal-actions">
                <button type="button" class="cancel-btn" onclick="closeBannerModal()">Cancel</button>
                <button type="submit" class="save-btn" id="saveBannerBtn" disabled>Save Changes</button>
            </div>
        </form>
    </div>
</div>
@endsection

@push('scripts')
<script>
function openBannerModal() {
    document.getElementById('bannerModal').classList.add('active');
}

function closeBannerModal() {
    document.getElementById('bannerModal').classList.remove('active');
    document.getElementById('bannerFile').value = '';
    document.getElementById('bannerPreview').style.display = 'none';
    document.getElementById('saveBannerBtn').disabled = true;
}

function previewBanner(input) {
    const preview = document.getElementById('bannerPreview');
    const saveBtn = document.getElementById('saveBannerBtn');

    if (input.files && input.files[0]) {
        const reader = new FileReader();

        reader.onload = function(e) {
            preview.src = e.target.result;
            preview.style.display = 'block';
            saveBtn.disabled = false;
        }

        reader.readAsDataURL(input.files[0]);
    }
}

document.querySelector('.change-banner-btn').addEventListener('click', openBannerModal);

document.getElementById('bannerUploadForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);

    fetch(this.action, {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            window.location.reload();
        } else {
            alert('Error uploading banner image. Please try again.');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error uploading banner image. Please try again.');
    });
});
</script>
@endpush