@extends('layouts.dashboard-new')

@push('styles')
<link rel="stylesheet" href="{{ asset('css/v2-accounts.css') }}">
@endpush

@section('title', 'Contact Info')

@section('content')
<div class="v2-account-container">
    <div class="v2-account-header">
        <h1 class="v2-account-title">Contact Info</h1>
    </div>

    <div class="v2-account-content">

            @if(session('success'))
                <div class="v2-alert v2-alert-success">
                    {{ session('success') }}
                </div>
            @endif

            @if($errors->any())
                <div class="v2-alert v2-alert-error">
                    <ul style="margin: 0; padding-left: 20px;">
                        @foreach($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
            @endif

            <form method="POST" action="{{ route('v2.account.contact.update') }}">
                @csrf

                <!-- First Name -->
                <div class="v2-form-group">
                    <label for="firstname" class="v2-form-label">First Name</label>
                    <input type="text"
                           id="firstname"
                           name="firstname"
                           class="v2-form-input"
                           value="{{ old('firstname', $user->firstname) }}"
                           required>
                    @error('firstname')
                        <div class="v2-error-message">{{ $message }}</div>
                    @enderror
                </div>

                <!-- Last Name -->
                <div class="v2-form-group">
                    <label for="lastname" class="v2-form-label">Last Name</label>
                    <input type="text"
                           id="lastname"
                           name="lastname"
                           class="v2-form-input"
                           value="{{ old('lastname', $user->lastname) }}"
                           required>
                    @error('lastname')
                        <div class="v2-error-message">{{ $message }}</div>
                    @enderror
                </div>

                <!-- Address -->
                <div class="v2-form-group">
                    <label for="addr1" class="v2-form-label">Address</label>
                    <input type="text"
                           id="addr1"
                           name="address[addr1]"
                           class="v2-form-input"
                           value="{{ old('address.addr1', $user->address->addr1 ?? '') }}"
                           placeholder="20815 Brinkley Street">
                    @error('address.addr1')
                        <div class="v2-error-message">{{ $message }}</div>
                    @enderror
                </div>

                <!-- Address Line 2 -->
                <div class="v2-form-group">
                    <input type="text"
                           id="addr2"
                           name="address[addr2]"
                           class="v2-form-input"
                           value="{{ old('address.addr2', $user->address->addr2 ?? '') }}"
                           placeholder="Apartment, suite, etc. (optional)">
                    @error('address.addr2')
                        <div class="v2-error-message">{{ $message }}</div>
                    @enderror
                </div>

                <!-- City -->
                <div class="v2-form-group">
                    <label for="city" class="v2-form-label">City</label>
                    <input type="text"
                           id="city"
                           name="address[city]"
                           class="v2-form-input"
                           value="{{ old('address.city', $user->address->city ?? '') }}"
                           placeholder="Cornelius">
                    @error('address.city')
                        <div class="v2-error-message">{{ $message }}</div>
                    @enderror
                </div>

                <!-- State -->
                <div class="v2-form-group">
                    <label for="state" class="v2-form-label">State</label>
                    <select id="state" name="address[state]" class="v2-form-select">
                        <option value="">Select State</option>
                        <option value="AL" {{ old('address.state', $user->address->state ?? '') == 'AL' ? 'selected' : '' }}>Alabama</option>
                        <option value="AK" {{ old('address.state', $user->address->state ?? '') == 'AK' ? 'selected' : '' }}>Alaska</option>
                        <option value="AZ" {{ old('address.state', $user->address->state ?? '') == 'AZ' ? 'selected' : '' }}>Arizona</option>
                        <option value="AR" {{ old('address.state', $user->address->state ?? '') == 'AR' ? 'selected' : '' }}>Arkansas</option>
                        <option value="CA" {{ old('address.state', $user->address->state ?? '') == 'CA' ? 'selected' : '' }}>California</option>
                        <option value="CO" {{ old('address.state', $user->address->state ?? '') == 'CO' ? 'selected' : '' }}>Colorado</option>
                        <option value="CT" {{ old('address.state', $user->address->state ?? '') == 'CT' ? 'selected' : '' }}>Connecticut</option>
                        <option value="DE" {{ old('address.state', $user->address->state ?? '') == 'DE' ? 'selected' : '' }}>Delaware</option>
                        <option value="FL" {{ old('address.state', $user->address->state ?? '') == 'FL' ? 'selected' : '' }}>Florida</option>
                        <option value="GA" {{ old('address.state', $user->address->state ?? '') == 'GA' ? 'selected' : '' }}>Georgia</option>
                        <option value="HI" {{ old('address.state', $user->address->state ?? '') == 'HI' ? 'selected' : '' }}>Hawaii</option>
                        <option value="ID" {{ old('address.state', $user->address->state ?? '') == 'ID' ? 'selected' : '' }}>Idaho</option>
                        <option value="IL" {{ old('address.state', $user->address->state ?? '') == 'IL' ? 'selected' : '' }}>Illinois</option>
                        <option value="IN" {{ old('address.state', $user->address->state ?? '') == 'IN' ? 'selected' : '' }}>Indiana</option>
                        <option value="IA" {{ old('address.state', $user->address->state ?? '') == 'IA' ? 'selected' : '' }}>Iowa</option>
                        <option value="KS" {{ old('address.state', $user->address->state ?? '') == 'KS' ? 'selected' : '' }}>Kansas</option>
                        <option value="KY" {{ old('address.state', $user->address->state ?? '') == 'KY' ? 'selected' : '' }}>Kentucky</option>
                        <option value="LA" {{ old('address.state', $user->address->state ?? '') == 'LA' ? 'selected' : '' }}>Louisiana</option>
                        <option value="ME" {{ old('address.state', $user->address->state ?? '') == 'ME' ? 'selected' : '' }}>Maine</option>
                        <option value="MD" {{ old('address.state', $user->address->state ?? '') == 'MD' ? 'selected' : '' }}>Maryland</option>
                        <option value="MA" {{ old('address.state', $user->address->state ?? '') == 'MA' ? 'selected' : '' }}>Massachusetts</option>
                        <option value="MI" {{ old('address.state', $user->address->state ?? '') == 'MI' ? 'selected' : '' }}>Michigan</option>
                        <option value="MN" {{ old('address.state', $user->address->state ?? '') == 'MN' ? 'selected' : '' }}>Minnesota</option>
                        <option value="MS" {{ old('address.state', $user->address->state ?? '') == 'MS' ? 'selected' : '' }}>Mississippi</option>
                        <option value="MO" {{ old('address.state', $user->address->state ?? '') == 'MO' ? 'selected' : '' }}>Missouri</option>
                        <option value="MT" {{ old('address.state', $user->address->state ?? '') == 'MT' ? 'selected' : '' }}>Montana</option>
                        <option value="NE" {{ old('address.state', $user->address->state ?? '') == 'NE' ? 'selected' : '' }}>Nebraska</option>
                        <option value="NV" {{ old('address.state', $user->address->state ?? '') == 'NV' ? 'selected' : '' }}>Nevada</option>
                        <option value="NH" {{ old('address.state', $user->address->state ?? '') == 'NH' ? 'selected' : '' }}>New Hampshire</option>
                        <option value="NJ" {{ old('address.state', $user->address->state ?? '') == 'NJ' ? 'selected' : '' }}>New Jersey</option>
                        <option value="NM" {{ old('address.state', $user->address->state ?? '') == 'NM' ? 'selected' : '' }}>New Mexico</option>
                        <option value="NY" {{ old('address.state', $user->address->state ?? '') == 'NY' ? 'selected' : '' }}>New York</option>
                        <option value="NC" {{ old('address.state', $user->address->state ?? '') == 'NC' ? 'selected' : '' }}>North Carolina</option>
                        <option value="ND" {{ old('address.state', $user->address->state ?? '') == 'ND' ? 'selected' : '' }}>North Dakota</option>
                        <option value="OH" {{ old('address.state', $user->address->state ?? '') == 'OH' ? 'selected' : '' }}>Ohio</option>
                        <option value="OK" {{ old('address.state', $user->address->state ?? '') == 'OK' ? 'selected' : '' }}>Oklahoma</option>
                        <option value="OR" {{ old('address.state', $user->address->state ?? '') == 'OR' ? 'selected' : '' }}>Oregon</option>
                        <option value="PA" {{ old('address.state', $user->address->state ?? '') == 'PA' ? 'selected' : '' }}>Pennsylvania</option>
                        <option value="RI" {{ old('address.state', $user->address->state ?? '') == 'RI' ? 'selected' : '' }}>Rhode Island</option>
                        <option value="SC" {{ old('address.state', $user->address->state ?? '') == 'SC' ? 'selected' : '' }}>South Carolina</option>
                        <option value="SD" {{ old('address.state', $user->address->state ?? '') == 'SD' ? 'selected' : '' }}>South Dakota</option>
                        <option value="TN" {{ old('address.state', $user->address->state ?? '') == 'TN' ? 'selected' : '' }}>Tennessee</option>
                        <option value="TX" {{ old('address.state', $user->address->state ?? '') == 'TX' ? 'selected' : '' }}>Texas</option>
                        <option value="UT" {{ old('address.state', $user->address->state ?? '') == 'UT' ? 'selected' : '' }}>Utah</option>
                        <option value="VT" {{ old('address.state', $user->address->state ?? '') == 'VT' ? 'selected' : '' }}>Vermont</option>
                        <option value="VA" {{ old('address.state', $user->address->state ?? '') == 'VA' ? 'selected' : '' }}>Virginia</option>
                        <option value="WA" {{ old('address.state', $user->address->state ?? '') == 'WA' ? 'selected' : '' }}>Washington</option>
                        <option value="WV" {{ old('address.state', $user->address->state ?? '') == 'WV' ? 'selected' : '' }}>West Virginia</option>
                        <option value="WI" {{ old('address.state', $user->address->state ?? '') == 'WI' ? 'selected' : '' }}>Wisconsin</option>
                        <option value="WY" {{ old('address.state', $user->address->state ?? '') == 'WY' ? 'selected' : '' }}>Wyoming</option>
                    </select>
                    @error('address.state')
                        <div class="v2-error-message">{{ $message }}</div>
                    @enderror
                </div>

                <!-- Postal Code -->
                <div class="v2-form-group">
                    <label for="postcode" class="v2-form-label">Postal Code</label>
                    <input type="text"
                           id="postcode"
                           name="address[postcode]"
                           class="v2-form-input"
                           value="{{ old('address.postcode', $user->address->postcode ?? '') }}"
                           placeholder="28031">
                    @error('address.postcode')
                        <div class="v2-error-message">{{ $message }}</div>
                    @enderror
                </div>

                <!-- Country -->
                <div class="v2-form-group">
                    <label for="country" class="v2-form-label">Country</label>
                    <select id="country" name="address[country]" class="v2-form-select">
                        <option value="">Select Country</option>
                        <option value="United States" {{ old('address.country', $user->address->country ?? '') == 'United States' ? 'selected' : '' }}>United States</option>
                        <option value="Canada" {{ old('address.country', $user->address->country ?? '') == 'Canada' ? 'selected' : '' }}>Canada</option>
                        <option value="United Kingdom" {{ old('address.country', $user->address->country ?? '') == 'United Kingdom' ? 'selected' : '' }}>United Kingdom</option>
                        <option value="Australia" {{ old('address.country', $user->address->country ?? '') == 'Australia' ? 'selected' : '' }}>Australia</option>
                        <option value="South Africa" {{ old('address.country', $user->address->country ?? '') == 'South Africa' ? 'selected' : '' }}>South Africa</option>
                    </select>
                    @error('address.country')
                        <div class="v2-error-message">{{ $message }}</div>
                    @enderror
                </div>

                <!-- Save Button -->
                <div class="v2-form-group">
                    <button type="submit" class="v2-btn v2-btn-primary">
                        Save Changes
                    </button>
                </div>
            </form>
    </div>
</div>
@endsection
