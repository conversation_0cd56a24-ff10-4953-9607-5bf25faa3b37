@extends('layouts.dashboard-new')

@push('styles')
<link rel="stylesheet" href="{{ asset('css/v2-accounts.css') }}">
@endpush

@section('title', 'Notification Preferences')

@section('content')
<div class="v2-account-container">
    <div class="v2-account-header">
        <h1 class="v2-account-title">Notification Preferences</h1>
    </div>

    <div class="v2-account-content">

            @if(session('success'))
                <div class="v2-alert v2-alert-success">
                    {{ session('success') }}
                </div>
            @endif

            @if($errors->any())
                <div class="v2-alert v2-alert-error">
                    <ul style="margin: 0; padding-left: 20px;">
                        @foreach($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
            @endif

            <form method="POST" action="{{ route('v2.account.notifications.update') }}">
                @csrf

                <!-- Share Email with Common Change Users -->
                <div class="v2-form-group">
                    <label class="v2-form-label">Share Email with Common Change Users</label>
                    <div class="v2-checkbox-group">
                        <input type="checkbox"
                               id="share_email"
                               name="share_email"
                               class="v2-checkbox"
                               value="1"
                               {{ old('share_email', $user->share_email) ? 'checked' : '' }}>
                        <label for="share_email" class="v2-checkbox-label">
                            <i class="fas fa-check" style="color: #10B981; font-size: 18px;"></i>
                        </label>
                    </div>
                    @error('share_email')
                        <div class="v2-error-message">{{ $message }}</div>
                    @enderror
                </div>

                <!-- Notification Preference -->
                <div class="v2-form-group">
                    <label for="default_email_notification" class="v2-form-label">Notification Preference</label>
                    <select id="default_email_notification"
                            name="default_email_notification"
                            class="v2-form-select"
                            required>
                        <option value="">Please select one of the following:</option>
                        <option value="daily_digest"
                                {{ old('default_email_notification', $user->default_email_notification) == 'daily_digest' ? 'selected' : '' }}>
                            Send a Daily Digest of all my groups
                        </option>
                        <option value="daily_per_discussion"
                                {{ old('default_email_notification', $user->default_email_notification) == 'daily_per_discussion' ? 'selected' : '' }}>
                            One email per day per Discussion
                        </option>
                        <option value="per_comment"
                                {{ old('default_email_notification', $user->default_email_notification) == 'per_comment' ? 'selected' : '' }}>
                            One email per comment
                        </option>
                        <option value="none"
                                {{ old('default_email_notification', $user->default_email_notification) == 'none' ? 'selected' : '' }}>
                            Don't send email updates
                        </option>
                    </select>
                    @error('default_email_notification')
                        <div class="v2-error-message">{{ $message }}</div>
                    @enderror
                </div>

                <!-- Save Button -->
                <div class="v2-form-group">
                    <button type="submit" class="v2-btn v2-btn-primary">
                        Save Changes
                    </button>
                </div>
            </form>
    </div>
</div>

<style>
/* Custom checkbox styling to match the design */
.v2-checkbox-group {
    align-items: flex-start;
}

.v2-checkbox {
    display: none;
}

.v2-checkbox + .v2-checkbox-label {
    position: relative;
    padding-left: 35px;
    cursor: pointer;
    display: inline-block;
    line-height: 20px;
}

.v2-checkbox + .v2-checkbox-label:before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 20px;
    height: 20px;
    border: 2px solid #E5E7EB;
    border-radius: 4px;
    background: white;
}

.v2-checkbox:checked + .v2-checkbox-label:before {
    background: #10B981;
    border-color: #10B981;
}

.v2-checkbox:checked + .v2-checkbox-label:after {
    content: '✓';
    position: absolute;
    left: 4px;
    top: 1px;
    color: white;
    font-size: 14px;
    font-weight: bold;
}

.v2-checkbox + .v2-checkbox-label .fas {
    display: none;
}

.v2-checkbox:checked + .v2-checkbox-label .fas {
    display: inline;
    position: absolute;
    left: 2px;
    top: 1px;
}
</style>
@endsection
