@extends('layouts.dashboard-new')

@push('styles')
<link rel="stylesheet" href="{{ asset('css/v2-accounts.css') }}">
@endpush

@section('title', 'Account Security')

@section('content')
<div class="v2-account-container">
    <div class="v2-account-header">
        <h1 class="v2-account-title">Account Security</h1>
    </div>

    <div class="v2-account-content">

            @if(session('success'))
                <div class="v2-alert v2-alert-success">
                    {{ session('success') }}
                </div>
            @endif

            @if($errors->any())
                <div class="v2-alert v2-alert-error">
                    <ul style="margin: 0; padding-left: 20px;">
                        @foreach($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
            @endif

            <form method="POST" action="{{ route('v2.account.security.update') }}" enctype="multipart/form-data">
                @csrf

                <!-- Email Section -->
                <div class="v2-form-group">
                    <label for="email" class="v2-form-label">Email</label>
                    <input type="email"
                           id="email"
                           name="email"
                           class="v2-form-input"
                           value="{{ old('email', $user->email) }}"
                           required>
                    @error('email')
                        <div class="v2-error-message">{{ $message }}</div>
                    @enderror
                </div>

                <!-- Reset Password Section -->
                <div class="v2-form-group">
                    <label class="v2-form-label">Reset Password</label>

                    <div style="margin-bottom: 15px;">
                        <label for="new_password" class="v2-form-label" style="font-weight: 500; margin-bottom: 5px;">New Password</label>
                        <input type="password"
                               id="new_password"
                               name="new_password"
                               class="v2-form-input"
                               placeholder="Enter new password">
                        @error('new_password')
                            <div class="v2-error-message">{{ $message }}</div>
                        @enderror
                    </div>

                    <div>
                        <label for="new_password_confirmation" class="v2-form-label" style="font-weight: 500; margin-bottom: 5px;">Confirm Password</label>
                        <input type="password"
                               id="new_password_confirmation"
                               name="new_password_confirmation"
                               class="v2-form-input"
                               placeholder="Confirm new password">
                    </div>
                </div>

                <!-- Profile Picture Section -->
                <div class="v2-form-group">
                    <label class="v2-form-label">Profile Picture</label>

                    <div class="v2-profile-pic-section">
                        @if($user->profile_pic && $user->profile_pic !== '/pix/profile-placeholder.png')
                            <img src="{{ $user->profile_pic }}" alt="Profile Picture" class="v2-profile-pic-current">
                        @else
                            <div class="v2-profile-pic-placeholder">
                                <i class="fas fa-user"></i>
                            </div>
                        @endif

                        <div>
                            <input type="file"
                                   id="profile_pic"
                                   name="profile_pic"
                                   accept="image/*"
                                   style="display: none;">
                            <button type="button"
                                    class="v2-btn v2-btn-secondary"
                                    onclick="document.getElementById('profile_pic').click();">
                                Change Image
                            </button>
                            <div style="font-size: 12px; color: #6B7280; margin-top: 5px;">
                                JPG, PNG, GIF up to 2MB
                            </div>
                        </div>
                    </div>
                    @error('profile_pic')
                        <div class="v2-error-message">{{ $message }}</div>
                    @enderror
                </div>

                <!-- Save Button -->
                <div class="v2-form-group">
                    <button type="submit" class="v2-btn v2-btn-primary">
                        Save Password
                    </button>
                </div>
            </form>
    </div>
</div>

<script>
document.getElementById('profile_pic').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            const img = document.querySelector('.v2-profile-pic-current') || document.querySelector('.v2-profile-pic-placeholder');
            if (img.tagName === 'IMG') {
                img.src = e.target.result;
            } else {
                // Replace placeholder with actual image
                const newImg = document.createElement('img');
                newImg.src = e.target.result;
                newImg.className = 'v2-profile-pic-current';
                newImg.alt = 'Profile Picture';
                img.parentNode.replaceChild(newImg, img);
            }
        };
        reader.readAsDataURL(file);
    }
});
</script>
@endsection
