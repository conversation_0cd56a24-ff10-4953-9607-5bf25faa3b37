@extends('layouts.dashboard-new')

@push('styles')
<link rel="stylesheet" href="{{ asset('css/v2-accounts.css') }}">
@endpush

@section('title', 'Social Media')

@section('content')
<div class="v2-account-container">
    <div class="v2-account-header">
        <h1 class="v2-account-title">Social Media</h1>
    </div>

    <div class="v2-account-content">

            @if(session('success'))
                <div class="v2-alert v2-alert-success">
                    {{ session('success') }}
                </div>
            @endif

            @if($errors->any())
                <div class="v2-alert v2-alert-error">
                    <ul style="margin: 0; padding-left: 20px;">
                        @foreach($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
            @endif

            <form method="POST" action="{{ route('v2.account.social.update') }}">
                @csrf

                <!-- Facebook URL -->
                <div class="v2-form-group">
                    <label for="facebook" class="v2-form-label">Facebook URL</label>
                    <input type="url"
                           id="facebook"
                           name="facebook"
                           class="v2-form-input"
                           value="{{ old('facebook', $user->facebook) }}"
                           placeholder="Facebook">
                    @error('facebook')
                        <div class="v2-error-message">{{ $message }}</div>
                    @enderror
                </div>

                <!-- LinkedIn URL -->
                <div class="v2-form-group">
                    <label for="twitter" class="v2-form-label">LinkedIn URL</label>
                    <input type="url"
                           id="twitter"
                           name="twitter"
                           class="v2-form-input"
                           value="{{ old('twitter', $user->twitter) }}"
                           placeholder="LinkedIn">
                    @error('twitter')
                        <div class="v2-error-message">{{ $message }}</div>
                    @enderror
                </div>

                <!-- Save Button -->
                <div class="v2-form-group">
                    <button type="submit" class="v2-btn v2-btn-primary">
                        Save Changes
                    </button>
                </div>
            </form>
    </div>
</div>
@endsection
