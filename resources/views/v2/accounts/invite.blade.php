@extends('layouts.dashboard-new')

@section('title', 'Invite Friends')

@push('styles')
<link rel="stylesheet" href="{{ asset('css/v2-accounts.css') }}">
<link rel="stylesheet" href="{{ asset('css/v2-invite.css') }}">
@endpush

@section('content')
<div class="v2-account-container">
    <div class="v2-account-header">
        <h1 class="v2-account-title">Invite Friends</h1>
    </div>

    <div class="v2-account-content">

        <!-- Display success/error messages -->
        @if(session('success'))
            <div class="v2-alert v2-alert-success">
                {{ session('success') }}
            </div>
        @endif

        @if($errors->any())
            <div class="v2-alert v2-alert-error">
                <ul class="v2-error-list">
                    @foreach($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
            </div>
        @endif

        <form id="invite-form" method="POST" action="{{ route('invite.send') }}" class="v2-invite-form">
            @csrf
            <input type="hidden" name="group_id" value="{{ Auth::user()->default_group_id }}">

            <!-- Invite Type Selection -->
            <div class="v2-invite-type-section">
                <div class="v2-radio-group">
                    <div class="v2-radio-option">
                        <input type="radio" id="invite_group" name="invite_to" value="group"
                               {{ old('invite_to', 'group') == 'group' ? 'checked' : '' }}
                               class="v2-radio-input">
                        <label for="invite_group" class="v2-radio-label">
                            <span class="v2-radio-circle"></span>
                            <span class="v2-radio-text">To join your group</span>
                        </label>
                        @if($defaultGroup)
                            <div class="v2-group-name">({{ $defaultGroup->name }})</div>
                        @endif
                    </div>

                    <div class="v2-radio-option">
                        <input type="radio" id="invite_site" name="invite_to" value="site"
                               {{ old('invite_to') == 'site' ? 'checked' : '' }}
                               class="v2-radio-input">
                        <label for="invite_site" class="v2-radio-label">
                            <span class="v2-radio-circle"></span>
                            <span class="v2-radio-text">To join Common Change</span>
                        </label>
                    </div>
                </div>
            </div>

            <!-- People to Invite -->
            <div class="v2-people-section">
                <div id="emails-container" class="v2-emails-container">
                    <!-- Email rows will be added here by JavaScript -->
                </div>

                <button type="button" id="add-more-btn" class="v2-add-more-btn">
                    + Add More People
                </button>
            </div>

            <!-- Message Section -->
            <div class="v2-message-section">
                <label class="v2-message-label">
                    Add a short introduction to let them know why you think they might be a good fit for, or interested in the group and Common Change:
                </label>
                <textarea name="message" class="v2-message-textarea" rows="5" placeholder="Your message here...">{{ old('message') }}</textarea>
            </div>

            <!-- Action Buttons -->
            <div class="v2-actions-section">
                <button type="submit" class="v2-btn v2-btn-primary">Send</button>
            </div>
        </form>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    let emailRowCount = 0;
    const emailsContainer = document.getElementById('emails-container');
    const addMoreBtn = document.getElementById('add-more-btn');

    // Function to add a new email row
    function addEmailRow(firstname = '', lastname = '', email = '') {
        const rowHtml = `
            <div class="v2-email-row" data-row="${emailRowCount}">
                <div class="v2-input-group">
                    <input type="text"
                           name="email[${emailRowCount}][firstname]"
                           value="${firstname}"
                           class="v2-input v2-input-firstname"
                           placeholder="First Name"
                           required>
                    <input type="text"
                           name="email[${emailRowCount}][lastname]"
                           value="${lastname}"
                           class="v2-input v2-input-lastname"
                           placeholder="Last Name"
                           required>
                    <input type="email"
                           name="email[${emailRowCount}][email]"
                           value="${email}"
                           class="v2-input v2-input-email"
                           placeholder="Email Address"
                           required>
                    <button type="button" class="v2-remove-btn" onclick="removeEmailRow(${emailRowCount})">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        `;

        emailsContainer.insertAdjacentHTML('beforeend', rowHtml);
        emailRowCount++;

        updateRemoveButtons();
    }

    // Function to remove an email row
    window.removeEmailRow = function(rowIndex) {
        const row = document.querySelector(`[data-row="${rowIndex}"]`);
        if (row) {
            row.remove();
            updateRemoveButtons();
        }
    };

    // Function to update remove button visibility
    function updateRemoveButtons() {
        const rows = emailsContainer.querySelectorAll('.v2-email-row');
        rows.forEach((row, index) => {
            const removeBtn = row.querySelector('.v2-remove-btn');
            // Show remove button only if there's more than one row
            removeBtn.style.display = rows.length > 1 ? 'flex' : 'none';
        });
    }

    // Add more people button click handler
    addMoreBtn.addEventListener('click', function() {
        addEmailRow();
    });

    // Initialize with old input or one empty row
    @if(!empty($old_input) && isset($old_input['email']))
        @foreach($old_input['email'] as $email)
            addEmailRow(
                {{ json_encode($email['firstname'] ?? '') }},
                {{ json_encode($email['lastname'] ?? '') }},
                {{ json_encode($email['email'] ?? '') }}
            );
        @endforeach
    @else
        addEmailRow();
    @endif

    // Form validation
    const form = document.getElementById('invite-form');
    form.addEventListener('submit', function(e) {
        const emailRows = emailsContainer.querySelectorAll('.v2-email-row');
        let hasValidEmail = false;

        emailRows.forEach(row => {
            const emailInput = row.querySelector('.v2-input-email');
            if (emailInput && emailInput.value.trim()) {
                hasValidEmail = true;
            }
        });

        if (!hasValidEmail) {
            e.preventDefault();
            alert('Please add at least one person to invite.');
        }
    });

    // Radio button styling update
    const radioInputs = document.querySelectorAll('.v2-radio-input');
    radioInputs.forEach(input => {
        input.addEventListener('change', function() {
            // Update visual state of all radio options
            radioInputs.forEach(radio => {
                const option = radio.closest('.v2-radio-option');
                if (radio.checked) {
                    option.classList.add('checked');
                } else {
                    option.classList.remove('checked');
                }
            });
        });

        // Set initial state
        if (input.checked) {
            input.closest('.v2-radio-option').classList.add('checked');
        }
    });
});
</script>
@endsection
