@extends('layouts.wizard')

@section('title')
	Introduce Yourself
@stop

@section('head')
<link type="text/css" rel="stylesheet" href="{{ asset('/css/dark.css?version=1') }}" media="all">
<link type="text/css" rel="stylesheet" href="{{ asset('/css/jquery-ui1.css?version=1') }}" media="all">
<link type="text/css" rel="stylesheet" href="{{ asset('/css/selectboxit.css?version=1') }}" media="all">
{{ App\Models\Wizard::setupClasses('intro') }}
@stop

@section('content')
<div class="container donation-content mobile-padd no-padding "> <span>Introduce Yourself</span>
	<p>You're almost done! Start getting to know your group now by introducing yourself. This introduction will post as a new discussion in your group and members will be notified.</p>
</div>
<div class="container no-padding">
	<div class="col-lg-12 welcome-content welcone-pedding group-font clearfix ">
		<h4>Introduction Message:</h4>
		@foreach ($errors->all('<li>:message</li>') as $error)
			{{ $error }}
		@endforeach

		<p>
		<!-- <form class="form-horizontal cust-control"> -->
		{{ html()->form('POST', url('/wizard/intro'))->class('form-horizontal cust-control')->attribute('name', 'intro')->open() }}
			{{ html()->hidden('title', 'Introducing ' . $user->firstname . ' ' . $user->lastname . '!') }}
			<div class="form-group cust-group">
				<div class="col-sm-12">
					<!-- <textarea class="form-control cust-form" rows="6" placeholder="Write your introduction message here. Tell your group a little bit about yourself,why you joined, and your goals."></textarea> -->
					{{ html()->textarea('comment')->rows('6')->class('form-control cust-form')->placeholder('Write your introduction message here. Tell your group a little bit about yourself,why you joined, And your hopes for participating.') }}
				</div>
			</div>
			@if (count($group_select))
				<div class="form-group cust-group">
					<label class="col-sm-12" for="group">Post message to:</label>
					<div class="col-sm-5">
						{{ html()->select('group_id', $group_select, $user->default_group_id)->class('basic-example')->id('group') }}
					</div>
				</div>
			@else
					{{ html()->hidden('group', $user->default_group_id) }}
			@endif
		<div class="col-lg-12 col-md-12  no-padding">
			<a href="{{ URL::route('wizard.finish',array('intro')) }}" title="Skip this part" class="btn btn-cancel  small-button" role="button">SKIP</a>
			<!-- <a href="#" onclick="document.intro.submit();" title="Submit your introduction" class="btn btn-success big-button btn-two" role="button">SEND </a> -->
			<button title="Submit your introduction" class="btn btn-success big-button btn-two">SEND </button>
		</div>
		<!-- </form> -->
		{{ html()->form()->close() }}
	</div>
</div>
@stop

@section('scripts')
<script src="{{ asset('/js/jquery-ui.js') }}"></script>
<script src="{{ asset('/js/selectBoxIt.js') }}"></script>
<script>
	$("select").selectBoxIt({
		autoWidth: false,
		showFirstOption: false
	});
</script>
@stop