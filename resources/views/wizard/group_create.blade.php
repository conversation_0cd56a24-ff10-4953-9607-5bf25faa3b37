@extends('layouts.wizard')

@section('title')
	Create a Group
@stop

@section('head')
	@include('groups.create_head')
{{ App\Models\Wizard::setupClasses('group') }}
@stop

@section('content')
  <div class="container home-content no-padding"> <span>My Group</span> </div>
  @include('groups.create_form',array('wizard'=>true))
@stop

@section('scripts')
	<script src="{{ asset('/js/bootstrap-datepicker.js') }}"></script>
	@include('groups.create_scripts')
@stop