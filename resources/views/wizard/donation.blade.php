@extends('layouts.wizard')

@section('title')
	Set up my donation
@stop

@section('head')
<link type="text/css" rel="stylesheet" href="{{ asset('/css/jquery-ui2.css?version=1') }}" media="all">
<link type="text/css" rel="stylesheet" href="{{ asset('/css/welcomenav.css?version=1') }}" media="all">
<link type="text/css" rel="stylesheet" href="{{ asset('/css/bootstrap-datetimepicker.css?version=1') }}" media="all">
<link type="text/css" rel="stylesheet" href="{{ asset('/css/selectboxit.css?version=1') }}" media="all">
{{ App\Models\Wizard::setupClasses('donation') }}
@stop

@section('content')
<div class="container donation-content no-padding">
	<div class="col-lg-12 col-sm-12 col-xs-12"> <span>Set up my donation</span>
		<p style="padding-bottom: 15px;">Common Change gives you the ability to set up one-time and recurring donations with a few simple clicks</p>
	</div>
</div>
<div class="container no-padding">
	<div class="container centerposition">
		<div class="group_tab">
			<a class="btn {{ $panel_active_us_and_others_pill }} small-button donation-tab" href="#us" aria-controls="us" role="tab" data-toggle="tab">UNITED STATES</a>
			<a class="btn {{ $panel_active_sa_pill }} small-button donation-tab" href="#sa" aria-controls="sa" role="tab" data-toggle="tab">SOUTH AFRICA</a>
			<a class="btn {{ $panel_active_uk_pill }} small-button donation-tab" href="#uk" aria-controls="uk" role="tab" data-toggle="tab">UNITED KINGDOM</a>
		</div>
	</div>
	<style>.welcone-pedding {padding-bottom: 15px}</style>
	<div class="col-lg-12 welcome-content welcone-pedding finaces-font clearfix ">
		<div class="col-lg-12 col-sm-12 finaces-content clearfix">
		<h4>Donation</h4>

		<!-- form strat -->
		<!-- <form class="form-horizontal cust-control"> -->
		{{ html()->form('POST', route('wizard.donate'))->class('form-horizontal cust-control')->id('donate-form')->open() }}
			@include('finances.donation_form', array('wizard'=>1))
		{{ html()->form()->close() }}
	</div>
	</div>
</div>
@stop

@section('scripts')
<script src="{{ asset('/js/bootstrap-toggle.js') }}"></script>
<script src="{{ asset('/js/bootstrap-datetimepicker.min.js') }}"></script>
<script src="{{ asset('/js/jquery-ui.js') }}"></script>
<script type="text/javascript">
	$(document).ready(function () {

		// Donation Tab Active/Inactive JS
		$('.donation-tab').on('click',function(){
			$('.donation-tab').each(function(){
				$(this).removeClass('btn-success2');
				$(this).addClass('discution');
			});

			$(this).addClass('btn-success2');
			$(this).removeClass('discution');
		});

		// DateTime picker for Dates
		$("#startDate").datetimepicker();

		// Calculates total donation
		function calculate_total() {
			var t1 = ($("#amount_commonchange").val()) ? parseFloat($("#amount_commonchange").val()) : 0;
			var t2 = ($("#amount_group").val()) ? parseFloat($("#amount_group").val()) : 0;
			var total = t1 + t2;
			$("#total_donation").text(total.toFixed(2));
		}
		calculate_total();
		$( "#amount_commonchange" ).keyup(calculate_total);
		$( "#amount_group" ).keyup(calculate_total);
	});
</script>
<script>
	$(function () {
		$('[data-toggle="tooltip"]').tooltip();
	});
</script>
<script src="{{ asset('/js/selectBoxIt.js') }}"></script>
<script>
	$("select").selectBoxIt({
		autoWidth: false,
		showFirstOption: false
	});
</script>

@if(!empty($_ENV['STRIPE_PUBLIC_KEY']))
	<script src="{{ asset('https://checkout.stripe.com/checkout.js') }}"></script>
	<script type="text/javascript">
		var handler = StripeCheckout.configure({
			key: '{{ $_ENV['STRIPE_PUBLIC_KEY'] }}',
			image: 'https://s3.amazonaws.com/stripe-uploads/acct_1031Km2NY3YN92fOmerchant-icon-1459460783202-AppleiPadRetina.png',
			token: function(token) {
				var $form = $('#donate-form');
				// Insert the token into the form so it gets submitted to the server
				$form.append($('<input type="hidden" name="stripeToken" />').val(token.id));
				$form.get(0).submit();
			}
		});

		$('#submit-donate').on('click',function(){
			//=== set error flag as false by default
			var err = false;
			//=== remove all errors at first
			$('.error').remove();

			//==== check whether location is set
			if($('#donation_type').val() == '' || $('#donation_type').val() == null){
				$('#donation_type').parent().prepend('<span class="error"> The donation type field is required.</span>');
				err = true;
			}

			//=== check whether donation field is set
			if($('#donation_type').val() == 'subscription' && ( $('#subscription_period').val()=='' || $('#subscription_period').val() == null )){
				$('#subscription_period').parent().prepend('<span class="error"> The subscription period field is required when donation type is subscription.</span>');
				err = true;
			}

			//==== check whether donation value is set as numeric value
			if($('#amount_commonchange').val() != '' &&  !$.isNumeric($('#amount_commonchange').val())){
				$('#amount_commonchange').parent().prepend('<span class="error" style="font-family: inherit; display: block"> The amount has to be a number.</span>');
				err = true;
			}

			const group_val = $('#amount_group').val()

			if(group_val != ''){
				if(!$.isNumeric(group_val)){
					$('#amount_group').parent().prepend('<span class="error" style="font-family: inherit; display: block"> The amount has to be a number.</span>');
					err = true;
				}else if(group_val < 3 && group_val > 0){
					$('#amount_group').parent().prepend('<span class="error" style="font-family: inherit; display: block"> If you\'re making a group contribution, it must be at least $3.00.</span>');
					err = true;
				}
			}

			//==== process the form if there is no error found
			if(err == false){
				handler.open({
					name: 'CommonChange.com',
					description: 'Common Change Donation',
					email: '{{ Auth::User()->email }}',
					zipCode: true,
					panelLabel: 'Donate $' + $('#total_donation').html()
				});
			}

		});

		// Close Checkout on page navigation
		$(window).on('popstate', function() {
			handler.close();
		});
	</script>
@endif
@stop
