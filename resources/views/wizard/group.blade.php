@extends('layouts.wizard')

@section('title')
	Join a Group
@stop

@section('head')
<link type="text/css" rel="stylesheet" href="{{ asset('/css/jquery-ui.css?version=1') }}" media="all">
<link type="text/css" rel="stylesheet" href="{{ asset('/css/selectboxit.css?version=1') }}" media="all">
{{ App\Models\Wizard::setupClasses('group') }}
@stop

@section('content')
  <div class="container mygroup-content no-padding mobile-padd"> <span>My Group</span>

	@if (Session::get('message',null) !== null)
		<p>{{ Session::get('message') }}</p>
	@endif

    <p>Groups are usually formed based on geographic proximity, shared interest, relational connection or community. Each Common Change group operates autonomously as defined in the group covenant.</p>
    <p>Where to start? Browse and join a “Public” group; contact a group facilitator directly for an invitation to a “Private” group; or create your own Group and invite others to join you. </p>

  </div>

  @if(count($invitedGroups))
  <div class="container no-padding clearfix">
  	<h4>Group Invite:</h4>
  	<p>You've already been invited to the following group(s).  Click "View Invite" to view group details and accept the invitation.</p>
    <div class="container group-list no-padding clearfix">
		@foreach ($invitedGroups as $group)
			<div class="col-sm-6 col-xs-12 no-padding">
			@include('groups.small_pane',array('wizard'=>1))
			</div>
		@endforeach
  	</div>
  </div>
  @endif

  <div class="container no-padding clearfix">
    @if(count($invitedGroups))
    <h4>Find a group:</h4>
    @endif
    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 welcome-content but-margin3 contain-margin clearfix">
	{{ html()->modelForm($input, 'GET', route('wizard.group'))->class('form-horizontal margin-mobile')->attribute('name', 'group')->open() }}

      <div class="col-lg-5 col-md-5 col-sm-3 col-xs-12 no-padding ">
      <a href="/wizard/group/create" title="" class="btn btn-success small-button" role="button"><span><img src="/images/plus.png"></span>CREATE A GROUP</a> </div>
      <div class="col-lg-2 col-md-2 col-sm-3 col-xs-12 chakbox-margin no-padding">
		<!-- <div class="ui-widget"> -->
			<!-- <select id="combobox">
				<option value="">Select one...</option>
				<option value="option2"> Default</option>
				<option value="option3">Alphabetical (A-Z)</option>
				<option value="option4">Alphabetical (Z-A)</option>
				<option value="option5">State</option>
				<option value="option5">Country</option>
				<option value="option5">Recent (??)</option>
			</select> -->
			{{ html()->select('sort', array('alpha' => 'Alphabetical (A-Z)', 'revalpha' => 'Alphabetical (Z-A)', 'state' => 'State', 'country' => 'Country'))->attribute('onchange', 'document.group.submit()') }}
		<!-- </div> -->
	</div>
	<div class="col-lg-2 col-md-2 col-sm-3 col-xs-12">
		<!-- <form class="navbar-form no-margin" role="search"> -->
		<div class="input-group">
			<!-- <input type="text" class="form-control cust-form" placeholder="Search" name="q"> -->
			{{ html()->text('search')->class('form-control cust-form')->placeholder('Search') }}
			<div class="input-group-btn">
				<button class="btn btn-success btn-default" type="submit"><i class="glyphicon glyphicon-search"></i></button>
			</div>
		</div>
		<!-- </form> -->
		{{ html()->closeModelForm() }}
	</div>
      <div class="col-lg-3 col-md-3 col-sm-3 col-xs-12 no-padding">
       <a href="{{ URL::route('wizard.finish',array('group')) }}" title="" class="btn btn-success  privet-group " role="button">CONTINUE WITHOUT GROUP</a> </div>
    </div>

    <!--main contain part -->
     <!-- part1 strat -->
		<div class="container group-list no-padding clearfix">
		{{ $pGroups->links() }}
		@forelse ($groups as $duple)
			<!-- <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 group-description signup no-padding people-margindbottom"> -->
			<div class="row">
			@foreach ($duple as $group)
				<div class="col-sm-6 col-xs-12 clearfix padding">
				@include('groups.small_pane',array('wizard'=>1))
				</div>
			@endforeach
			</div>
		@empty
			<p>No groups to display.</p>
		@endforelse
		</div>
  </div>
@stop

@section('scripts')
<script src="{{ asset('/js/jquery-ui.js') }}"></script>
<script src="{{ asset('/js/selectBoxIt.js') }}"></script>
<script>
	console.log('did it');
	$("select").selectBoxIt({
		autoWidth: false
	});
</script>
@stop