@extends('layouts.wizard')

@section('title')
	Account Details
@stop

@section('head')
<link type="text/css" rel="stylesheet" href="{{ asset('/css/bootstrap-toggle.css?version=1') }}" media="all">
<link type="text/css" rel="stylesheet" href="{{ asset('/css/jquery-ui1.css?version=1') }}" media="all">
<link type="text/css" rel="stylesheet" href="{{ asset('/css/selectboxit.css?version=1') }}" media="all">
{{ App\Models\Wizard::setupClasses('account') }}
<style>
input[type="file"] {
    display: none;
}

</style>
{{ App\Models\Wizard::setupClasses('account') }}
@stop

@section('content')
<div class="container welcome-contentpart no-padding">
	<span>Welcome to Common Change!</span>
	<p>Thank you for joining Common Change. Follow the 4-step wizard above to get started.</p>
</div>
  <div class="container no-padding">
    <div class="col-lg-12 welcome-content welcome-color welcone-pedding clearfix text-padding1">
      <h4>My Account</h4>

      <!-- my account contain form start -->
	  {{ html()->modelForm($user, 'POST', url('wizard/account'))->class('form-horizontal cust-control')->attribute('name', 'account')->acceptsFiles()->open() }}
      <!-- <form class="form-horizontal cust-control"> -->
      @include('account.profile_form')
      <!-- </form> -->
	  {{ html()->closeModelForm() }}

      <!-- Submit buttons -->

      <div class="col-lg-3 col-md-3 col-sm-3 col-xs-12 but-padding no-padding"> <a  title="Submit a need Request" class="btn btn-success big-button" role="button" onclick="document.account.submit();"><span></span>SAVE & CONTINUE</a> </div>
      <!-- end Submit Need Request -->
    </div>
    <!-- end edit group detail contain -->
</div>
@stop

@section('scripts')
<script src="{{ asset('/js/bootstrap-toggle.js') }}"></script>
<script src="{{ asset('/js/jquery-ui.js') }}"></script>
<script src="{{ asset('/js/selectBoxIt.js') }}"></script>
<script>
	console.log('did it');
	$("select").selectBoxIt({
		autoWidth: false,
		showFirstOption: false
	});
</script>

	<script>
	$(document).ready(function(){
		$(window).resize(function() {

			ellipses1 = $("#bc1 :nth-child(2)")
			if ($("#bc1 a:hidden").length >0) {ellipses1.show()} else {ellipses1.hide()}

			ellipses2 = $("#bc2 :nth-child(2)")
			if ($("#bc2 a:hidden").length >0) {ellipses2.show()} else {ellipses2.hide()}

		})

	});
	</script>
	<script>
	$(document).on('change', '.file-upload-bg :file', function() {
		var input = $(this),
		numFiles = input.get(0).files ? input.get(0).files.length : 1,
		label = input.val().replace(/\\/g, '/').replace(/.*\//, '');
		input.trigger('fileselect', [numFiles, label]);
	});

	$(document).ready( function() {
		$('.file-upload-bg :file').on('fileselect', function(event, numFiles, label) {
			log = numFiles > 1 ? numFiles + ' files selected' : label;
			console.log(log);
			$("#show_file").val(log);
			return;
		});
	});
	</script>
@stop