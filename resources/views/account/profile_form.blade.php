@inject('locationHelper', 'App\Models\LocationHelper')
        <div class="form-group ">
          <label for="firstname" class="col-sm-2 control-label"> <span>First Name:</span></label>
          <div class="col-sm-4">
            <!-- <input type="text" class="form-control cust-form" id="input" placeholder=""> -->
			@error('firstname')
        <span class="error">{{ $message }}</span>
      @enderror
			{{ html()->text('firstname')->class('form-control cust-form')->id('firstname')->placeholder('First name')->attribute('autocomplete', 'given-name') }}
          </div>
        </div>
        <div class="form-group ">
          <label for="lastname" class="col-sm-2 control-label"><span>Last Name:</span></label>
          <div class="col-sm-4">
			<!-- <input type="text" class="form-control cust-form" id="input" placeholder=""> -->
			@error('lastname')
        <span class="error">{{ $message }}</span>
      @enderror
			{{ html()->text('lastname')->class('form-control cust-form')->id('lastname')->placeholder('Last name')->attribute('autocomplete', 'family-name') }}
          </div>
        </div>
        <div class="form-group">
          <label for="email" class="col-sm-2 control-label"><span>Email:</span></label>
          <div class="col-sm-4">
            <!-- <input type="email" class="form-control cust-form" id="input" placeholder=""> -->
			@error('email')
        <span class="error">{{ $message }}</span>
      @enderror
			{{ html()->email('email')->class('form-control cust-form')->id('email')->attribute('placeholder', 'Email Address') }}
          </div>
        </div>
        <div class="form-group">
          <label for="default_email_notification" class="col-sm-2 control-label"> <span>Notification Preference:</span></label>
          <div class="col-sm-4">
      @error('default_email_notification')
        <span class="error">{{ $message }}</span>
      @enderror
      {{ html()->select('default_email_notification', array(0 => 'Please select one of the following:', 'daily_digest' => 'Send a Daily Digest of all my groups', 'daily_per_discussion' => 'One email per day per Discussion', 'per_comment' => 'One email per comment', 'none' => "Don't send email updates")) }}
          </div>
        </div>
        <div class="form-group">
          <label for="share_email" class="col-sm-2 control-label"><span>Share Email with <br>
            Common Change Users:</span></label>
          <div class="col-sm-4">
			<table>
				<tr>
					<td>{{ html()->checkbox('share_email', null, 'true')->id('share_email')->class('css-checkbox3') }}
					<label for="share_email" class="css-label3"></label></td>
				</tr>
			</table>
          </div>
        </div>
        <div class="form-group">
          <label for="addr1" class="col-sm-2 infoimg control-label"><span>Address:</span></label>
          <div class="col-sm-4">
            <!-- <input type="text" class="form-control input-margin cust-form" id="input"  placeholder=""> -->
			@error('addr1')
        <span class="error">{{ $message }}</span>
      @enderror
			{{ html()->text('address[addr1]')->class('form-control input-margin cust-form')->id('addr1')->placeholder('Street Address') }}
            <!-- <input type="text" class="form-control cust-form" id="input" placeholder=""> -->
			@error('addr2')
        <span class="error">{{ $message }}</span>
      @enderror
			{{ html()->text('address[addr2]')->class('form-control cust-form')->id('addr2')->placeholder('Apt / Suite / etc') }}
          </div>
        </div>
        <div class="form-group">
          <label for="city" class="col-sm-2 control-label"> <span>City:</span></label>
          <div class="col-sm-4">
            <!-- <input type="text" class="form-control cust-form" id="input" placeholder=""> -->
			@error('city')
        <span class="error">{{ $message }}</span>
      @enderror
			{{ html()->text('address[city]')->class('form-control cust-form')->id('city')->placeholder('City') }}
          </div>
        </div>
        <div class="form-group">
          <label for="state" class="col-sm-2 control-label"> <span>State:</span></label>
          <div class="col-sm-4">
            <!--
			<select class="basic-example">
              <option value="option1">Not in the United States</option>
              <option value="option2">USA</option>
              <option value="option1">USA</option>
            </select>
			-->
			@error('state')
        <span class="error">{{ $message }}</span>
      @enderror
			{{ html()->select('address[state]', $locationHelper->subdivisionsForSelect('US')) }}
          </div>
        </div>
        <div class="form-group">
          <label for="postcode" class="col-sm-2 control-label"> <span>Postal Code:</span></label>
          <div class="col-sm-4">
            <!-- <input type="text" class="form-control cust-form" id="input" placeholder=""> -->
			@error('postcode')
        <span class="error">{{ $message }}</span>
      @enderror
			{{ html()->text('address[postcode]')->class('form-control cust-form')->id('postcode')->placeholder('Postal Code') }}
          </div>
        </div>
        <!-- select option -->
        <div class="form-group">
          <label for="country" class="col-sm-2 control-label"> <span>Country:</span></label>
          <div class="col-sm-4">
			<!--
            <select class="basic-example">
              <option value="option1">USA</option>
              <option value="option2">INDIA</option>
              <option value="option1">CHINA</option>
            </select>
			-->
			@error('country')
        <span class="error">{{ $message }}</span>
      @enderror
			{{ html()->select('address[country]', $locationHelper->countriesForSelect()) }}
          </div>
        </div>
        <div class="form-group">
          <label for="facebook" class="col-sm-2 control-label"> <span>Facebook URL:</span></label>
          <div class="col-sm-4">
            <!-- <input type="text" class="form-control cust-form" id="input" placeholder=""> -->
			@error('facebook')
        <span class="error">{{ $message }}</span>
      @enderror
			{{ html()->text('facebook')->class('form-control cust-form')->id('facebook')->placeholder('Facebook') }}
          </div>
        </div>
        <div class="form-group">
          <label for="twitter" class="col-sm-2 control-label"> <span>Twitter URL:</span></label>
          <div class="col-sm-4">
            <!-- <input type="text" class="form-control cust-form" id="input" placeholder=""> -->
			@error('twitter')
        <span class="error">{{ $message }}</span>
      @enderror
			{{ html()->text('twitter')->class('form-control cust-form')->id('twitter')->placeholder('Twitter') }}
          </div>
        </div>
        <!-- photo upload -->
        <div class="form-group clearfix">
          <div class="col-sm-12 profile"> <label for="profile_pic"> Profile Picture:</label></div>
        </div>
        <div class="form-group clearfix">
            <div class="col-sm-4 col-sm-push-6 col-xs-12">
              <label for="profile_pic" class="btn btn-success big-button file-upload-bg">
                <!-- Form::text('profile_pic',null,array('class'=>'form-control cust-form col-sm-5', 'id'=>'input', 'placeholder'=>'No File Selected')) -->
                {{ html()->file('profile_pic')->id('profile_pic') }}
                <span>CHOOSE FILE</span>
              </label>
            </div>
            <div class="col-sm-2 col-sm-pull-4 col-xs-6">
              <!-- <img src="/images/profile-photos.png"> -->
              <img class="img-responsive img-thumbnail col-sm-12 col-xs-6 no-padding" src="{{ $user->profile_pic }}">
            </div>
            <div class="col-sm-4 col-sm-pull-4 col-xs-12">
              @error('profile_pic')
                <span class="error">{{ $message }}</span>
              @enderror
              <input id="show_file" type="text" class="form-control cust-form" readonly placeholder="No File Selected">
              <span class="help-block col-sm-12 col-xs-6 no-padding">
                Images should be smaller than 2&nbsp;MB in size, and should measure at least 250x250 pixels.
              </span>
            </div>
        </div>
