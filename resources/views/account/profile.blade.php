@extends('layouts.no_container')

@section('title')
	Account Details
@stop

@section('head')
<link type="text/css" rel="stylesheet" href="{{ asset('/css/bootstrap-toggle.css?version=1') }}" media="all">
<link type="text/css" rel="stylesheet" href="{{ asset('/css/welcomenav.css?version=2') }}" media="all">
<link type="text/css" rel="stylesheet" href="{{ asset('/css/jquery-ui1.css?version=1') }}" media="all">
<link type="text/css" rel="stylesheet" href="{{ asset('/css/selectboxit.css?version=1') }}" media="all">
<style>
input[type="file"] {
    display: none;
}

img.profile_pic {
	margin:0 auto;
	width:157px;
	height:auto;
	#border:1px solid #aaaaaa;
}

</style>
@stop

@section('content')
<section id="main-bg" class="bg3-padding">
	<div class="container welcome-contentpart mobile-padd no-padding"> <span>MY ACCOUNT</span> </div>
	@include('account.bar',array('a' => 'btn-success1', 'b' => 'privet-group cust-form', 'c' => 'privet-group cust-form', 'd' => 'privet-group cust-form'))
  <div class="container no-padding">
    <div class="col-lg-12 welcome-content welcome-color welcone-pedding clearfix text-padding1">
      <h4>My Account</h4>
      
      <!-- my account contain form start -->
	  {{ html()->modelForm($user, 'PUT', route('account.profile'))->class('form-horizontal cust-control')->attribute('name', 'account')->acceptsFiles()->open() }}
      <!-- <form class="form-horizontal cust-control"> -->
      @include('account.profile_form')
      <!-- </form> -->
	  {{ html()->closeModelForm() }}
      
      <!-- Submit buttons -->
      
      <div class="col-lg-3 col-md-3 col-sm-3 col-xs-12 but-padding no-padding"> <a  title="Submit a need Request" class="btn btn-success big-button" role="button" onclick="document.account.submit();"><span></span>SAVE CHANGES</a> </div>
      <!-- end Submit Need Request --> 
    </div>
    <!-- end edit group detail contain -->
</div>
</section>
@stop

@section('scripts')
<script src="{{ asset('/js/bootstrap-toggle.js') }}"></script>
<script src="{{ asset('/js/jquery-ui.js') }}"></script>
<script src="{{ asset('/js/selectBoxIt.js') }}"></script>
<script src="{{ asset('/js/moment.min.js') }}"></script>
<script src="{{ asset('/js/moment-timezone-with-data-2012-2022.min.js') }}"></script>
<script src="{{ asset('/js/js.cookie.min.js') }}"></script>
<script>
	$("select").selectBoxIt({
		autoWidth: false,
		showFirstOption: false
	});
</script>
<script>
$(document).ready(function(){
	$(window).resize(function() {

		ellipses1 = $("#bc1 :nth-child(2)")
		if ($("#bc1 a:hidden").length >0) {ellipses1.show()} else {ellipses1.hide()}

		ellipses2 = $("#bc2 :nth-child(2)")
		if ($("#bc2 a:hidden").length >0) {ellipses2.show()} else {ellipses2.hide()}

	});
});
</script> 
<script> 
$(document).on('change', '.btn-file :file', function() {
	var input = $(this),
	numFiles = input.get(0).files ? input.get(0).files.length : 1,
	label = input.val().replace(/\\/g, '/').replace(/.*\//, '');
	input.trigger('fileselect', [numFiles, label]);
});

$(document).ready( function() {
	$('.btn-file :file').on('fileselect', function(event, numFiles, label) {
		log = numFiles > 1 ? numFiles + ' files selected' : label;
		console.log(log);
		$("#show_file").val(log);
		return;
	});
});
</script>
<script>
$(document).ready( function() {
	var tz = moment.tz.guess()
	console.log('Guessed timezone: ' + tz);
	Cookies.set('user_tz_name', tz);
});
</script>
@stop