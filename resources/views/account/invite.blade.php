@extends('layouts.no_container')

@section('title')
	Invite Friends
@stop

@section('head')
	<link type="text/css" rel="stylesheet" href="{{ asset('/css/dropdown.css?version=1') }}" media="all">
	<link type="text/css" rel="stylesheet" href="{{ asset('/css/welcomenav.css?version=2') }}" media="all">
@stop

@section('content')
<section id="main-bg" class="bg3-padding">
	<div class="container welcome-contentpart mobile-padd no-padding"> <span>MY ACCOUNT</span> </div>
	@include('account.bar',array('c' => 'btn-success1', 'b' => 'privet-group cust-form', 'a' => 'privet-group cust-form', 'd' => 'privet-group cust-form'))
	<div class="container no-padding">
		<div class="col-lg-12 col-xs-12 col-sm-12 welcome-content contain-margin clearfix">
			<div class="col-lg-12 col-xs-12 col-sm-12 myaccount-cantain clearfix">
				<h2>INVITE YOUR FRIENDS</h2>
				{{ html()->form('POST', route('invite.send'))->class('form-horizontal cust-control clearfix')->attribute('name', 'invite')->open() }}
					{{ html()->hidden('group_id', Auth::User()->default_group_id) }}
					<div class="col-lg-12 col-xs-12 col-sm-12 no-padding">
						<table>
						<tbody>
							<tr>
								<td><!-- <input type="radio" name="checkboxG4" value="group" id="checkboxG4" class="css-checkbox1"> -->
									{{ html()->radio('invite_to', true, 'group')->id('invite_group')->class('css-checkbox1') }}
									<label for="invite_group" class="css-label1 font1">To join your group ({{Auth::User()->default_group()->first()->name }})</label>
								</td>
							</tr>
						</tbody>
						</table>
						<table>
						<tbody>
							<tr>
							<td><!-- <input type="radio" name="checkboxG4" value="site" id="checkboxG45" class="css-checkbox1" checked> -->
								{{ html()->radio('invite_to', null, 'site')->id('invite_site')->class('css-checkbox1') }}
								<label for="invite_site" class="css-label1 font1">To join Common Change</label>
							</td>
							</tr>
						</tbody>
						</table>
					</div>

					<!-- <form class="form-horizontal cust-control clearfix"> -->
					
						@foreach ($errors->all('<li>:message</li>') as $error)
							{{ $error }}
						@endforeach

						<div id="emails"></div>
						<div class="col-sm-3 no-padding"><a onclick="addBlankRow()"> + Add More People</a></div><br /><br />
						<div class="form-group cust-group">

							<div class="col-sm-9 col-xs-12 col-lg-9 ">
								<!-- <textarea class="form-control cust-form" rows="5" placeholder=""></textarea> -->
								<p>Add a short introduction to let them know why you think they might be a good fit for, or interested in the group and Common Change:</p>
								{{ html()->textarea('message')->class('form-control cust-form')->rows('5')->placeholder('Your message here') }}
							</div>
						</div>
					<!-- </form> -->
				<div class="clearfix"></div>

				<div class="col-lg-4 col-md-4 col-xs-12 no-padding butaccount-margin">
					<a href="#" title="View Request" class="btn btn-success  privet-group small-button" role="button">CANCEL</a> 
					<button type="submit" title="View Request" class="btn btn-success small-button">SEND</button>
				</div>
				{{ html()->form()->close() }}
			</div>
		</div>
	</div>
</section>
@stop

@section('scripts')
	<script src="{{ asset('/js/bootstrap-toggle.js') }}"></script>
	<script>
		$(document).ready(function() {
			window.numrows = 0;

			@if(!empty($old_input))
				@foreach($old_input['email'] as $email)
					addRow({{ json_encode($email['firstname']) }}, {{ json_encode($email['lastname']) }}, {{ json_encode($email['email']) }});
				@endforeach
			@else
				addRow('','','');
			@endif
			
		});
		
		function addBlankRow() { addRow('','',''); }

		function addRow(firstname, lastname, email) {
			num = window.numrows;
			$("#emails").append(
				'<div class="form-group cust-group">' +
					'<div class="col-sm-3 col-xs-12 margin-mobile">' +
						'<input type="text" name="email['+num+'][firstname]" value="'+firstname+'" class="form-control cust-form" placeholder="First Name">' +
					'</div>' +
					'<div class="col-sm-3 col-xs-12 margin-mobile">' +
						'<input type="text" name="email['+num+'][lastname]" value="'+lastname+'" class="form-control cust-form" placeholder="Last Name">' +
					'</div>' +
					'<div class="col-sm-3 col-xs-12 margin-mobile">' +
						'<input type="text" name="email['+num+'][email]" value="'+email+'" class="form-control cust-form" placeholder="Email Address">' +
					'</div>' +
				'</div>');
			window.numrows++;
		}
	</script>
@stop