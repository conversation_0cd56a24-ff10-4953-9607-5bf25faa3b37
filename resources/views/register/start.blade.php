@extends('layouts.modern')

@section('title', 'Create Your Profile')

@section('head')
<link rel="stylesheet" href="{{ asset('css/registration-centered.css') }}">
<style>
    /* Override text alignment for registration form labels */
    #registration-form .form-label,
    #registration-form label {
        text-align: left !important;
        display: block;
        font-family: 'Inter', sans-serif;
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 21px;
        color: #1A1A1A;
        margin-bottom: 6px;
    }

    .btn-outline {
        padding: 17px 24px;
        width: 150px;
        height: 50px;
    }
</style>
@endsection

@section('content')
<div class="container">
    <div class="logo-container">
        <img src="/images/logo.png" alt="Common Change Logo">
    </div>

    <div class="heading">Create Your Profile</div>
    <div class="message">Let's set up your account details.</div>

    <div class="form-content">
        <form id="registration-form" method="POST" action="{{ route('register.complete') }}" enctype="multipart/form-data">
            @csrf
            <input type="hidden" name="email" value="{{ $email ?? old('email') }}">

            <div class="form-group">
                <label class="form-label" for="firstname">First Name</label>
                <input type="text" id="firstname" name="firstname" class="form-input" value="{{ old('firstname') }}" required>
                @error('firstname')
                    <span class="error">{{ $message }}</span>
                @enderror
            </div>

            <div class="form-group">
                <label class="form-label" for="lastname">Last Name</label>
                <input type="text" id="lastname" name="lastname" class="form-input" value="{{ old('lastname') }}" required>
                @error('lastname')
                    <span class="error">{{ $message }}</span>
                @enderror
            </div>

            <div class="form-group">
                <label class="form-label" for="address">Address</label>
                <input type="text" id="address" name="address[addr1]" class="form-input" value="{{ old('address.addr1') }}" required>
                @error('address.addr1')
                    <span class="error">{{ $message }}</span>
                @enderror
            </div>

            <div class="form-group">
                <label class="form-label" for="city">City</label>
                <input type="text" id="city" name="address[city]" class="form-input" value="{{ old('address.city') }}" required>
                @error('address.city')
                    <span class="error">{{ $message }}</span>
                @enderror
            </div>

            <div class="form-group">
                <label class="form-label" for="state">State</label>
                <select id="state" name="address[state]" class="form-select" required>
                    <option value="">Select a state</option>
                    @foreach($states ?? [] as $code => $name)
                        <option value="{{ $code }}" {{ old('address.state') == $code ? 'selected' : '' }}>{{ $name }}</option>
                    @endforeach
                </select>
                @error('address.state')
                    <span class="error">{{ $message }}</span>
                @enderror
            </div>

            <div class="form-group">
                <label class="form-label" for="postcode">Postal Code</label>
                <input type="text" id="postcode" name="address[postcode]" class="form-input" value="{{ old('address.postcode') }}" required>
                @error('address.postcode')
                    <span class="error">{{ $message }}</span>
                @enderror
            </div>

            <div class="form-group">
                <label class="form-label" for="country">Country</label>
                <select id="country" name="address[country]" class="form-select" required>
                    <option value="">Select a country</option>
                    @foreach($countries ?? [] as $code => $name)
                        <option value="{{ $code }}" {{ old('address.country') == $code ? 'selected' : '' }}>{{ $name }}</option>
                    @endforeach
                </select>
                @error('address.country')
                    <span class="error">{{ $message }}</span>
                @enderror
            </div>

            <div class="form-group">
                <label class="form-label">Profile Picture</label>
                <div class="profile-picture-container">
                    <div class="profile-picture">
                        <img id="profile-preview" src="{{ asset('images/profile-placeholder.png') }}" alt="Profile Picture">
                    </div>
                    <label for="profile_pic" class="btn btn-outline">
                        Upload Image
                        <input type="file" id="profile_pic" name="profile_pic" accept="image/*">
                    </label>
                    @error('profile_pic')
                        <span class="error">{{ $message }}</span>
                    @enderror
                </div>
            </div>

            <div class="button-container">
                <button type="submit" class="btn btn-primary btn-wide">Save & Continue</button>
            </div>
        </form>
    </div>

    <div id="loading-overlay" class="loading-overlay">
        <div class="loading-spinner"></div>
        <p id="loading-text">Creating your account...</p>
    </div>
</div>
@endsection

@section('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const form = document.getElementById('registration-form');
        const profileInput = document.getElementById('profile_pic');
        const profilePreview = document.getElementById('profile-preview');
        const loadingOverlay = document.getElementById('loading-overlay');

        // Handle profile picture preview
        profileInput.addEventListener('change', function() {
            const file = this.files[0];
            if (file) {
                // Validate file size (max 2MB)
                if (file.size > 2 * 1024 * 1024) {
                    alert('Profile picture must be less than 2MB in size.');
                    this.value = '';
                    return;
                }

                // Validate file type
                const validTypes = ['image/jpeg', 'image/png', 'image/jpg', 'image/gif'];
                if (!validTypes.includes(file.type)) {
                    alert('Profile picture must be a JPEG, PNG, JPG, or GIF file.');
                    this.value = '';
                    return;
                }

                // Show preview
                const reader = new FileReader();
                reader.onload = function(e) {
                    profilePreview.src = e.target.result;

                    // Check image dimensions
                    const img = new Image();
                    img.onload = function() {
                        if (img.width < 250 || img.height < 250) {
                            alert('Profile picture must be at least 250x250 pixels.');
                            profileInput.value = '';
                            profilePreview.src = "{{ asset('images/profile-placeholder.png') }}";
                        }
                    };
                    img.src = e.target.result;
                }
                reader.readAsDataURL(file);
            }
        });

        // Form validation
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            let isValid = true;
            let errorMessages = [];

            // Validate first name
            const firstname = document.getElementById('firstname').value.trim();
            if (!firstname) {
                isValid = false;
                errorMessages.push('First name is required');
            }

            // Validate last name
            const lastname = document.getElementById('lastname').value.trim();
            if (!lastname) {
                isValid = false;
                errorMessages.push('Last name is required');
            }

            // Validate address
            const address = document.getElementById('address').value.trim();
            if (!address) {
                isValid = false;
                errorMessages.push('Address is required');
            }

            // Validate city
            const city = document.getElementById('city').value.trim();
            if (!city) {
                isValid = false;
                errorMessages.push('City is required');
            }

            // Validate state
            const state = document.getElementById('state').value;
            if (!state) {
                isValid = false;
                errorMessages.push('State is required');
            }

            // Validate postal code
            const postcode = document.getElementById('postcode').value.trim();
            if (!postcode) {
                isValid = false;
                errorMessages.push('Postal code is required');
            } else if (!/^[a-zA-Z0-9\-\s]+$/.test(postcode)) {
                isValid = false;
                errorMessages.push('Postal code can only contain letters, numbers, spaces, and hyphens');
            }

            // Validate country
            const country = document.getElementById('country').value;
            if (!country) {
                isValid = false;
                errorMessages.push('Country is required');
            }

            if (!isValid) {
                let errorHtml = '<ul style="text-align: left; margin-top: 0;">';
                errorMessages.forEach(error => {
                    errorHtml += '<li>' + error + '</li>';
                });
                errorHtml += '</ul>';

                Swal.fire({
                    icon: 'error',
                    title: 'Validation Error',
                    html: errorHtml,
                    confirmButtonColor: '#3085d6'
                });
                return;
            }

            // Show loading overlay
            loadingOverlay.style.display = 'flex';
            this.submit();
        });
    });
</script>
@endsection
