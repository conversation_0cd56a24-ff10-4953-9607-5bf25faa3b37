
<div>
	<!-- Tab panes -->
	<div class="tab-content">
		<div role="tabpanel" class="{{ $panel_active_us_and_others }} tab-pane donation-content-tabs" id="us">
			<div class="col-lg-6 col-sm-6 no-padding">
				<div class="form-group cust-group">
					<label for="input" class="col-sm-5 control-label"> <span>Is this a one-time or recurring donation?:</span> </label>
					<div class="col-sm-7">
						<!--
                        <select id="combobox">
                            <option value="">Select one...</option>
                            <option value="option2">Recurring</option>
                            <option value="option3">Recurring</option>
                        </select>
                        -->
						@error('donation_type')
							<span class="error">{{ $message }}</span>
						@enderror
						{{ html()->select('donation_type', array('' => 'Select one...', 'charge' => 'One Time', 'subscription' => 'Recurring'))->id('donation_type') }}
					</div>
				</div>
				<div class="form-group cust-group">
					<label for="input" class="col-sm-5 control-label"> <span>Donation frequency: </span> </label>
					<div class="col-sm-7">
						<!--
                        <select id="combobox1">
                            <option value="">Select one...</option>
                            <option value="option1"> Weekly</option>
                            <option value="option2"> Bi-monthly</option>
                            <option value="option1">Monthly</option>
                        </select>
                        -->
						@error('subscription_period')
							<span class="error">{{ $message }}</span>
						@enderror
						{{ html()->select('subscription_period', array('' => 'Select one...', 'weekly' => 'Weekly', 'monthly' => 'Monthly'))->id('subscription_period') }}
					</div>
				</div>
				<!--
	<div class="form-group cust-group">
		<label for="input" class="col-sm-5 control-label"> <span>Donation duration:</span> </label>
		<div class="col-sm-7">
			{{ html()->select('subscription_end', array('' => 'Select one...', 'infinite' => 'Never End', 'finite' => 'End'))->id('combobox2') }}
						</div>
                    </div>
                    -->
				<!--
	<div class="form-group cust-group">
		<label for="input" class="col-sm-5 control-label1"> <span>Date recurring Donation ends:</span></label>
		<div class="col-sm-7">
			@error('subscription_end_date')
				<span class="error">{{ $message }}</span>
			@enderror
						<div class="input-group date" id="startDate">
                            {{ html()->text('subscription_end_date')->class('form-control cust-form') }}
						<span class="input-group-addon addonradious"><i class="glyphicon glyphicon-calendar"></i></span>
                    </div>
                </div>
            </div>
            <div class="form-group cust-group color">
                <label for="input" class="col-sm-5 control-label1"> <span>Date of Donation:</span></label>
                <div class="col-sm-7 datepadd input-group date" id="startDate">
                    @error('donation_date')
						<span class="error">{{ $message }}</span>
					@enderror
				{{ html()->text('donation_date')->class('form-control cust-form') }}
						<span class="input-group-addon addonradious"><i class="glyphicon glyphicon-calendar"></i></span>
                    </div>
                </div>
                -->
				<div class="form-group cust-group">
					<label for="input" class="col-sm-5 control-label1"> <span>Recipient:</span></label>
					<div class="col-sm-7 color">
						Common Change Operations:<br />
						<!-- <input type="text" class="form-control cust-form" id="input" placeholder="$0.00"> -->
						@error('amount_commonchange')
							<span class="error">{{ $message }}</span>
						@enderror
						{{ html()->text('amount_commonchange')->class('form-control cust-form')->placeholder('0.00')->id('amount_commonchange') }}<p></p>
					</div>
					<div class="clearfix"></div>
					<div class="col-sm-7 color col-sm-offset-5">
						My Group:<br />
						<!-- <input type="text" class="form-control cust-form" id="input" placeholder="$0.00"> -->
						@error('amount_group')
							<span class="error">{{ $message }}</span>
						@enderror
						{{ html()->text('amount_group')->class('form-control cust-form')->placeholder('0.00')->id('amount_group') }}<p></p>
						<p>TOTAL: $<span id="total_donation">0.00</span></p>
					</div>
				</div>
				<div class="col-lg-6 col-sm-12 col-md-6 col-lg-offset-5 col-md-offset-5 no-padding input-margin finaces-but">
					@if(isset($wizard))
						<a role="button" class="btn btn-cancel" title="Skip the donation section of the wizard" href="{{ URL::route('wizard.finish',array('donation')) }}">SKIP</a>&nbsp;
					@endif
					<li id="submit-donate" class="btn btn-success big-button">DONATE</button>
				</div>
			</div>
			<div class="col-lg-6 col-sm-6 finaces-padding" >
				<div class="col-lg-12 col-sm-12 finaces-text">
					<h3> HOW CAN I CHANGE RECURRING DONATIONS?</h3>
					<p>If you would like to make a change to your contribution, please contact us at <a href="mailto:<EMAIL>"><EMAIL></a></p>
					<h3>Donations are typically processed within 48-72 hours.</h3>
					<p class="pull-right">
						<img src="/images/stripe_logo.png">
					</p>
				</div>
			</div>
			<div class="clearfix"></div>
		</div>
		<div role="tabpanel" class="{{ $panel_active_sa }} tab-pane donation-content-tabs" id="sa">
			<div class="col-lg-12 col-xs-12">
			<p>
				Common Change South Africa charges a 5% administrative service fee on all group contributions. This service fee covers accounting costs and bank and transfer costs.
			</p>
			<h6>There are two ways to contribute to your Group Fund</h6><br />
			</div>
			<div class="col-lg-6 col-xs-12">
				<div class="panel panel-default">
					<div class="panel-heading"><b>OPTION 1:</b> Set up a direct EFT to the following bank account. Direct EFTs to this account do not incur any additional third party charges. Your bank may charge a third party fee for cash deposits.</div>
					<ul class="list-group">
						<li type="button" class="list-group-item">Bank Name: First National Bank</li>
						<li class="list-group-item">AC #: ***********</li>
						<li class="list-group-item">Type: Cheque</li>
						<li class="list-group-item">Branch Code: 250-655</li>
						<li class="list-group-item">Reference: Please also include your name in the reference and group shortcode to assign your contribution to the correct group fund. E.g "TSmith-GroupShortcode"</li>
					</ul>
				</div>
			</div>
			<div class="col-lg-6 col-xs-12">
				<div class="panel panel-default">
					<div class="panel-heading"><b>OPTION 2:</b> Contribute via Mastercard, Visa or Bitcoin using PayFast.</div>
					<div class="panel-body">
						PayFast, an approved merchant services payment gateway. You may also use PayFast to setup a recurring payment. Members of South African groups who hold bank accounts out-of-country may use PayFast to set up a direct EFT. Note, PayFast charges third party processing fees.<br /> <br />For more info see <a href="https://www.payfast.co.za/fees/" target="_blank">https://www.payfast.co.za/fees/</a> <br />Or contact us on <a href="mailto:<EMAIL>"><EMAIL></a>
					</div>
				</div>
			</div>

			<div class="col-lg-12 col-xs-12">
				<div class="">
					@if(isset($wizard))
						<a role="button" class="btn btn-cancel" title="Skip the donation section of the wizard" href="{{ URL::route('wizard.finish',array('donation')) }}">SKIP</a>&nbsp;
						<a role="button" class="btn btn-success" title="Skip the donation section of the wizard" href="{{ URL::route('wizard.finish',array('donation')) }}">NEXT</a>&nbsp;
					@endif
				</div>
				<br />
			</div>
			<div class="clearfix"></div>


		</div>
		<div role="tabpanel" class="{{ $panel_active_uk }} tab-pane donation-content-tabs" id="uk">
			<div class="col-lg-12 col-xs-12">
			<p>
				To make a contribution to Common Change UK and to make a contribution to a specific group account, you may either fill out the form below, or follow the instructions to create a Standing Order.
			</p>
			</div>
			<div class="col-lg-6 col-xs-12">
				<script src="https://donorbox.org/widget.js" paypalExpress="false"></script><iframe allowpaymentrequest="" frameborder="0" height="900px" name="donorbox" scrolling="no" seamless="seamless" src="https://donorbox.org/embed/common-change-uk?default_interval=m&hide_donation_meter=true" style="max-width: 500px; min-width: 310px; max-height:none!important" width="100%"></iframe>
			</div>
			<div class="col-lg-6 col-xs-12">
				<div class="panel panel-default">
					<div class="panel-heading"><strong>NOTE</strong></div>
					<div class="panel-body">
						<p>If you have any questions or need support, please contact <NAME_EMAIL>.</p>
						<p><strong>Donations are typically processed within 5 business days.</strong></p>
						<p>
							For more info see <a href="https://commonchange.zendesk.com" target="_blank">https://commonchange.zendesk.com</a><br />
							Or contact us on <a href="mailto:<EMAIL>"><EMAIL></a>
						</p>
					</div>
				</div>
			</div>

			<div class="col-lg-12 col-xs-12">
				<div class="">
					@if(isset($wizard))
						<a role="button" class="btn btn-cancel" title="Skip the donation section of the wizard" href="{{ URL::route('wizard.finish',array('donation')) }}">SKIP</a>&nbsp;
						<a role="button" class="btn btn-success" title="Skip the donation section of the wizard" href="{{ URL::route('wizard.finish',array('donation')) }}">NEXT</a>&nbsp;
					@endif
				</div>
				<br />
			</div>
			<div class="clearfix"></div>

		</div>
	</div>
</div>

