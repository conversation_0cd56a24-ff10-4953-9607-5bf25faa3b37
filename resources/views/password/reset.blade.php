@extends('layouts.master')

@section('title')
	Reset password
@stop

@section('home-content')
	<h2>Reset your password</h2>
@stop

@section('content')
<div class="welcome-content">
	@if (Session::has('error'))
	  <span class="error">{{ Session::get('error') }}</span>
	@elseif (Session::has('status'))
	  <san class="success">{{ Session::get('status') }}</span>
	@endif

	{{ html()->form('POST', route('password.reset'))->open() }}
	
 		{{ html()->hidden('token', $token) }}
 		<div class="col-xs-12 form-group cust-group no-padding">
			<div class="col-sm-2">{{ html()->label('Email:', 'email')->class('control-label') }}</div>
			<div class="col-sm-4 no-padding">{{ html()->text('email')->class('form-control cust-form') }}</div>
		</div>
 		<div class="col-xs-12 form-group cust-group no-padding">
			<div class="col-sm-2">{{ html()->label('Password:', 'password')->class('control-label') }}</div>
			<div class="col-sm-4 no-padding">{{ html()->password('password')->class('form-control cust-form') }}</div>
		</div>
 		<div class="col-xs-12 form-group cust-group no-padding">
			<div class="col-sm-2">{{ html()->label('Confirm Password:', 'password_confirmation')->class('control-label') }}</div>
			<div class="col-sm-4 no-padding">{{ html()->password('password_confirmation')->class('form-control cust-form') }}</div>
		</div>

		<div class="col-xs-12 form-group cust-group no-padding">
			<div class="col-sm-offset-2 padding">{{ html()->submit('Submit')->class('btn btn-success') }}</div>
		</div>
		<div class="clearfix"></div>

	{{ html()->form()->close() }}
</div>
@stop
