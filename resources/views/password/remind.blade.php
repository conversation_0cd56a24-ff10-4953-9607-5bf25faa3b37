@extends('layouts.master')

@section('title')
	Send Password Reminder
@stop

@section('home-content')
	<h2>Send Password Reminder</h2>
@stop

@section('content')
<div class="welcome-content">
	@if ($errors->any())
		@error('email')
		  <div class="col-xs-12"><span class="error">{{ $message }}</span></div>
		@enderror

	@elseif (Session::has('status'))
	  <div class="col-xs-12"><span class="success">{{ Session::get('status') }}</span></div>
	@endif

	<div class="col-xs-12"><p>Please enter the email address associated with this account:</p></div>
 
	{{ html()->form('POST', route('password.remind'))->open() }}
 
	  <div class="col-sm-1">{{ html()->label('Email:', 'email')->class('control-label') }}</div>
	  <div class="col-sm-3">{{ html()->text('email')->class('form-control cust-form') }}</div>&nbsp;&nbsp;&nbsp;
	  {{ html()->submit('Submit')->class('btn btn-success') }}
 
	{{ html()->form()->close() }}
</div>
@stop