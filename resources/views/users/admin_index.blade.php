@extends('layouts.master')
@inject('locationHelper', 'App\Models\LocationHelper')

@section('title')
	Admin: Users
@stop

@section('head')
<link type="text/css" rel="stylesheet" href="{{ asset('/css/jqueryui.css?version=1') }}" media="all">
<link type="text/css" rel="stylesheet" href="{{ asset('/css/welcomenav.css?version=2') }}" media="all">
<link type="text/css" rel="stylesheet" href="{{ asset('/css/selectboxit.css?version=1') }}" media="all">
@stop

@section('content')
<section id="main-bg">
	<div class="container  no-padding">
		<div class="col-lg-12 col-sm-12 col-xs-12 no-padding margin-padding ">
		<div class="col-xs-12">
			@include('admin.bar',array('current_page'=>'users'))
		</div>
		<div class="col-sm-2 col-xs-12 allgroup-contain "> <span>USERS</span> </div>
		<div class="col-sm-8 col-xs-12 no-padding new-radius centerposition " style="text-align:right;">
		{{ html()->modelForm($input, 'GET', route('user.admin_index'))->class('form-inline margin-mobile')->attribute('name', 'user')->open() }}
			<div class="form-group">
				{{ html()->select('group', array('0' => '-- All Groups --') + App\Models\Group::withTrashed()->orderBy('name')->pluck('name', 'id')->toArray())->class('form-control')->attribute('onchange', 'document.user.submit()') }}
			</div>
			<div class="form-group">
				{{ html()->select('sort', array('default' => 'Default Sort', 'joined' => 'Date Joined', 'group' => 'Group'))->class('form-control')->attribute('onchange', 'document.user.submit()') }}
			</div>
			<div class="form-group">
				<div class="input-group">
				  <!-- <input type="text" class="form-control cust-form1" placeholder="" name=""> -->
				  {{ html()->text('search')->class('form-control')->placeholder('Search Name/Email') }}
				  <div class="input-group-btn">
					<button class="btn btn-success" type="submit"><i class="glyphicon glyphicon-search"></i></button>
				  </div>
				</div>
			</div>
			<div class="form-group">
				<input class="btn btn-success" type="submit" name="output_csv" value="Get CSV">
			</div>
		{{ html()->closeModelForm() }}
		</div>
		</div>
	</div>

	<div class="container welcome-content no-padding">
		<div class="col-xs-12 clearfix">{{ $users->links() }}</div>
		<div class="col-xs-12 discuss-container no-padding">
		<!--main contain part -->
		<div class="col-lg-12 col-sm-12 mobilescroll col-xs-12 no-padding">
			<table class="table table-responsive table-condensed">
				<thead class="tdclass">
					<tr >
						<th>NAME</th>
						<th>EMAIL</th>
						<th>DEFAULT GROUP</th>
						<th>JOINED</th>
						<th>LAST ACTIVITY</th>
						<th>STATE</th>
						<th title="Country">CTRY</th>
						<!-- <th>STATUS</th> -->
						@if(Auth::User()->can_impersonate)
							<th title="impersonate"></th>
						@endif
						<th title="mobile app"></th>
						<th title="suspended"></th>
						<th title="reset link"></th>
					</tr>
				</thead>
				<tbody>

				@forelse($users as $user)
					@if($user->trashed())
					<tr class="deleted">
						<td><a href="{{ URL::route('user.show',array('user'=>$user->id)) }}" target="cc_issue_admin">{{ $user->firstname }} {{ $user->lastname }}</a></td>
						<td><a href="mailto:{{ $user->email }}">{{ $user->email }}</a></td>
						<td>
							@if(isset($user->default_group))
							{{ $user->default_group->name }}
							@endif
						</td>
						<td>{{ $user->created_at->toDateString() }}</td>
						<td>{{ $user->updated_at->toDateString() }}</td>
						<td><span title="{{ $user->address ? $locationHelper->subdivisionName($user->address->state) : '' }}">{{ $user->address ? $user->address->state : '' }}</span></td>
						<td><span title="{{ $user->address ? $locationHelper->countryName($user->address->country) : '' }}">{{ $user->address ? $user->address->country : '' }}</span></td>
						<!-- <td>{{ $user->trashed() ? 'disabled' : 'normal' }}</td> -->
						<td></td>
						<td></td>
						<td></td>
					</tr>
					@else
					<tr>
						<td><a href="{{ URL::route('user.show',array('user'=>$user->id)) }}" target="cc_issue_admin">{{ $user->firstname }} {{ $user->lastname }}</a></td>
						<td><a href="mailto:{{ $user->email }}">{{ $user->email }}</a></td>
						<td>
							@if(isset($user->default_group))
							{{ $user->default_group->name }}
							@endif
						</td>
						<td>{{ $user->created_at->toDateString() }}</td>
						<td>{{ $user->updated_at->toDateString() }}</td>
						<td><span title="{{ $user->address ? $locationHelper->subdivisionName($user->address->state) : '' }}">{{ $user->address && $user->address->state != 'zzzz' ? $user->address->state : '' }}</span></td>
						<td><span title="{{ $user->address ? $locationHelper->countryName($user->address->country) : '' }}">{{ $user->address ? $user->address->country : '' }}</span></td>
						<!-- <td>{{ $user->trashed() ? 'disabled' : 'normal' }}</td> -->
						@if(Auth::User()->can_impersonate)
						<td>
							@if($user->email_valid)
								<a href="{{ URL::route('user.impersonate',array('id'=>$user->id)) }}" title="Impersonate user"><i class="glyphicon glyphicon-user"></i></a>
							@else
								<a href="{{ URL::route('user.validate_email',array('id'=>$user->id)) }}" class="validate_email" title="Manually validate Email">
									<span style="color:red"><i class="glyphicon glyphicon-envelope"></i></span></a>
							@endif
						</td>
						@endif
						<td>
							<a href="{{ URL::route('user.toggle_mobile',array('id'=>$user->id)) }}" title="Toggle mobile app">
								@if($user->can_use_mobile_app)
									<span style="color:green"  title="Disallow mobile"><i class="glyphicon glyphicon-phone"></i></span>
								@else
									<span style="color:red" title="Activate mobile"><i class="glyphicon glyphicon-phone"></i></span>
								@endif
							</a>
						</td>
						<td>
							<a href="{{ URL::route('user.toggle_suspended',array('id'=>$user->id)) }}" title="Toggle suspended status">
								@if($user->suspended)
									<span style="color:red" title="Reactivate user"><i class="glyphicon glyphicon-minus-sign"></i></span>
								@else
									<span style="color:green"  title="Suspend user"><i class="glyphicon glyphicon-ok-sign"></i></span>
								@endif
							</a>
						</td>
						<td>
							<a href="{{ URL::route('user.reset_link',array('id'=>$user->id)) }}" title="Password Reset Email text">
								<i class="glyphicon glyphicon-qrcode"></i>
							</a>
						</td>
					</tr>
					@endif
				@empty
					<tr><td colspan="6">No matching users.</td></tr>
				@endforelse

				</tbody>
			</table>
			</container>
			<div class="clearfix"></div>
		</div>
		</div>
	</div>
</section>
@stop

@section('scripts')
<script src="{{ asset('/js/bootstrap-toggle.js') }}"></script>
<script src="{{ asset('/js/jquery-ui.js') }}"></script>
<script src="{{ asset('/js/selectBoxIt.js') }}"></script>
<script>
	$(document).ready(function() {
		$('.validate_email').click(function(event) {
		    event.preventDefault();
		    var r=confirm("Validate this user's email?");
		    if (r==true)   {
		       window.location = $(this).attr('href');
		    }
		});
	});
</script>
<script>
/*
	$("select").selectBoxIt({
		autoWidth: false,
	});
*/
</script>
@stop