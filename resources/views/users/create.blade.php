@extends('layouts.master')

@section('title')
	Register Account
@stop

@section('head')
	<style>
		.label {
			text-align: right;
		}
	</style>
@stop

@section('home-content')
	<span></span>
@stop

@section('content')
	<h1>Create an account!</h1>

	{{ html()->form()->open() }}
		<table>
		<tr>
			<td class=label>{{ html()->label('First Name: ', 'firstname') }}</td>
			<td>{{ html()->text('firstname') }}</td>
			<td>@error('firstname')
					<span class="error">{{ $message }}</span>
				@enderror</td>
		</tr>
		<tr>
			<td class=label>{{ html()->label('Last Name: ', 'lastname') }}</td>
			<td>{{ html()->text('lastname') }}</td>
			<td>@error('lastname')
					<span class="error">{{ $message }}</span>
				@enderror</td>
		</tr>
		<tr>
			<td class=label>{{ html()->label('Email Address: ', 'email') }}</td>
			<td>{{ html()->email('email') }}</td>
			<td>@error('email')
					<span class="error">{{ $message }}</span>
				@enderror</td>
		</tr>
		<tr>
			<td class=label>{{ html()->label('Password: ', 'password') }}</td>
			<td>{{ html()->password('password') }}</td>
			<td>@error('password')
					<span class="error">{{ $message }}</span>
				@enderror</td>
		</tr>
		<tr>
			<td class=label>{{ html()->label('Confirm Password: ', 'password_confirmation') }}</td>
			<td>{{ html()->password('password_confirmation') }}</td>
			<td>@error('password_confirmation')
					<span class="error">{{ $message }}</span>
				@enderror</td>
		</tr>

		<tr><td></td><td>{{ html()->submit('Register') }}</td></tr>
		</table>
	{{ html()->form()->close() }}


@stop