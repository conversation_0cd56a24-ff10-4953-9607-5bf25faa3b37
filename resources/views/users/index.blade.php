@extends('layouts.no_container')
@inject('locationHelper', 'App\Models\LocationHelper')

@section('title')
	All Users
@stop

@section('head')
<link type="text/css" rel="stylesheet" href="{{ asset('/css/jqueryui.css?version=1') }}" media="all">
<link type="text/css" rel="stylesheet" href="{{ asset('/css/welcomenav.css?version=2') }}" media="all">
<link type="text/css" rel="stylesheet" href="{{ asset('/css/selectboxit.css?version=1') }}" media="all">
@stop

@section('content')
<section id="main-bg" class="bg2-pedding">
	<div class="container  no-padding">
		<div class="col-lg-12 col-sm-12 col-xs-12 no-padding margin-padding ">
			<div class="col-lg-8 col-sm-6 col-xs-12 allgroup-contain "> <span>ALL USERS</span> </div>
			<div class="col-lg-4 col-sm-6 col-xs-12 no-padding new-radius ">
			{{ html()->modelForm($input, 'GET', route('user.index'))->class('form-horizontal margin-mobile')->attribute('name', 'searchform')->open() }}
				<div class="col-lg-6 col-sm-6 col-xs-12 no-padding  pull-left">
				  <!-- <div class="ui-widget"> -->
					<!-- <select id="combobox">
						<option value="">Select one...</option>
						<option value="option1"> Default</option>
						<option value="option2">Alphabetical (A-Z)</option>
						<option value="option3">Alphabetical (Z-A)</option>
						<option value="option4"> By State</option>
					</select> -->
					{{ html()->select('sort', array('alpha' => 'Alphabetical (A-Z)', 'revalpha' => 'Alphabetical (Z-A)', 'state' => 'State', 'country' => 'Country'))->attribute('onchange', 'document.searchform.submit()') }}

				  <!-- </div> -->
				</div>
				<div class="col-lg-6 col-sm-6 col-xs-12 pull-right">
					<!-- search but strat -->
					<!-- <form class="navbar-form no-margin  pull-right" role="search"> -->
						<div class="input-group">
							<!-- <input type="text" class="form-control cust-form1" placeholder="" name=""> -->
							{{ html()->text('search')->class('form-control cust-form1')->placeholder('Search') }}
							<div class="input-group-btn">
								<button class="btn btn-success btn-success3 " type="submit"><i class="glyphicon glyphicon-search"></i></button>
							</div>
						</div>
					<!-- </form> -->
					<!-- end of search but -->
				</div>
			{{ html()->closeModelForm() }}
			</div>
		</div>
	</div>
	</div>
	<div class="container no-padding">
	{{ $pUsers->links() }}
	@forelse ($users as $triple)
		<div class="col-lg-12 col-sm-12 col-md-12 group-description signup no-padding people-margindbottom clearfix">
		@foreach ($triple as $user)
			<div class="col-lg-4 col-sm-4 col-md-4 {{ $user['triple_class'] }}">
				<div class="shared-people welcome-content clearfix">
					<h2>{{ $user->firstname }} {{ $user->lastname }}</h2>
					<div class="col-lg-12 col-md-12  col-sm-12  group-text group-margin1  no-padding">
						<div class="col-lg-3  col-sm-3 no-padding round"><span class="largepix" style="background-image: url('{{ $user->profile_pic }}')"></span></div>
						<div class="col-lg-9  col-sm-9 no-padding extrapadding5">
							<p><span>Location: </span>
								@if (isset($user->address))
									{{ $user->address->city }},
									@if($user->address->state != 'zzzz')
										{{ $locationHelper->subdivisionName($user->address->state) }},
									@endif
									{{ $locationHelper->countryName($user->address->country) }}
								@else
									Unknown
								@endif
							<br />
							<span>Member since:</span> {{ $user->created_at->toFormattedDateString() }}<br />
							<span>Group:</span>
								@if ($user->groups->count())
									<a href="{{ URL::route('group.show',$user->groups->first()->id) }}"> {{ $user->default_group()->first()->name }}</a>
								@else
									No Group
								@endif
							</p>

							<!--social part strat -->
							<div class=" social ">
								<div class="cust-dropdown">
									@if(!empty($user->facebook))
										<a href="https://facebook.com/{{ urlencode($user->facebook) }}" title="Connect with {{ $user->firstname}} on Facebook" target="_blank"><img src="/images/fb.png"></a>
									@else
										<img src="/images/fbnocolor.png" title="User has not shared their Facebook account.">
									@endif
								</div>
								<div class="cust-dropdown">
									@if(!empty($user->twitter))
										<a href="https://twitter.com/{{ urlencode($user->twitter) }}" title="Connect with {{ $user->firstname}} on Twitter" target="_blank"><img src="/images/twitter.png"></a>
									@else
										<img src="/images/twitternocolor.png" title="User has not shared their Twitter account.">
									@endif
								</div>
								<div class="cust-dropdown">
									@if($user->share_email)
										<a href="mailto:{{ $user->email }}" title="Send {{ $user->firstname}} an email"><img src="/images/email.png"></a>
									@else
										<img src="/images/emailnocolor.png" title="User has not shared their email address.">
									@endif
								</div>
							</div>
							<!--end of social part -->
						</div>
					</div>
				</div>
			</div>
		@endforeach
		</div>
	@empty
		<p>No groups to display.</p>
	@endforelse
	</div>
</section>
@stop

@section('scripts')
	<script src="{{ asset('/js/bootstrap-toggle.js') }}"></script>
	<script src="{{ asset('/js/jquery-ui.js') }}"></script>
	<script>
	$('#myDropdown .dropdown-menu').on({
		"click":function(e){
			e.stopPropagation();
		}
	});
	</script>
<script src="{{ asset('/js/selectBoxIt.js') }}"></script>
<script>
	console.log('did it');
	$("select").selectBoxIt({
		autoWidth: false
	});
</script>
@stop