@extends('layouts.dashboard-new')
@inject('navBar', 'App\Models\NavBar')

@section('title', 'Home')

@push('styles')
<link type="text/css" rel="stylesheet" href="{{ asset('/css/selectboxit.css?version=1') }}" media="all">
<style>
    .content-body .container {
        margin: 0 20px;
        padding: 20px 0;
        max-width: 1200px;
        background: transparent;
    }
    
    .home-content {
        background: white;
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.04);
        padding: 24px;
        margin-bottom: 24px;
    }
    
    .group-content {
        background: white;
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.04);
        padding: 24px;
    }
    
    .group-content cite {
        font-size: 1.5rem;
        font-weight: 600;
        color: #333;
        font-style: normal;
        display: block;
        margin-bottom: 16px;
    }
    
    .group-content .font3 {
        font-weight: 600;
        color: #555;
        margin-right: 8px;
    }
    
    .group-content p {
        margin-bottom: 12px;
        color: #666;
    }
    
    .group-content h2 {
        color: #333;
        font-size: 1.25rem;
        font-weight: 600;
        margin: 24px 0 16px 0;
    }
    
    .table {
        background: white;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }
    
    .table thead th {
        background: #f8f9fa;
        color: #333;
        font-weight: 600;
        border: none;
        padding: 12px 16px;
    }
    
    .table tbody td {
        border: none;
        padding: 12px 16px;
        border-bottom: 1px solid #f0f0f0;
    }
    
    .table tbody tr:last-child td {
        border-bottom: none;
    }
    
    .tinypix {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background-size: cover;
        background-position: center;
        display: inline-block;
        background-color: #e0e7ff;
    }
    
    .btn {
        padding: 8px 16px;
        border-radius: 6px;
        font-weight: 500;
        text-decoration: none;
        display: inline-block;
        transition: all 0.2s;
    }
    
    .btn-success {
        background: #10b981;
        color: white;
        border: 1px solid #10b981;
    }
    
    .btn-success:hover {
        background: #059669;
        border-color: #059669;
    }
    
    .small-button {
        font-size: 14px;
        padding: 6px 12px;
    }
    
    .form-control {
        border: 1px solid #e5e7eb;
        border-radius: 6px;
        padding: 8px 12px;
        background: #fafbfc;
    }
    
    .input-group-btn .btn {
        border-radius: 0 6px 6px 0;
    }
    
    .pagination {
        margin: 20px 0;
    }
    
    .alert {
        border-radius: 8px;
        padding: 16px 20px;
        margin-bottom: 20px;
    }
</style>
@endpush

@section('content')
<div class="container">
    @if (isset($motd))
    <div class="home-content">
        <blockquote style="margin: 0; padding: 0; border: none; font-style: italic; color: #666;">
            <p style="margin: 0;">{{ $motd }}</p>
        </blockquote>
    </div>
    @endif

    @if (isset($group))
        @include('groups.mygroup_details')
    @endif
</div>
@endsection

@push('scripts')
<script src="{{ asset('/js/jquery-ui.js') }}"></script>
<script src="{{ asset('/js/selectBoxIt.js') }}"></script>
<script>
    console.log('Home page loaded with v2 layout');
    $("select").selectBoxIt({
        autoWidth: false
    });
</script>
@endpush