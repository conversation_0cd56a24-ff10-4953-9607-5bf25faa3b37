{{ html()->form('POST', url('login'))->class('form-horizontal cust-control')->attribute('name', 'loginform')->open() }}
    @error('invalid')
        <span class="error">{{ $message }}</span>
    @enderror
    <div class="form-group cust-group">
        <label for="login_email" class="col-sm-4 control-label"> <span>Email Address:</span></label>
        <div class="col-sm-7 ">
            {{ html()->email('email', $email)->class('form-control cust-form')->id('login_email')->attribute('placeholder', 'Email Address') }}
        </div>
    </div>
    <div class="form-group cust-group">
        <label for="login_pw" class="col-sm-4 control-label"> <span>Password:</span></label>
        <div class="col-sm-7 ">
            {{ html()->password('password')->class('form-control cust-form')->id('login_pw')->attribute('placeholder', 'Password') }}
        </div>
    </div>
    <div class="checkbox readme font">
        <label>
            <input type="checkbox" name="remember"> Remember Me
        </label>
    </div>
    <!--log in button -->
    <div class="row">
        <div class="col-sm-4 col-xs-3">
            <!-- <a title="Log In" class="btn btn-success big-button" role="button" onclick="document.loginform.submit();">Log In</a> -->
            <button title="Log In" class="btn btn-success big-button">Log In</button>
        </div>
        <div class="col-xs-7 sign-text"> <span> <a href="{{ URL::to('password/remind') }}"><strong>Forgot your password?</strong></a></span> </div>
    </div>
<!-- </form> -->
{{ html()->form()->close() }}
