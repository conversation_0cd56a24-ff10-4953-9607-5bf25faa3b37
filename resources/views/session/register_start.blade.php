@extends('layouts.modern')

@section('title', 'Start Registration')

@section('content')
<!-- Signup Page -->
<div class="signup-page">
    <div class="signup-image" style="background-image: url('/images/landing-bg-1.png');"></div>
    <div class="signup-section">
        <div class="signup-logo">
            <img src="/images/logo.png" alt="Common Change Logo">
        </div>

        <h1>Create Your Account</h1>
        <p class="signup-instructions">
            Your email <span class="email-highlight">{{ $email }}</span> has been verified.
            <br>Now let's set up your account.
        </p>

        <form id="registration-form" method="POST" action="{{ route('register.complete') }}" novalidate>
            @csrf
            <input type="hidden" name="email" value="{{ $email }}">

            <input type="text" id="firstname" name="firstname" placeholder="First Name" required value="{{ old('firstname') }}">

            <input type="text" id="lastname" name="lastname" placeholder="Last Name" required value="{{ old('lastname') }}">

            <input type="text" id="addr1" name="address[addr1]" placeholder="Address Line 1" required value="{{ old('address.addr1') }}">

            <input type="text" id="addr2" name="address[addr2]" placeholder="Address Line 2" value="{{ old('address.addr2') }}">

            <input type="text" id="city" name="address[city]" placeholder="City" required value="{{ old('address.city') }}">

            <input type="text" id="state" name="address[state]" placeholder="State/Province" required value="{{ old('address.state') }}">

            <input type="text" id="postcode" name="address[postcode]" placeholder="Postal Code" required value="{{ old('address.postcode') }}">

            <select id="country" name="address[country]" required>
                <option value="">Select Country</option>
                <option value="US" {{ old('address.country') == 'US' ? 'selected' : '' }}>United States</option>
                <option value="CA" {{ old('address.country') == 'CA' ? 'selected' : '' }}>Canada</option>
                <option value="GB" {{ old('address.country') == 'GB' ? 'selected' : '' }}>United Kingdom</option>
                <option value="AU" {{ old('address.country') == 'AU' ? 'selected' : '' }}>Australia</option>
                <option value="ZA" {{ old('address.country') == 'ZA' ? 'selected' : '' }}>South Africa</option>
                <option value="NZ" {{ old('address.country') == 'NZ' ? 'selected' : '' }}>New Zealand</option>
                <option value="DE" {{ old('address.country') == 'DE' ? 'selected' : '' }}>Germany</option>
                <option value="FR" {{ old('address.country') == 'FR' ? 'selected' : '' }}>France</option>
                <option value="ES" {{ old('address.country') == 'ES' ? 'selected' : '' }}>Spain</option>
                <option value="IT" {{ old('address.country') == 'IT' ? 'selected' : '' }}>Italy</option>
                <option value="JP" {{ old('address.country') == 'JP' ? 'selected' : '' }}>Japan</option>
                <option value="CN" {{ old('address.country') == 'CN' ? 'selected' : '' }}>China</option>
                <option value="IN" {{ old('address.country') == 'IN' ? 'selected' : '' }}>India</option>
                <option value="BR" {{ old('address.country') == 'BR' ? 'selected' : '' }}>Brazil</option>
                <option value="MX" {{ old('address.country') == 'MX' ? 'selected' : '' }}>Mexico</option>
            </select>

            @if(!Session::has('verified_password'))
            <input type="password" id="password" name="password" placeholder="Password" required>

            <input type="password" id="password_confirmation" name="password_confirmation" placeholder="Confirm Password" required>
            @endif

            <div class="checkbox-group">
                <input type="checkbox" id="share_email" name="share_email" {{ old('share_email') ? 'checked' : '' }}>
                <label for="share_email">Share my email with other group members</label>
            </div>

            <div class="checkbox-group">
                <input type="checkbox" id="terms" name="terms" required {{ old('terms') ? 'checked' : '' }}>
                <label for="terms">I agree to the <a href="{{ url('/terms-of-service') }}" target="_blank">Terms of Service</a></label>
            </div>

            <button type="submit" class="btn-primary">Create Account</button>
        </form>

        <div id="loading" style="display: none;">
            <div class="spinner"></div>
            <p id="loading-text">Creating your account...</p>
        </div>

        <div class="signup-footer">
            Already have an account? <a href="{{ route('auth.login') }}">Login</a>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const form = document.getElementById('registration-form');
        const loading = document.getElementById('loading');

        // Display any server-side validation errors using SweetAlert
        @if($errors->any())
            const errorMessages = [];
            @foreach($errors->all() as $error)
                errorMessages.push("{{ $error }}");
            @endforeach

            let errorHtml = '<ul style="text-align: left; margin-top: 0;">';
            errorMessages.forEach(error => {
                errorHtml += '<li>' + error + '</li>';
            });
            errorHtml += '</ul>';

            Swal.fire({
                icon: 'error',
                title: 'Validation Error',
                html: errorHtml,
                confirmButtonColor: '#3085d6'
            });
        @endif

        form.addEventListener('submit', function(e) {
            e.preventDefault();

            // Client-side validation
            const firstname = document.getElementById('firstname').value.trim();
            const lastname = document.getElementById('lastname').value.trim();
            const addr1 = document.getElementById('addr1').value.trim();
            const city = document.getElementById('city').value.trim();
            const state = document.getElementById('state').value.trim();
            const postcode = document.getElementById('postcode').value.trim();
            const country = document.getElementById('country').value;
            const terms = document.getElementById('terms').checked;

            let errors = [];

            if (!firstname) {
                errors.push('First name is required');
            }

            if (!lastname) {
                errors.push('Last name is required');
            }

            if (!addr1) {
                errors.push('Address is required');
            }

            if (!city) {
                errors.push('City is required');
            }

            if (!state) {
                errors.push('State/Province is required');
            }

            if (!postcode) {
                errors.push('Postal code is required');
            } else if (!/^[a-zA-Z0-9\-\s]+$/.test(postcode)) {
                errors.push('Postal code can only contain letters, numbers, spaces, and hyphens');
            }

            if (!country) {
                errors.push('Country is required');
            }

            // Only validate password if the password fields are present
            @if(!Session::has('verified_password'))
            const password = document.getElementById('password').value;
            const passwordConfirmation = document.getElementById('password_confirmation').value;

            if (!password) {
                errors.push('Password is required');
            } else if (password.length < 8) {
                errors.push('Password must be at least 8 characters');
            }

            if (password !== passwordConfirmation) {
                errors.push('Passwords do not match');
            }
            @endif

            if (!terms) {
                errors.push('You must agree to the Terms of Service');
            }

            if (errors.length > 0) {
                let errorHtml = '<ul style="text-align: left; margin-top: 0;">';
                errors.forEach(error => {
                    errorHtml += '<li>' + error + '</li>';
                });
                errorHtml += '</ul>';

                Swal.fire({
                    icon: 'error',
                    title: 'Validation Error',
                    html: errorHtml,
                    confirmButtonColor: '#3085d6'
                });
                return;
            }

            // Show loading spinner with SweetAlert but keep form visible
            Swal.fire({
                title: 'Creating Account',
                text: 'Please wait while we set up your account...',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            // Submit the form without hiding it
            this.submit();
        });
    });
</script>
@endsection
