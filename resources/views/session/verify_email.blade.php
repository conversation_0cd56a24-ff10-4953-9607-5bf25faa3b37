@extends('layouts.modern')

@section('title', 'Verify Email')

@section('content')
<!-- Signup Page -->
<div class="signup-page">
    <div class="signup-image" style="background-image: url('/images/landing-bg-1.png');"></div>
    <div class="signup-section">
        <div class="signup-logo">
            <img src="/images/logo.png" alt="Common Change Logo">
        </div>
        
        <h1>Check your email for a code.</h1>
        
        <div style="width: 60%; text-align: left;">
            <p class="instruction-text">
                We've sent a 6-character code to <span class="email-highlight">{{ $email }}</span>.
            </p>
            <p class="expiry-note">
                The code expires shortly, so enter it soon.
            </p>
            
            <p class="didnt-receive">
                Didn't receive the email? <a href="#" id="resend-link" class="try-again">Try again</a>
            </p>
        </div>
        
        <form id="verification-form" method="POST" action="{{ route('email.verify') }}">
            @csrf
            <div class="otp-container">
                <input type="text" maxlength="1" class="otp-input" name="code_1" id="code_1" autofocus>
                <input type="text" maxlength="1" class="otp-input" name="code_2" id="code_2">
                <input type="text" maxlength="1" class="otp-input" name="code_3" id="code_3">
                <input type="text" maxlength="1" class="otp-input" name="code_4" id="code_4">
                <input type="text" maxlength="1" class="otp-input" name="code_5" id="code_5">
                <input type="text" maxlength="1" class="otp-input" name="code_6" id="code_6">
            </div>
            
            <button type="submit" class="btn-primary">Next</button>
        </form>
        
        <div id="loading" style="display: none;">
            <div class="spinner"></div>
            <p id="loading-text">Verifying code...</p>
        </div>
        
        <div class="signup-footer">
            Already have an account? <a href="{{ route('auth.login') }}">Login</a>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Auto-tab between OTP input fields
        const otpInputs = document.querySelectorAll('.otp-input');

        otpInputs.forEach((input, index) => {
            input.addEventListener('input', function(e) {
                // Only proceed if the input is a number
                if (!/^\d*$/.test(this.value)) {
                    this.value = this.value.replace(/[^\d]/g, '');
                    return;
                }
                
                if (this.value.length === 1 && index < otpInputs.length - 1) {
                    otpInputs[index + 1].focus();
                }
                
                // If all inputs are filled, submit the form
                if (index === otpInputs.length - 1 && this.value.length === 1) {
                    const allFilled = Array.from(otpInputs).every(input => input.value.length === 1);
                    if (allFilled) {
                        document.getElementById('verification-form').dispatchEvent(new Event('submit'));
                    }
                }
            });
            
            input.addEventListener('keydown', function(e) {
                if (e.key === 'Backspace' && !this.value && index > 0) {
                    otpInputs[index - 1].focus();
                }
            });
            
            // Only allow numbers
            input.addEventListener('keypress', function(e) {
                if (!/[0-9]/.test(e.key)) {
                    e.preventDefault();
                }
            });
            
            // Handle paste event to distribute digits across inputs
            input.addEventListener('paste', function(e) {
                e.preventDefault();
                const pastedData = (e.clipboardData || window.clipboardData).getData('text');
                const digits = pastedData.replace(/\D/g, '').split('');
                
                // Fill current and subsequent inputs
                for (let i = 0; i < digits.length && i + index < otpInputs.length; i++) {
                    otpInputs[i + index].value = digits[i];
                }
                
                // Focus on the next empty input or the last one
                const nextEmptyIndex = Array.from(otpInputs).findIndex((input, idx) => idx >= index && !input.value);
                if (nextEmptyIndex !== -1 && nextEmptyIndex < otpInputs.length) {
                    otpInputs[nextEmptyIndex].focus();
                } else {
                    otpInputs[otpInputs.length - 1].focus();
                }
            });
        });
        
        // Form submission
        const form = document.getElementById('verification-form');
        const loading = document.getElementById('loading');

        // Remove any existing error messages when starting fresh
        function clearErrors() {
            const existingErrors = document.querySelectorAll('.error-message');
            existingErrors.forEach(error => error.remove());
        }

        // Display error message using standard method instead of Notiflix
        function showError(message) {
            clearErrors(); // Remove any existing error messages
            
            // Create error element
            const errorElement = document.createElement('div');
            errorElement.className = 'error-message';
            errorElement.textContent = message || 'Verification failed. Please try again.';
            
            // Insert before the form
            form.parentNode.insertBefore(errorElement, form);
            
            // Log for debugging
            console.log('Error displayed:', message);
        }

        form.addEventListener('submit', function(e) {
            e.preventDefault();
            clearErrors();
            
            // Show loading
            form.style.display = 'none';
            loading.style.display = 'flex';
            
            // Get the OTP code from individual inputs
            const formData = new FormData();
            formData.append('code_1', document.getElementById('code_1').value);
            formData.append('code_2', document.getElementById('code_2').value);
            formData.append('code_3', document.getElementById('code_3').value);
            formData.append('code_4', document.getElementById('code_4').value);
            formData.append('code_5', document.getElementById('code_5').value);
            formData.append('code_6', document.getElementById('code_6').value);
            formData.append('_token', document.querySelector('input[name="_token"]').value);
            
            // Send the verification request
            fetch('{{ route("email.verify") }}', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('input[name="_token"]').value,
                    'Accept': 'application/json'
                },
                body: formData
            })
            .then(response => {
                console.log('Response status:', response.status);
                return response.json().then(data => {
                    console.log('Response data:', data);
                    if (!response.ok) {
                        throw new Error(data.message || 'Verification failed. Please try again.');
                    }
                    return data;
                });
            })
            .then(data => {
                if (data.success) {
                    // Smooth transition to next page
                    loading.querySelector('#loading-text').textContent = 'Success! Redirecting...';
                    setTimeout(() => {
                        window.location.href = data.redirect;
                    }, 500);
                } else {
                    form.style.display = 'block';
                    loading.style.display = 'none';
                    
                    // Show error message smoothly
                    showError(data.message || 'Verification failed. Please try again.');
                    
                    // Clear inputs with a slight delay for better UX
                    setTimeout(() => {
                        otpInputs.forEach(input => input.value = '');
                        otpInputs[0].focus();
                    }, 100);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                form.style.display = 'block';
                loading.style.display = 'none';
                
                // Show error message smoothly
                showError(error.message || 'An error occurred. Please try again.');
            });
        });
        
        // Resend verification code
        const resendLink = document.getElementById('resend-link');

        resendLink.addEventListener('click', function(e) {
            e.preventDefault();
            
            resendLink.textContent = 'Sending...';
            
            fetch('{{ route("email.resend") }}', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('input[name="_token"]').value,
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                },
                body: JSON.stringify({})
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.json();
            })
            .then(data => {
                console.log('Resend response:', data);
                if (data.success) {
                    resendLink.textContent = 'Code sent!';
                    setTimeout(() => {
                        resendLink.textContent = 'Try again';
                    }, 3000);
                } else {
                    resendLink.textContent = 'Failed to send';
                    setTimeout(() => {
                        resendLink.textContent = 'Try again';
                    }, 3000);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                resendLink.textContent = 'Failed to send';
                setTimeout(() => {
                    resendLink.textContent = 'Try again';
                }, 3000);
            });
        });
    });
</script>
@endsection
