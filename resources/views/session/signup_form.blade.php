{{ html()->form('POST', url('register'))->class('form-horizontal cust-control')->attribute('name', 'signup')->open() }}
<div class="form-group cust-group">
    <label for="input" class="col-sm-4 control-label"> <span>First Name:</span></label>
    <div class="col-sm-7 ">
        <input type='hidden' name='recaptcha_token' id='recaptcha_token'>
        @if($errors->has('recaptcha_token'))
            <span class="error">{{$errors->first('recaptcha_token')}}</span>
        @endif

        @error('firstname')
            <span class="error">{{ $message }}</span>
        @enderror
        {{ html()->text('firstname')->class('form-control cust-form')->id('input')->placeholder('First name') }}
    </div>
</div>
<div class="form-group cust-group">
    <label for="input" class="col-sm-4 control-label"> <span>Last Name:</span></label>
    <div class="col-sm-7">
        <!-- <input type="text" class="form-control cust-form" id="input" placeholder="Last name"> -->
        @error('lastname')
            <span class="error">{{ $message }}</span>
        @enderror
        {{ html()->text('lastname')->class('form-control cust-form')->id('input')->placeholder('Last name') }}
    </div>
</div>
<div class="form-group cust-group">
    <label for="input" class="col-sm-4 control-label"> <span>Email Address:</span></label>
    <div class="col-sm-7">
        <!-- <input type="email" class="form-control cust-form" id="input" placeholder="Email Address"> -->
        @error('email')
            <span class="error">{{ $message }}</span>
        @enderror
        {{ html()->email('email', $email)->class('form-control cust-form')->id('input')->attribute('placeholder', 'Email Address') }}
    </div>
</div>
<div class="form-group cust-group">
    <label for="input" class="col-sm-4 control-label"> <span>Password:</span></label>
    <div class="col-sm-7">
        <!-- <input type="password" class="form-control cust-form" id="input" placeholder="Password"> -->
        @error('password')
            <span class="error">{{ $message }}</span>
        @enderror
        {{ html()->password('password')->class('form-control cust-form')->id('input')->attribute('placeholder', 'Password') }}
    </div>
</div>
<div class="form-group cust-group">
    <label for="input" class="col-sm-4 control-label"> <span>Confirm Password:</span></label>
    <div class="col-sm-7">
        <!-- <input type="password" class="form-control cust-form" id="input" placeholder="Password"> -->
        @error('password_confirmation')
            <span class="error">{{ $message }}</span>
        @enderror
        {{ html()->password('password_confirmation')->class('form-control cust-form')->id('input')->attribute('placeholder', 'Password') }}
    </div>
</div>
@if(!empty($_ENV['RECAPTCHA_SECRET_KEY']))
<div class="col-xs-12 no-padding">
    <label style="width:100%;" for="recaptcha">Please check the box to confirm you're human:</label>
    @error('g-recaptcha-response')
        <span class="error">{{ $message }}</span>
    @enderror
    {{ html()->captcha() }}
</div>
@endif
<div class="col-xs-12 sign-text no-padding"> <span>By signing up, I agree with the <strong><a href="https://www.commonchange.com/terms-of-service/" target="_blank">Terms of Service</a></strong></span> </div>
<!--sing up part button  -->
<div class="col-lg-3 col-md-3 col-xs-12 no-padding">
    <!-- <a title="Submit a need Request" class="btn btn-success big-button" role="button" onclick="document.signup.submit();">Sign Up</a> -->
    <button title="Sign Up" class="btn btn-success big-button">Join</button>
</div>
<!-- </form> -->
{{ html()->form()->close() }}
