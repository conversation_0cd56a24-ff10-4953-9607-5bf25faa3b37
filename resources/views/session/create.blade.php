@extends('layouts.master')

@section('title')
	Common Change
@stop

@section('head')
	<style>
		.label {
			text-align: right;
		}
	</style>
@stop

@section('home-content')
	<span></span>
	<!--
	<blockquote>
		<p>
			Download the best resource to help get you up-to-speed on all of our features, options,<br />
			tips, and tricks: our <a href="https://www.commonchange.com/group-handbook/" target="_blank">Group Handbook</a>.
		</p>
		<p>
			Curious about what’s happening in other groups?
			<a href="https://www.commonchange.com/blog/" target="_blank">Read Now</a>
		</p>
	</blockquote>
	-->
@stop

@section('content')
<!--main contain part -->
<div class="row group-description signup people-margindbottom clearfix">
	<div class="col-sm-6 col-xs-12">
		<div class="shared-people welcome-content sign-border clearfix">
			<h2>LOG IN</h2>
			<span >
				<blockquote>Log in to your Common Change account now </blockquote>
			</span>
			@include('session.login_form')
		</div>
	</div>
	<!--sing up part strat -->
	<div class="col-sm-6 col-xs-12">
		<div class="shared-people welcome-content sign-border clearfix">
			<h2>JOIN NOW</h2>
			<span >
			</span>
			@include('session.signup_form')
		</div>
	</div>
	<!--sing up part end -->
</div>
<!-- end of contain part -->
<div class="clearfix"></div>
<!-- text part-->
<div class="col-lg-12 larg-text">
	<p>Pooling money with people you know, to share with people you care about.</p>
</div>
<!-- end of text part -->
@include('session.recaptcha')
@stop

@section('old-content')
	<h1>Login!</h1>

	@if($errors->has('invalid'))
		<div class="error">{{ $errors->first('invalid') }}</div>
	@endif


	{{ html()->form()->open() }}
		<table>
		<tr>
			<td class=label>{{ html()->label('Email Address: ', 'email') }}</td>
			<td>{{ html()->email('email') }}</td>
			<td>@error('email')
					<span class="error">{{ $message }}</span>
				@enderror</td>
		</tr>
		<tr>
			<td class=label>{{ html()->label('Password: ', 'password') }}</td>
			<td>{{ html()->password('password') }}</td>
			<td>@error('password')
					<span class="error">{{ $message }}</span>
				@enderror</td>
		</tr>
		<tr><td></td><td>{{ html()->submit('Login') }} or {{ html()->a(url('/register'), 'Create a new Account') }}</td></tr>
		<tr><td></td><td>{{ html()->a(url('/password/remind'), 'Forgot Password') }}</td></tr>
		</table>
	{{ html()->form()->close() }}

@stop

