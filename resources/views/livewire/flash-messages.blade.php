<div>
    @if($successMessage)
        <div 
            x-data="{ show: true }" 
            x-show="show" 
            x-init="setTimeout(() => show = false, 5000)"
            x-transition:enter="transition ease-out duration-300"
            x-transition:enter-start="opacity-0 transform scale-90"
            x-transition:enter-end="opacity-100 transform scale-100"
            x-transition:leave="transition ease-in duration-300"
            x-transition:leave-start="opacity-100 transform scale-100"
            x-transition:leave-end="opacity-0 transform scale-90"
            class="success-message"
        >
            <div class="flex justify-between items-center">
                <span>{{ $successMessage }}</span>
                <button type="button" @click="show = false" class="close-btn">&times;</button>
            </div>
        </div>
    @endif
    
    @if($errorMessage)
        <div 
            x-data="{ show: true }" 
            x-show="show" 
            x-init="setTimeout(() => show = false, 5000)"
            x-transition:enter="transition ease-out duration-300"
            x-transition:enter-start="opacity-0 transform scale-90"
            x-transition:enter-end="opacity-100 transform scale-100"
            x-transition:leave="transition ease-in duration-300"
            x-transition:leave-start="opacity-100 transform scale-100"
            x-transition:leave-end="opacity-0 transform scale-90"
            class="error-message"
        >
            <div class="flex justify-between items-center">
                <span>{{ $errorMessage }}</span>
                <button type="button" @click="show = false" class="close-btn">&times;</button>
            </div>
        </div>
    @endif
</div>