@extends('layouts.master')
@inject('navBar', 'App\Models\NavBar')

@section('title')
	Resources
@stop

@section('head')
<link type="text/css" rel="stylesheet" href="{{ asset('/css/selectboxit.css?version=1') }}" media="all">
{{ $navBar->setActive('home') }}
@stop

@section('home-content')
	<span>RESOURCES</span>
@stop

@section('content')
<div class="welcome-content">
	<h2><small>Video Tutorials:</small></h2>
	<a href="{{ URL::to('https://www.commonchange.com/video-tutorials/') }}" target="_blank" class="btn btn-success" role="button">
	VIEW TUTORIALS
	</a><br /><br />

	<h2><small>User Handbook PDF</small></h2>
	<a href="{{ URL::to('https://www.commonchange.com/wp-content/uploads/2015/05/Group-Handbook-v030315.pdf') }}" target="_blank" class="btn btn-success" role="button">
	DOWNLOAD HANDBOOK
	</a>
</div>
@stop

@section('scripts')
<script src="{{ asset('/js/jquery-ui.js') }}"></script>
<script src="{{ asset('/js/selectBoxIt.js') }}"></script>
<script>
	console.log('did it');
	$("select").selectBoxIt({
		autoWidth: false
	});
</script>
@stop