@extends('layouts.master')

@section('title')
	Submit a Discussion
@stop

@section('head')
<link type="text/css" rel="stylesheet" href="{{ asset('/css/jquery-ui1.css?version=1') }}" media="all">
@stop

@section('home-content')
	<span>Edit Discussion</span>
@stop

@section('content')
<div class="col-lg-12 welcome-content welcone-pedding clearfix text-padding">
	<h4>DESCRIPTION</h4>
	<p>Enter the details below to create a new group discussion. Discussions can be used to communicate with your group. You may want to discuss
	group goals, size, new members and objectives.</p>

	<!-- form start -->
	<!-- <form class="form-horizontal cust-control"> -->
	{{ html()->modelForm($discussion, 'PUT', route('discussion.update', $discussion->id))->class('form-horizontal cust-control')->attribute('name', 'discuss')->open() }}
		@include('discussions.form')

		@if( !$discussion->hasIssue() && $group->canCreateRequest(Auth::User()) )
		<div class="form-group cust-group">
			<div class="col-xs-12  col-lg-offset-2 col-sm-offset-3">
				<div class="col-lg-1 col-md-1 col-sm-2 col-xs-3 no-padding">
					<a href="{{ URL::route('discussion.new_issue',array('discussion'=>$discussion->id)) }}" title="View Request" class="btn btn-success small-button" role="button">Attach a Request</a>
				</div>
			</div>
		</div>
		@endif
		<div class="form-group cust-group">
			<label for="input" class="col-sm-3 col-lg-2  control-label"> <span>Discussion Title:</span></label>
			<div class="col-sm-4">
				<!-- <input type="text" class="form-control cust-form" id="input" placeholder=""> -->
				@error('title')
					<span class="error">{{ $message }}</span>
				@enderror
				{{ html()->text('title')->class('form-control cust-form')->id('input')->placeholder('') }}
			</div>
		</div>
		<div class="form-group cust-group">
			<label for="input" class="col-sm-3 col-lg-2 control-label"> <span>Open/Closed:</span> </label>
			<div class="col-sm-4">
				<!-- <input type="text" class="form-control cust-form" id="input" placeholder="MM/DD/YYYY"> -->
				@error('status')
					<span class="error">{{ $message }}</span>
				@enderror
				<div class="ui-widget">
					{{ html()->select('status', array(0 => 'Please Select', 'open' => 'Open', 'closed' => 'Closed'))->id('combobox') }}
				</div>
			</div>
		</div>

		<!-- Submit buttons -->
		<div class="form-group cust-group">
			<div class="col-xs-12  col-lg-offset-2 col-sm-offset-3">
				<div class="col-lg-1 col-md-1 col-sm-2 col-xs-3 no-padding">
					<a href="#" title="View Request" class="btn btn-success small-button" role="button">BACK</a>
				</div>
				<div class="col-lg-3 col-md-3 col-sm-3 col-xs-9 no-padding">
					<button type="submit" title="Save Changes" class="btn btn-success big-button"><span><img src="/images/button-two.png"></span>SAVE</button>
				</div>
			</div>
		</div>
		<!-- end Submit buttons -->
	{{ html()->closeModelForm() }}
	<!-- form end -->
</div>
@stop

@section('scripts')
<script src="{{ asset('/js/jquery-ui.js') }}"></script>
<script src="{{ asset('/js/custom-combobox.js') }}"></script>
@stop