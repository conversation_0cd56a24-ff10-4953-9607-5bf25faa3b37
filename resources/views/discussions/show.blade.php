@extends('layouts.master')

@section('title')
	View Discussion
@stop

@section('head')
<link type="text/css" rel="stylesheet" href="{{ asset('/css/dropdown.css?version=1') }}" media="all">
<link type="text/css" rel="stylesheet" href="{{ asset('/css/welcomenav.css?version=2') }}" media="all">
@stop

@section('home-content')
	<span>DISCUSSION</span>
@stop

@section('content')
<div class="col-lg-12 col-sm-12 col-xs-12 welcome-content contain-margin clearfix">
	<div class="col-lg-12 col-sm-12 col-xs-12 needs-content clearfix">
		<h2>{{ $discussion->title }}
		@if($discussion->canEdit(Auth::User()))
			&nbsp;&nbsp;&nbsp;
			<small>
			<a href="{{ URL::route('discussion.edit',$discussion->id) }}">Edit Discussion</a>
			</small>
		@endif
		@if($discussion->canDelete(Auth::User()))
			@if($discussion->canEdit(Auth::User()))
				&nbsp;|&nbsp;
			@endif
			<small>
			<a href="{{ URL::route('discussion.delete',array('discussion'=>$discussion->id)) }}" id="delete_discussion">Delete Discussion</a>
			</small>
		@endif
		</h2>
		<div>by {{ $discussion->creator->firstname }} {{ $discussion->creator->lastname }}</div>
		<hr>
	</div>
	<!-- start comment part -->
	<div class=" col-lg-12 col-sm-12 col-xs-12 need-bg need-top clearfix">
		<h2>Comments</h2>
		<ul class="comment-contain">
		@foreach($discussion->posts as $post)
			@include('posts.display')
		@endforeach
			<li class="comment1 clearfix">
				<div class="col-lg-12 col-sm-12  col-xs-12 clearfix  no-padding">
					<div class="col-lg-1 col-sm-1 no-padding"><span class="mediumpix" style="background-image: url('{{ Auth::User()->profile_pic }}')"></span></div>
					<div class="col-sm-8 cust-group">
						{{ html()->form('POST', route('discussion.post', $discussion->id))->attribute('name', 'post')->open() }}
						<!-- <input type="text" class="form-control cust-form" id="input" placeholder="Write a comment..."> -->
						<div class="col-sm-8 no-padding">{{ html()->textarea('comment')->class('form-control cust-form')->rows(3)->placeholder('Write a comment...') }}</div>
						<div class="col-sm-4"><button type="submit" class="btn butvote btn1  btn-default"> <span><img src="/images/arrowsend.png"></span>Submit</button></div>
						{{ html()->form()->close() }}
					</div>
				</div>
			</li>
			<li></li>
		</ul>
	</div>
	<!-- end of comment part -->
</div>
@stop

@section('scripts')
<script> 
	$(document).ready(function() {
		$(".basic-example").heapbox();
		$('.delete_comment').click(function(event) {
			event.preventDefault();
			var r=confirm("Are you sure you want to delete this comment?");
			if (r==true)   {  
				window.location = $(this).attr('href');
			}
		});
		$('#delete_discussion').click(function(event) {
			event.preventDefault();
			var r=confirm("Are you sure you want to delete this discussion?");
			if (r==true)   {  
				window.location = $(this).attr('href');
			}
		});
	});
</script>
<script> 
	$(function () {
		$('[data-toggle="tooltip"]').tooltip();
	});
</script>
<script src="{{ asset('/js/bootstrap-toggle.js') }}"></script>
<script src="{{ asset('/js/jquery.heapbox-0.9.4.js') }}"></script>
@stop
