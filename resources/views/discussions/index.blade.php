@extends('layouts.dashboard-new')

@section('title')
	Discussions for {{ $group->name }}
@stop

@push('styles')
<link type="text/css" rel="stylesheet" href="{{ asset('/css/bootstrap-datetimepicker.css?version=1') }}" media="all">
@endpush

@section('content')
<div class="content-body">
    <div class="container">
        <div class="page-header">
            <h1>Discussions for {{ $group->name }}</h1>
            <div class="page-actions">
                <a href="{{ URL::route('group.create_discussion',array('id'=>$group->id)) }}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Start a Discussion
                </a>
            </div>
        </div>

        <!-- Filter and Search Form -->
        <div class="filter-section">
            {{ html()->modelForm($input, 'GET', route('group.discussions', ['id' => $group->id]))->class('filter-form')->attribute('name', 'discuss')->open() }}
                <div class="filter-row">
                    <div class="filter-group">
                        <label for="from_date">From Date:</label>
                        {{ html()->text('from_date')->id('from_date')->class('form-control form_datetime')->placeholder('Start date') }}
                    </div>
                    <div class="filter-group">
                        <label for="to_date">To Date:</label>
                        {{ html()->text('to_date')->id('to_date')->class('form-control form_datetime')->placeholder('End date') }}
                    </div>
                    <div class="filter-group">
                        <label for="sort">Sort by:</label>
                        {{ html()->select('sort', array('newest' => 'Newest', 'oldest' => 'Oldest', 'alpha' => 'Alphabetical (A-Z)', 'revalpha' => 'Alphabetical (Z-A)'))->class('form-control')->attribute('onchange', 'document.discuss.submit()') }}
                    </div>
                    <div class="filter-group">
                        <label for="search">Search:</label>
                        <div class="search-input-group">
                            {{ html()->text('search')->class('form-control')->placeholder('Search discussions...') }}
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                </div>
            {{ html()->closeModelForm() }}
        </div>

        <!-- Pagination -->
        <div class="pagination-wrapper">
            {{ $discussions->links() }}
        </div>

        <!-- Discussions List -->
        <div class="activity-card">
            <div class="card-body">
                @if($discussions->count() > 0)
                    @foreach($discussions as $discussion)
                        <div class="activity-item">
                            <div class="activity-avatar">
                                @if($discussion->creator && $discussion->creator->profile_pic)
                                    <img src="{{ $discussion->creator->profile_pic }}" alt="{{ $discussion->creator->firstname }} {{ $discussion->creator->lastname }}">
                                @else
                                    <div class="avatar-placeholder">
                                        <i class="fas fa-user"></i>
                                    </div>
                                @endif
                            </div>
                            <div class="activity-content">
                                <div class="activity-header">
                                    <h3 class="activity-title">
                                        <a href="{{ URL::route('discussion.show',array('discussion'=>$discussion->id)) }}">{{ $discussion->title }}</a>
                                    </h3>
                                    <span class="activity-time">{{ $discussion->created_at->diffForHumans() }}</span>
                                </div>
                                <p class="activity-group">
                                    Discussion by {{ $discussion->creator ? $discussion->creator->firstname . ' ' . $discussion->creator->lastname : 'Unknown User' }}
                                    in {{ $group->name }}
                                </p>
                                <div class="activity-statuses">
                                    <span class="badge badge-info">{{ $discussion->status }}</span>
                                    @if ($discussion->is_unread)
                                        <span class="badge badge-warning">Unread</span>
                                    @else
                                        <span class="badge badge-success">Read</span>
                                    @endif
                                    @if ($discussion->has_new_posts)
                                        <span class="badge badge-primary">New Posts</span>
                                    @endif
                                </div>
                                <div class="activity-actions">
                                    <a href="{{ URL::route('discussion.show',array('discussion'=>$discussion->id)) }}" class="activity-action">
                                        <i class="fas fa-eye"></i>
                                        <span>View Discussion</span>
                                    </a>
                                    <a href="{{ URL::route('discussion.show',array('discussion'=>$discussion->id)) }}#comments" class="activity-action">
                                        <i class="fas fa-comment"></i>
                                        <span>Reply</span>
                                    </a>
                                    @if($discussion->canDelete(Auth::User()))
                                        <a href="{{ URL::route('discussion.delete',array('discussion'=>$discussion->id)) }}" class="activity-action delete_discussion" title="Delete this discussion">
                                            <i class="fas fa-trash"></i>
                                            <span>Delete</span>
                                        </a>
                                    @endif
                                </div>
                            </div>
                        </div>
                    @endforeach
                @else
                    <div class="empty-state">
                        <div class="empty-icon">
                            <i class="fas fa-comments"></i>
                        </div>
                        <h3>No discussions yet</h3>
                        <p>Start the conversation by creating the first discussion for this group.</p>
                        <a href="{{ URL::route('group.create_discussion',array('id'=>$group->id)) }}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Start Discussion
                        </a>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
@stop

@section('scripts')
<script src="{{ asset('/js/jquery-ui.js') }}"></script>
<script src="{{ asset('/js/bootstrap-datetimepicker.min.js') }}"></script>
<script type="text/javascript">
$(document).ready(function() {
    $(".form_datetime").datetimepicker({ 
        format: 'yyyy-mm-dd', 
        pickTime: false,
        autoclose: true
    });
    
    $('.delete_discussion').click(function(event) {
        event.preventDefault();
        var r = confirm("Are you sure you want to delete this discussion?");
        if (r == true) {
            window.location = $(this).attr('href');
        }
    });
});
</script>
@stop
