<!-- Bootstrap core JavaScript
    ================================================== --> 
<!-- Placed at the end of the document so the pages load faster -->
@include('common.scripts_base')
@if(App::environment() != 'local')
	<!-- begin SumoMe -->
	<script src="//load.sumome.com/" data-sumo-site-id="{{ $_ENV['SUMO_SITE_ID'] }}" async="async"></script>
	<!-- end SumoMe -->

	<!-- BEGIN MOUSEFLOW: -->
	<script type="text/javascript">
	   var _mfq = _mfq || [];
	   (function() {
	       var mf = document.createElement("script"); mf.type = "text/javascript"; mf.async = true;
	       mf.src = "//cdn.mouseflow.com/projects/df771ff7-4cca-48f1-bb74-298cc3fc82e5.js";
	       document.getElementsByTagName("head")[0].appendChild(mf);
	   })();
	</script>
	<!-- END MOUSEFLOW -->

	<!--Start of Tawk.to Script-->
	<script type="text/javascript">
	var Tawk_API=Tawk_API||{}, Tawk_LoadStart=new Date();
	(function(){
	var s1=document.createElement("script"),s0=document.getElementsByTagName("script")[0];
	s1.async=true;
	s1.src='https://embed.tawk.to/564aaabba1dfe5e33b503d3e/default';
	s1.charset='UTF-8';
	s1.setAttribute('crossorigin','*');
	s0.parentNode.insertBefore(s1,s0);
	})();
	</script>
	<!--End of Tawk.to Script-->

@endif
<!-- IE10 viewport hack for Surface/desktop Windows 8 bug --> 
<!-- <script src="../../assets/js/ie10-viewport-bug-workaround.js"></script> -->
