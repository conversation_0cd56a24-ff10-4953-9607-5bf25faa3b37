<header id="header">
  <div class="container top-header no-padding clearfix">
    <div class="col-lg-6 col-md-6 col-xs-12 pull-left logo-content no-padding"> <a href="//www.commonchange.com"><img src="/images/logo.png" alt="" title="Common Change" class="img-responsive"></a> </div>
    <div class="col-lg-6 col-md-6 col-xs-12 pull-right signout-content no-padding">
		@if( Auth::check() )
			<span class="user-welcome">
				<i class="glyphicon glyphicon-bell" style="cursor:pointer;" aria-label="Settings Menu" title="Notifications"></i>&nbsp;&nbsp;
				Welcome, {{ Auth::user()->firstname }} {{ Auth::user()->lastname }}&nbsp;&nbsp;
				<span class="dropdown" style="cursor:pointer;">
					<a class="dropdown-toggle" id="dropdownMenu1" data-toggle="dropdown"><i class="glyphicon glyphicon-cog" aria-label="Settings Menu" title="Settings Menu"></i><i class="caret"></i></a>
				  </button>
				  <ul class="dropdown-menu dropdown-menu-right" role="menu" aria-labelledby="dropdownMenu1">
					<li role="presentation"><a role="menuitem" tabindex="-1" href="/account"><i class="glyphicon glyphicon-list-alt"></i>&nbsp;&nbsp;Account Settings</a></li>
					<li role="presentation"><a role="menuitem" tabindex="-1" href="/account/invite"><i class="glyphicon glyphicon-bullhorn"></i>&nbsp;&nbsp;Invite Friends</a></li>
					<li role="presentation"><a role="menuitem" tabindex="-1" href="/resources"><i class="glyphicon glyphicon-info-sign"></i>&nbsp;&nbsp;Resources</a></li>
					<li role="presentation"><a role="menuitem" tabindex="-1" href="/help"><i class="glyphicon glyphicon-question-sign"></i>&nbsp;&nbsp;Help</a></li>
				  </ul>
				</span>
			</span>
			@if(Session::has('impersonater_id'))
			{{ html()->a(url('/stop_impersonate'), 'Stop Impersonating >')->class('link-account text-small')->attribute('style', 'color:red;') }}
			@else
			{{ html()->a(url('/logout'), 'Log Out >')->class('link-account text-small') }}
			@endif
		@endif
	</div>
	@if(isset(Auth::User()->wizard_bookmark) && (Route::getCurrentRoute() !== null) && !preg_match('/wizard/',Route::getCurrentRoute()->uri()))
    <div class="col-lg-6 col-md-6 col-xs-12 pull-right signout-content no-padding">
		{{ html()->a(url('/wizard'), 'Return to the Setup Wizard')->class('link-account text-small') }}
	</div>
	@endif
  </div>
</header>
