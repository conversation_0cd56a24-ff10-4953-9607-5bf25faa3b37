@if(Session::get('alerts') !== null)
	<div id="cc_alerts" class="container">
	@foreach(Session::pull('alerts') as $type=>$alert)
		<div class="alert alert-{{ $type }} alert-dismissible" role="alert">
			<table><tbody><tr class="alert-row">
				<td class="alert-symbol" aria-label="alert-symbol">
					@if($type == 'info')
						<i class="glyphicon glyphicon-info-sign"></i>
					@elseif($type == 'success')
						<i class="glyphicon glyphicon-ok-sign"></i>
					@elseif($type == 'warning')
						<i class="glyphicon glyphicon-exclamation-sign"></i>
					@elseif($type == 'danger')
						<i class="glyphicon glyphicon-remove-sign"></i>
					@endif
				</td>
				<td class="alert-text"><p>{{ $alert }}</p></td>
				<td class="close-button" aria-label="close-button">
					<button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">&times;</span></button>
				</td>
			
			</tr></tbody></table>
		</div>
	@endforeach
	</div>
@endif
