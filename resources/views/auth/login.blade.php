@extends('layouts.modern')

@section('title', 'Login to Common Change')

@section('head')
<link rel="stylesheet" href="{{ asset('css/landing.css') }}">
@endsection

@section('content')
<!-- Debug info for login page -->
<script>
    console.log('Login page loaded');
    console.log('Current URL:', window.location.href);
    console.log('Referrer:', document.referrer);

    // Check for authentication status
    const isAuthenticated = document.cookie.includes('common_change_v5_session');
    console.log('Authentication cookie present:', isAuthenticated);

    // Add a flag to detect redirect loops
    if (sessionStorage.getItem('login_page_visits')) {
        const visits = parseInt(sessionStorage.getItem('login_page_visits')) + 1;
        sessionStorage.setItem('login_page_visits', visits);
        console.log('Login page visits:', visits);

        if (visits > 3) {
            console.error('Possible redirect loop detected!');
        }
    } else {
        sessionStorage.setItem('login_page_visits', 1);
    }
</script>

<!-- Login Page -->
<div class="signup-page" id="login-container">
    <div class="signup-image signin-image"></div>
    <div class="signup-section">
        <div class="signup-logo">
            <img src="/images/logo.png" alt="Common Change Logo">
        </div>

        <h1>Welcome Back</h1>
        <p class="signup-instructions">
            <b>Login to your account</b><br>
            Enter your credentials below
        </p>

        <!-- Error message container -->
        <div id="login-error" class="error-message">
            @error('invalid')
                {{ $message }}
            @enderror
        </div>

        <!-- Debug information -->
        @if(isset($debug_info))
        <div class="debug-info" style="display: none;">
            <pre>{{ json_encode($debug_info, JSON_PRETTY_PRINT) }}</pre>
        </div>
        @endif

        <form method="POST" action="{{ route('auth.login.post') }}" id="login-form" novalidate>
            @csrf
            <!-- Debug info for form submission -->
            <input type="hidden" name="debug_timestamp" value="{{ time() }}">
            <input type="hidden" name="session_id" value="{{ session()->getId() }}">
            <input type="hidden" name="route_name" value="{{ request()->route()->getName() }}">
            <div class="input-group">
                <input type="email" name="email" placeholder="Email address" value="{{ old('email') }}" class="@error('email') is-invalid @enderror" required>
                @error('email')
                    <div class="field-error">{{ $message }}</div>
                @enderror
            </div>

            <div class="input-group">
                <input type="password" name="password" placeholder="Password" class="@error('password') is-invalid @enderror" required>
                @error('password')
                    <div class="field-error">{{ $message }}</div>
                @enderror
            </div>

            <div class="remember-me">
                <label>
                    <input type="checkbox" name="remember"> Remember me
                </label>
                <a href="{{ route('password.remind') }}" class="forgot-password">Forgot password?</a>
            </div>

            <button type="submit" class="btn-primary">Login</button>
        </form>

        <div class="divider">OR</div>

        <div class="social-signup">
            <a href="{{ route('login.google') }}" class="social-btn">
                <img src="/images/google-icon.png" alt="Google Icon" class="social-icon">
                Login with Google
            </a>

            <a href="{{ route('login.apple') }}" class="social-btn">
                <img src="/images/apple-icon.png" alt="Apple Icon" class="social-icon">
                Login with Apple
            </a>

            <a href="{{ route('login.microsoft') }}" class="social-btn">
                <img src="/images/windows-icon.png" alt="Microsoft Icon" class="social-icon">
                Login with Microsoft
            </a>

            <a href="{{ route('login.facebook') }}" class="social-btn">
                <img src="/images/facebook-icon.png" alt="Facebook Icon" class="social-icon">
                Login with Facebook
            </a>
        </div>

        <div class="signup-footer">
            Don't have an account? <a href="{{ route('signup') }}">Sign up</a>
        </div>
    </div>
</div>


@endsection

@section('scripts')
<script>
    // Add Vite debugging information
    console.log('Vite assets loaded:', {
        'modern.js': typeof window.modernApp !== 'undefined',
        'app.js': typeof window.axios !== 'undefined'
    });

    // Check for any redirection issues
    console.log('Current URL:', window.location.href);

    document.addEventListener('DOMContentLoaded', function() {
        // Login form handling
        const loginForm = document.getElementById('login-form');

        // Display server-side validation errors using SweetAlert if available
        @if($errors->any())
            if (typeof Swal !== 'undefined') {
                const errorMessages = [];
                @foreach($errors->all() as $error)
                    errorMessages.push("{{ $error }}");
                @endforeach

                let errorHtml = '<ul style="text-align: left; margin-top: 0;">';
                errorMessages.forEach(error => {
                    errorHtml += '<li>' + error + '</li>';
                });
                errorHtml += '</ul>';

                Swal.fire({
                    icon: 'error',
                    title: 'Validation Error',
                    html: errorHtml,
                    confirmButtonColor: '#5144A1'
                });
            } else {
                // If SweetAlert is not available, show error in the container
                const errorContainer = document.getElementById('login-error');
                errorContainer.textContent = "{{ $errors->first() }}";
                errorContainer.style.display = 'block';

                // Add invalid class to inputs with errors
                @if($errors->has('email'))
                    document.querySelector('input[name="email"]').classList.add('is-invalid');
                @endif

                @if($errors->has('password'))
                    document.querySelector('input[name="password"]').classList.add('is-invalid');
                @endif
            }
        @endif



        // Login form validation and submission
        loginForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(loginForm);
            const submitButton = loginForm.querySelector('button[type="submit"]');
            const emailInput = loginForm.querySelector('input[name="email"]');
            const passwordInput = loginForm.querySelector('input[name="password"]');
            const email = emailInput.value.trim();
            const password = passwordInput.value;
            const errorContainer = document.getElementById('login-error');

            // Clear previous error messages
            errorContainer.textContent = '';
            errorContainer.style.display = 'none';

            // Remove previous error styling
            emailInput.classList.remove('is-invalid');
            passwordInput.classList.remove('is-invalid');

            // Remove any existing field error messages
            const existingFieldErrors = loginForm.querySelectorAll('.field-error');
            existingFieldErrors.forEach(el => el.remove());

            let isValid = true;
            let errors = [];

            // Email validation with regex
            const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
            if (!email) {
                isValid = false;
                errors.push('Please enter your email address');
                emailInput.classList.add('is-invalid');
                addFieldError(emailInput, 'Please enter your email address');
            } else if (!emailRegex.test(email)) {
                isValid = false;
                errors.push('Please enter a valid email address');
                emailInput.classList.add('is-invalid');
                addFieldError(emailInput, 'Please enter a valid email address');
            }

            // Password validation
            if (!password) {
                isValid = false;
                errors.push('Please enter your password');
                passwordInput.classList.add('is-invalid');
                addFieldError(passwordInput, 'Please enter your password');
            }

            // If validation fails, show errors
            if (!isValid) {
                if (typeof Swal !== 'undefined') {
                    // Use SweetAlert if available
                    let errorHtml = '<ul style="text-align: left; margin-top: 0;">';
                    errors.forEach(error => {
                        errorHtml += '<li>' + error + '</li>';
                    });
                    errorHtml += '</ul>';

                    Swal.fire({
                        icon: 'error',
                        title: 'Validation Error',
                        html: errorHtml,
                        confirmButtonColor: '#5144A1'
                    });
                } else {
                    // Fallback to standard error display
                    errorContainer.textContent = errors[0];
                    errorContainer.style.display = 'block';
                }
                return;
            }

            // Show loading state
            submitButton.disabled = true;
            submitButton.textContent = 'Logging in...';

            // Show loading spinner with SweetAlert if available
            if (typeof Swal !== 'undefined') {
                Swal.fire({
                    title: 'Logging in...',
                    text: 'Please wait',
                    allowOutsideClick: false,
                    didOpen: () => {
                        Swal.showLoading();
                    }
                });
            }

            // Add debugging information before form submission
            console.log('Form submission - Authentication status:', document.cookie.includes('common_change_v5_session'));
            console.log('Form action:', loginForm.action);
            console.log('Form method:', loginForm.method);

            // Store login attempt timestamp
            localStorage.setItem('last_login_attempt', Date.now());

            // Submit the form
            loginForm.submit();

            // Helper function to add field-specific error messages
            function addFieldError(inputElement, message) {
                const errorDiv = document.createElement('div');
                errorDiv.className = 'field-error';
                errorDiv.textContent = message;

                // Find the parent input-group
                const inputGroup = inputElement.closest('.input-group');
                if (inputGroup) {
                    inputGroup.appendChild(errorDiv);
                } else {
                    // Fallback if input-group not found
                    inputElement.insertAdjacentElement('afterend', errorDiv);
                }
            }
        });


    });
</script>
@endsection
