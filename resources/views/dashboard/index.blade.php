@extends('layouts.dashboard')

@section('title', 'Dashboard')

@section('page-title', 'Dashboard')

@section('page-actions')
<div class="page-actions">
        <i class="fa-solid fa-plus"></i> New Request
    </a>
</div>
@endsection

@section('content')
<div class="dashboard-grid">
    <div class="dashboard-card">
        <div class="card-header">
            <h3>My Groups</h3>
            
        </div>
    </div>
    
    <div class="dashboard-card">
        <div class="card-header">
            <h3>Recent Discussions</h3>
        </div>
        <div class="card-body">
            @if($recentDiscussions->count() > 0)
                <div class="discussion-list">
                    @foreach($recentDiscussions as $discussion)
                        <div class="discussion-item">
                            <div class="discussion-info">
                                <h4>{{ $discussion->title }}</h4>
                                <p>{{ $discussion->group->name }} • {{ $discussion->created_at->diffForHumans() }}</p>
                            </div>
                            <div class="discussion-stats">
                                <span><i class="fa-solid fa-comment"></i> {{ $discussion->posts_count }}</span>
                            </div>
                        </div>
                    @endforeach
                </div>
            @else
                <div class="empty-state">
                    <p>No recent discussions found.</p>
                    <a href="{{ route('discussions.create') }}" class="btn btn-outline">Start Discussion</a>
                </div>
            @endif
        </div>
    </div>
    
    
</div>
@endsection
