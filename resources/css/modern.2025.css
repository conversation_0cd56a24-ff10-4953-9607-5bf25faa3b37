/* Core styles for signup page */
:root {
    /* Primary colors */
    --primary-green: #9ACB48;
    --primary-purple: #8347CC;
    --primary-grey: #CECECE;
    --primary-navy: #5144A1;
    --primary-black: #1A1A1A;
    
    /* Light colors */
    --light-cream: #F9F9F0;
    --light-lavender: #F0F0F9;
    --light-blue: #E9EFF2;
    --light-gray: #808080;
    --light-white: #FFFFFF;
    
    /* Font */
    --font-family: 'Inter', sans-serif;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: var(--font-family);
}

body {
    background-color: var(--light-white);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Signup Page */
.signup-page {
    display: flex;
    height: 100vh;
}

.signup-image {
    flex: 1;
    background-size: cover;
    background-position: center;
}

.signup-section {
    flex: 1;
    padding: 40px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center; /* Center align the content */
    width: 100%;
}

.signup-section form {
    width: 100%;
    text-align: center;
}


/* Container for form elements - 60% of parent width */
.signup-section-content {
    width: 100%; /* Changed from 60% to 100% */
}

.signup-section .signup-logo {
    margin-bottom: 50px;
    width: 60%; /* Changed from 60% to 100% */
}

.signup-section .signup-logo img {
    height: 89px;
}

/* Typography styles */
.signup-section h1 {
    font-family: 'Inter', sans-serif;
    font-weight: 700;
    font-size: 24px;
    line-height: 36px;
    letter-spacing: 0px;
    vertical-align: middle;
    color: var(--primary-navy);
    margin-bottom: 20px;
    width: 60%; /* Changed from 60% to 100% */
}

.signup-section .signup-instructions {
    font-size: 18px;
    margin-bottom: 30px;
    width:60%;
}

.signup-section .signup-instructions b {
    font-family: 'Poppins', sans-serif;
    font-weight: 600;
    font-size: 20px;
    line-height: 30px;
    letter-spacing: 0px;
    vertical-align: middle;
    display: block;
    margin-bottom: 5px;
}

.signup-section .signup-instructions br + span,
.signup-section .signup-instructions br + text {
    font-family: 'Poppins', sans-serif;
    font-weight: 600;
    font-size: 20px;
    line-height: 30px;
    letter-spacing: 0px;
    vertical-align: middle;
    display: block;
}




/* Input styles with placeholder color */
.signup-section input[type="email"],
.signup-section input[type="text"],
.signup-section input[type="password"],
.signup-section input[type="number"],
.signup-section textarea,
.signup-section select {
    width: 100%; /* Changed from 60% to 100% */
    padding: 15px;
    margin-bottom: 20px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 16px;
    box-shadow: 0px 0px 4px 2px #0000001A;
}

/* Placeholder color for all browsers */
.signup-section input::placeholder,
.signup-section textarea::placeholder {
    color: var(--light-gray);
    opacity: 1; /* Firefox */
}

/* Internet Explorer 10-11 */
.signup-section input:-ms-input-placeholder,
.signup-section textarea:-ms-input-placeholder {
    color: var(--light-gray);
}

/* Microsoft Edge */
.signup-section input::-ms-input-placeholder,
.signup-section textarea::-ms-input-placeholder {
    color: var(--light-gray);
}

.signup-section .btn-primary {
    background-color: var(--primary-navy);
    color: white;
    border: none;
    padding: 15px;
    width: 60%;
    border-radius: 5px;
    font-size: 16px;
    cursor: pointer;
    margin-bottom: 20px;
    transition: background-color 0.3s ease;
}

/* Fix for button width */
.signup-section .btn-primary {
    background-color: var(--primary-navy);
    color: white;
    border: none;
    padding: 15px;
    width: 60%;
    border-radius: 5px;
    font-size: 16px;
    cursor: pointer;
    margin-bottom: 20px;
    transition: background-color 0.3s ease;
}

.signup-section .btn-primary:hover {
    background-color: var(--primary-purple) !important;
    color: white !important;
}

.signup-section .divider {
    text-align: center;
    margin: 20px 0;
    color: #777;
    width: 60%;
}

.signup-section .social-signup {
    display: flex;
    flex-direction: column;
    gap: 10px;
    width: 60%;
}

.signup-section .social-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: white;
    border: 1px solid var(--primary-grey);
    padding: 12px;
    border-radius: 5px;
    font-size: 12px;
    cursor: pointer;
    gap: 10px;
    width: 100%;
    transition: all 0.3s ease;
    color: var(--primary-black);
    text-decoration: none;
}

.signup-section .social-btn:hover {
    background-color: var(--light-lavender);
    border-color: var(--primary-purple);
}

.signup-section .social-btn img {
    height: 20px;
}

.signup-section .signup-footer {
    text-align: center;
    margin-top: 30px;
    width: 60%;
}

.signup-footer a {
    color: var(--primary-purple);
    text-decoration: none;
}

/* Email Verification Page Styles */
/* Reusing signup-page classes where applicable */

/* OTP Verification Styles */
.instruction-text {
    font-size: 16px;
    margin-bottom: 10px;
    color: var(--primary-black);
    text-align: left;
    width: 100%;
}

.email-highlight {
    font-weight: bold;
}

.expiry-note {
    font-size: 16px;
    margin-bottom: 30px;
    color: var(--primary-black);
    text-align: left;
    width: 100%;
}

.didnt-receive {
    margin-bottom: 30px;
    color: var(--primary-black);
    text-align: left;
    width: 100%;
}

.try-again {
    color: var(--primary-purple);
    text-decoration: none;
    font-weight: 500;
}

.try-again:hover {
    text-decoration: underline;
}

.otp-container {
    display: flex;
    gap: 8px;
    margin-bottom: 30px;
    justify-content: space-between;
    width: 100%;
    max-width: 100%;
}

.otp-input {
    width: calc((100% - 40px) / 6); /* Distribute width evenly among 6 inputs with 8px gaps */
    height: 60px;
    font-size: 24px;
    text-align: center;
    border: 1px solid #ddd;
    border-radius: 5px;
    box-shadow: 0px 0px 4px 2px rgba(0, 0, 0, 0.1);
}

.otp-input:focus {
    border-color: var(--primary-purple);
    outline: none;
}

.error-message {
    color: #d9534f;
    margin-bottom: 15px;
    text-align: center;
}

/* The following classes are already defined in the modern.2025.css file
   for the signup page, so we're reusing them:
   - signup-page
   - signup-image
   - signup-section
   - signup-logo
   - btn-primary
   - divider
   - social-signup
   - social-btn
   - social-icon
   - signup-footer
*/

#registration-container,
#registration-form,
#registration-form .form-group,
#registration-form label {
    text-align: center;
}

#registration-form label {
    display: block;
}

#registration-form .form-group {
    margin-bottom: 20px;
}

#registration-form .checkbox-group {
    display: flex;
    width: 100%;
}

#registration-form .checkbox-group input[type="checkbox"] {
    margin-right: 10px;
}

#registration-form button.btn-primary {
    margin-top: 10px;
}

/* Add these styles to ensure form elements are properly aligned */
.signup-section form {
    width: 60%;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.signup-section form .otp-container,
.signup-section form .btn-primary {
    width: 100%;
    max-width: 100%;
}

.otp-container {
    display: flex;
    gap: 8px;
    margin-bottom: 30px;
    justify-content: space-between;
    width: 100%;
}

.btn-primary {
    width: 100%;
    margin-top: 0;
}
