@echo off
cd %~dp0

docker-compose build
docker-compose up -d
docker-compose exec web bash -c "until pgrep -f -x ""php artisan serve --host 0.0.0.0"" > /dev/null; do echo ""waiting for composer""; sleep 5; done'

if not exist ".env" (
    echo .env doesn't exist - creating.
    copy .env.cc_local .env
	docker-compose exec web php artisan key:generate
)

docker-compose exec web php artisan migrate:install
docker-compose exec web php artisan migrate
docker-compose exec web php artisan db:seed
