# Laravel app for commonchange.com

Uses Laravel 8

## Setting up a quick dev environment using Docker
This will set up a mysql container, and a PHP container that is able to run the Laravel app using PHP's built-in web server.

Once up and running, you can point your browser to [http://localhost:8000/](http://localhost:8000/) or [http://127.0.0.1:8000/](http://127.0.0.1:8000/) to view the app.  
**The root of this repo will be a mounted volume inside the PHP container, so any changes you make here will be immediately reflected in the web server.**

### Prep checklist:
* You'll need to install Docker: [https://www.docker.com/products/docker-desktop/](https://www.docker.com/products/docker-desktop/)
* Familiarity with Docker: I recommend [https://docs.docker.com/get-started/](https://docs.docker.com/get-started/)
* Familiarity with Laravel 8 / Composer: [https://laravel.com/docs/8.x/](https://laravel.com/docs/8.x/)

### Initial Steps:
1. Clone this repo.
2. `cd` to the root directory of the repo.
3. Run `build_docker_env.sh` (or `build_docker_env.bat` if you're developing on Windows).

### Stopping the containers:
`docker-compose stop`

### Starting the containers up again:
`docker-compose start`

### Details:
#### The build_docker_env.sh script does the following:
1. Uses `docker-compose build` to build/download our images (we build our php image using the Dockerfile, then download the mysql 5.7 image)
2. Brings both containers up using `docker-compose up -d`.
This will start the mysql service in the mysql container.  In the web container, we first run `composer install` before starting up the server with `php artisan serve --host 0.0.0.0`
3. Waits for the server to come up.
4. Copies `.env.cc_local` to `.env` if `.env` doesn't already exist
5. Generates a unique application key if one doesn't already exist.
6. Runs migration scripts to create the database tables
7. Seeds any required data (countries list, initial user / group)

The script uses docker-compose to build and bring up the containers in a coordinated fashion (check out docker-compose.yml for details).  Once the script finishes, you should have two running containers: a mysql container accessible on port 3306, and a php container running a web server accessible on port 8000.

#### Some things to be aware of:
* By default, the containers will bind to ports 8000 and 3306 on localhost.  Make sure you don't already have services bound to those ports (i.e. another MySQL server), otherwise the container will be unable to start up.
* docker-compose will create a data volume for persisting the database data.  Use `docker volume ls` to see the volume name.  It will most likely be called `cc-app-v3_cc-app-v3-dbdata`.
