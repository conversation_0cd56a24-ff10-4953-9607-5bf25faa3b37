<?php

use Illuminate\Foundation\Inspiring;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Schedule;

Artisan::command('inspire', function () {
    $this->comment(Inspiring::quote());
})->purpose('Display an inspiring quote')->hourly();

Schedule::command('cron:process_expired_proposals')->cron('5 * * * *');
Schedule::command('cron:send_deadline_notification')->cron('5 * * * *');
Schedule::command('cron:send_daily_email')->cron('9 13 * * *');
Schedule::command('cron:send_daily_by_discussion')->cron('9 13 * * *');
