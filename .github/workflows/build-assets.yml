name: Build and Deploy Assets

on:
  push:
    branches: [ main, master, staging ]
  pull_request:
    branches: [ main, master, staging ]
  workflow_dispatch:

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v3

    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'

    - name: Install Dependencies
      run: npm install

    - name: Build Assets
      run: npm run build

    - name: Check Build Success
      run: |
        if [ ! -f "public/build/manifest.json" ]; then
          echo "Build failed - manifest.json not found"
          exit 1
        fi

    - name: Upload Build Artifacts
      uses: actions/upload-artifact@v3
      with:
        name: build-assets
        path: public/build/
        retention-days: 7

    # This step assumes you're using a deployment service that pulls from GitHub
    # If you're using a different deployment method, you might need to adjust this
    - name: Deploy to Staging
      if: github.ref == 'refs/heads/staging'
      run: |
        echo "Assets built successfully and will be deployed with the code"
        # Add any additional deployment steps here if needed
