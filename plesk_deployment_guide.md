# Plesk Deployment Guide for Common Change App

This guide outlines the steps to deploy the application to a Plesk server, with a focus on building Vite assets.

## Prerequisites

- Access to the Plesk server via SSH or terminal
- Node.js and npm installed on the server
- Git access to pull the latest code

## Deployment Steps

### 1. Pull the Latest Code

```bash
cd /var/www/vhosts/v5-dev.commonchange.com/httpdocs
git pull origin main  # or your deployment branch
```

### 2. Install Composer Dependencies

```bash
composer install --no-dev --optimize-autoloader
```

### 3. Build Vite Assets

Run the build script to compile all frontend assets:

```bash
chmod +x build_assets.sh
./build_assets.sh
```

Alternatively, you can run the npm commands directly:

```bash
npm install
npm run build
```

### 4. Update Environment Variables

Make sure your `.env` file has the correct settings for production:

```bash
# Check if APP_ENV is set to production
grep "APP_ENV" .env
# If needed, update it
sed -i 's/APP_ENV=.*/APP_ENV=production/' .env
```

### 5. Clear Laravel Cache

```bash
php artisan config:clear
php artisan cache:clear
php artisan view:clear
php artisan route:clear
```

### 6. Set Proper Permissions

```bash
# Set ownership
chown -R webuser:webuser /var/www/vhosts/v5-dev.commonchange.com/httpdocs

# Set directory permissions
find /var/www/vhosts/v5-dev.commonchange.com/httpdocs -type d -exec chmod 755 {} \;

# Set file permissions
find /var/www/vhosts/v5-dev.commonchange.com/httpdocs -type f -exec chmod 644 {} \;

# Make specific files executable
chmod +x /var/www/vhosts/v5-dev.commonchange.com/httpdocs/build_assets.sh
chmod +x /var/www/vhosts/v5-dev.commonchange.com/httpdocs/artisan
```

## Troubleshooting

### Vite Manifest Not Found

If you see the error "Vite manifest not found", it means the assets weren't built correctly. Try:

1. Check if the manifest file exists:
   ```bash
   ls -la public/build/manifest.json
   ```

2. If it doesn't exist, run the build script again:
   ```bash
   ./build_assets.sh
   ```

3. Check for any errors in the npm build process:
   ```bash
   npm run build
   ```

4. Verify Node.js and npm versions:
   ```bash
   node -v
   npm -v
   ```

### Fallback Assets

The application includes fallback assets that will be used if the Vite manifest isn't found. These are located at:
- `public/js/modern.2025.js`
- `public/css/modern.2025.css`

These fallbacks ensure the application remains functional even if there are issues with the Vite build process.

## Automated Deployment

Consider setting up a Git hook or a CI/CD pipeline to automate these steps whenever code is pushed to the deployment branch.

## Contact

If you encounter any issues with the deployment process, please contact the development team.
