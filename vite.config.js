// Change to ESM format
import { defineConfig } from 'vite';
import laravel from 'laravel-vite-plugin';

export default defineConfig({
    plugins: [
        laravel({
            input: [
                'resources/css/app.css',
                'resources/css/modern.2025.css',
                'resources/js/app.js',
                'resources/js/modern.2025.js',
            ],
            refresh: true,
        }),
    ],
    build: {
        // Ensure source maps are generated
        sourcemap: true,
    },
});
