version: '3'
services:
    mailhog:
        image: mailhog/mailhog
        ports:
            - '8025:8025'
            - '1025:1025'
    web:
        build: .
        image: laravel8
        depends_on:
            - mysql
        ports:
            - '8000:8000'
        volumes:
            - '.:/laravel-app'
    mysql:
        image: 'mysql/mysql-server:5.7'
        environment:
            - MYSQL_ALLOW_EMPTY_PASSWORD=yes
            - MYSQL_DATABASE=cc_app
            - MYSQL_USER=cc_app
            - MYSQL_PASSWORD=i_has_pw
        ports:
            - '3306:3306'
        volumes:
            - 'cc-app-v3-dbdata:/var/lib/mysql'
        command: '--collation-server=utf8mb4_general_ci --character-set-server=utf8mb4'
    redis:
        image: 'redis:alpine'
        ports:
            - '${FORWARD_REDIS_PORT:-6379}:6379'
        volumes:
            - 'sail-redis:/data'
        networks:
            - sail
        healthcheck:
            test:
                - CMD
                - redis-cli
                - ping
            retries: 3
            timeout: 5s
    memcached:
        image: 'memcached:alpine'
        ports:
            - '${FORWARD_MEMCACHED_PORT:-11211}:11211'
        networks:
            - sail
    mailpit:
        image: 'axllent/mailpit:latest'
        ports:
            - '${FORWARD_MAILPIT_PORT:-1025}:1025'
            - '${FORWARD_MAILPIT_DASHBOARD_PORT:-8025}:8025'
        networks:
            - sail
    selenium:
        image: selenium/standalone-chromium
        extra_hosts:
            - 'host.docker.internal:host-gateway'
        volumes:
            - '/dev/shm:/dev/shm'
        networks:
            - sail
    soketi:
        image: 'quay.io/soketi/soketi:latest-16-alpine'
        environment:
            SOKETI_DEBUG: '${SOKETI_DEBUG:-1}'
            SOKETI_METRICS_SERVER_PORT: '9601'
            SOKETI_DEFAULT_APP_ID: '${PUSHER_APP_ID}'
            SOKETI_DEFAULT_APP_KEY: '${PUSHER_APP_KEY}'
            SOKETI_DEFAULT_APP_SECRET: '${PUSHER_APP_SECRET}'
        ports:
            - '${PUSHER_PORT:-6001}:6001'
            - '${PUSHER_METRICS_PORT:-9601}:9601'
        networks:
            - sail
volumes:
    cc-app-v3-dbdata: null
    sail-mysql:
        driver: local
    sail-redis:
        driver: local
