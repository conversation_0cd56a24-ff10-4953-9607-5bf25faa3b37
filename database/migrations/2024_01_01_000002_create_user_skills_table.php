<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_skills', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('title');
            $table->text('description');
            $table->enum('category', [
                'technical', 'creative', 'manual', 'professional', 
                'educational', 'health', 'domestic', 'other'
            ])->default('other');
            $table->enum('experience_level', ['beginner', 'intermediate', 'advanced', 'expert'])->default('intermediate');
            $table->boolean('is_available')->default(true); // Skill availability
            $table->decimal('hourly_rate', 8, 2)->nullable(); // Optional hourly rate
            $table->string('currency', 3)->default('USD'); // Currency code
            $table->boolean('is_free')->default(true); // Whether offered for free
            $table->text('requirements')->nullable(); // Any requirements or materials needed
            $table->timestamps();
            
            $table->index(['user_id', 'is_available']);
            $table->index('category');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_skills');
    }
};
