<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('mobile_app_accounts', function (Blueprint $table) {
            $table->id();
            $table->string('one_signal_id')->unique();
            $table->foreignId('user_id');
            $table->boolean('logged_in')->default(true);
            $table->boolean('notify')->default(true);
            $table->text('data');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('mobile_app_accounts');
    }
};
