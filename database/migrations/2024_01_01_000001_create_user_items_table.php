<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('title');
            $table->text('description')->nullable();
            $table->string('amazon_asin')->nullable(); // Amazon Product ID
            $table->string('amazon_url')->nullable(); // Amazon Product URL
            $table->string('image_url')->nullable(); // Product image URL
            $table->decimal('price', 10, 2)->nullable(); // Product price
            $table->string('currency', 3)->default('USD'); // Currency code
            $table->boolean('is_available')->default(true); // Item availability
            $table->enum('condition', ['new', 'like_new', 'good', 'fair', 'poor'])->default('good');
            $table->text('notes')->nullable(); // Additional notes from user
            $table->timestamps();
            
            $table->index(['user_id', 'is_available']);
            $table->index('amazon_asin');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_items');
    }
};
