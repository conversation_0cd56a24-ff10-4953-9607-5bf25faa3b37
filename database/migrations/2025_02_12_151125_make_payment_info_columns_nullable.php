<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('payment_info', function (Blueprint $table) {
            $table->string('firstname')->nullable()->change();
            $table->string('lastname')->nullable()->change();
            $table->string('email')->nullable()->change();
            $table->string('phone')->nullable()->change();
            $table->text('notes')->nullable()->change();
            $table->enum('payment_type', ['paypal', 'check', 'wire', 'uk_payment'])->nullable()->change();
            $table->string('paypal_email')->nullable()->change();
            $table->string('wire_bank_name')->nullable()->change();
            $table->string('wire_bank_routing')->nullable()->change();
            $table->text('wire_bank_address')->nullable()->change();
            $table->string('wire_acct_number')->nullable()->change();
            $table->string('uk_bank')->nullable()->change();
            $table->string('uk_sortcode')->nullable()->change();
            $table->string('uk_account')->nullable()->change();
            $table->string('uk_account_name')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // If you need to revert, you'd have to specify the *original*
        // non-nullable state of each column here.  This is complex
        // and might require knowing the database schema before the
        // migration was run.  A safer approach might be to create
        // a separate migration to handle reverting.

        // For this example, we'll just log a warning.
        \Log::warning("Cannot reliably revert this migration.  You'll have to manually set the NOT NULL constraints if needed.");
    }
};
