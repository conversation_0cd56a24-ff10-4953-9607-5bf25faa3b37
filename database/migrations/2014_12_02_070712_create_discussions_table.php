<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('discussions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('creator_id');
            $table->string('title', 200);
            $table->enum('status', ['closed', 'open'])->default('open');
            $table->foreignId('group_id');
            $table->timestamps();
            $table->softDeletes();
        });

        Schema::create('discussion_user', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id');
            $table->foreignId('discussion_id');
        });

        Schema::create('posts', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id');
            $table->text('comment');
            $table->morphs('postable');
            $table->timestamps();
        });

        Schema::create('post_user', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id');
            $table->foreignId('post_id');
        });

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('discussions');
        Schema::dropIfExists('discussion_user');
        Schema::dropIfExists('posts');
        Schema::dropIfExists('post_user');
    }
};
