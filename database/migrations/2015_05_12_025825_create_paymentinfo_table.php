<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payment_info', function (Blueprint $table) {
            $table->id();
            $table->string('firstname');
            $table->string('lastname');
            $table->string('email');
            $table->string('phone');
            $table->text('notes');
            $table->enum('payment_type', ['paypal', 'check', 'wire', 'uk_payment']);
            $table->string('paypal_email');
            $table->string('wire_bank_name');
            $table->string('wire_bank_routing');
            $table->text('wire_bank_address');
            $table->string('wire_acct_number');
            $table->string('uk_bank');
            $table->string('uk_sortcode');
            $table->string('uk_account');
            $table->string('uk_account_name');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payment_info');
    }
};
