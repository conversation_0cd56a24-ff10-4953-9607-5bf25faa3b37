<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('recipients', function (Blueprint $table) {
            $table->string('phone')->nullable()->change();
            $table->string('email')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Reverting this migration is complex as it requires knowing the
        // original NOT NULL state.  We'll log a warning and leave it
        // to manual intervention if a true rollback is needed.

        \Log::warning('Cannot reliably revert this migration. Manual intervention is required to restore NOT NULL constraints on phone, created_at, updated_at, and deleted_at if needed.');

        // For a more complete (but still potentially problematic) rollback, you could try:
        // Schema::table('recipients', function (Blueprint $table) {
        //     $table->string('phone')->change(); // This assumes 'phone' was originally NOT NULL
        //     $table->timestamps()->change(); // This assumes timestamps were originally NOT NULL
        //     $table->softDeletes()->change(); // This assumes softDeletes was originally NOT NULL
        // });
        // However, this assumes the original state. If the columns were already nullable, this will cause issues.
    }
};
