<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('users', function (Blueprint $table) {
            $table->id();
            $table->string('email')->unique();
            $table->boolean('email_valid')->default(false);
            $table->timestamp('email_verified_at')->nullable();
            $table->boolean('share_email')->default(1);
            $table->string('phone', 30)->nullable();
            $table->string('password');
            $table->string('firstname');
            $table->string('lastname');
            $table->string('facebook')->nullable();
            $table->string('twitter')->nullable();
            $table->string('blog')->nullable();
            $table->string('token', 40)->nullable()->unique();
            $table->string('profile_pic')->default('/images/logo-cc-blue.png');
            $table->enum('role', ['admin', 'user'])->default('user');
            $table->string('wizard_bookmark')->nullable();
            $table->foreignId('default_group_id')->default(1);
            $table->enum('default_email_notification',
                ['daily_digest', 'daily_per_discussion', 'per_comment', 'none']
            )->default('daily_digest');
            $table->boolean('suspended')->default(false);
            $table->boolean('can_use_mobile_app')->default(false);
            $table->rememberToken();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('users');
    }
};
