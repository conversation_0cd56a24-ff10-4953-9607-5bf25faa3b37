<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('addresses', function (Blueprint $table) {
            $table->string('addressable_type', 255)->nullable()->change();
            $table->unsignedBigInteger('addressable_id')->nullable()->change();
            $table->enum('type', ['primary', 'billing', 'shipping'])->nullable()->default('primary')->change();
            $table->string('addr1', 255)->nullable()->change();
            $table->string('city', 255)->nullable()->change();
            $table->string('state', 255)->nullable()->change();
            $table->string('postcode', 255)->nullable()->change();
            $table->string('country', 255)->nullable()->change();
        });
    }

    public function down(): void
    {
        Schema::table('addresses', function (Blueprint $table) {
            $table->string('addressable_type', 255)->nullable(false)->change();
            $table->unsignedBigInteger('addressable_id')->nullable(false)->change();
            $table->enum('type', ['primary', 'billing', 'shipping'])->nullable(false)->default('primary')->change();
            $table->string('addr1', 255)->nullable(false)->change();
            $table->string('city', 255)->nullable(false)->change();
            $table->string('state', 255)->nullable(false)->change();
            $table->string('postcode', 255)->nullable(false)->change();
            $table->string('country', 255)->nullable(false)->change();
        });
    }
};
