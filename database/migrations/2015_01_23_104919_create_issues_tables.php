<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('issues', function (Blueprint $table) {
            $table->id();
            $table->foreignId('creator_id');
            $table->foreignId('recipient_id');
            $table->foreignId('discussion_id');
            $table->string('title', 200);
            $table->text('description');
            $table->enum('status', ['open', 'closed'])->default('open');
            $table->string('resolution')->nullable();
            $table->foreignId('issue_category_id');
            $table->foreignId('issue_type_id');
            $table->timestamps();
            $table->softDeletes();
        });

        Schema::create('recipients', function (Blueprint $table) {
            $table->id();
            $table->string('firstname');
            $table->string('lastname');
            $table->string('email');
            $table->string('phone');
            $table->timestamps();
            $table->softDeletes();
        });

        Schema::create('issue_categories', function (Blueprint $table) {
            $table->id();
            $table->string('name', 50);
            $table->string('description', 200);
            $table->softDeletes();
        });

        Schema::create('issue_types', function (Blueprint $table) {
            $table->id();
            $table->string('name', 50);
            $table->string('description', 200);
            $table->softDeletes();
        });

        Schema::create('issue_user', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id');
            $table->foreignId('issue_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('issues');
        Schema::dropIfExists('recipients');
        Schema::dropIfExists('issue_categories');
        Schema::dropIfExists('issue_types');
        Schema::dropIfExists('issue_user');
    }
};
