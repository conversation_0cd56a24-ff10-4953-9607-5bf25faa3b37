<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('voting_matrices', function (Blueprint $table) {
            $table->id();
            $table->string('name')->unique();
            $table->softDeletes();
        });
        Schema::create('voting_matrix_rows', function (Blueprint $table) {
            $table->id();
            $table->foreignId('voting_matrix_id');
            $table->integer('funds_percentage_upper')->unsigned();
            $table->integer('num_members_1')->unsigned();
            $table->integer('num_members_2')->unsigned();
            $table->integer('num_members_3')->unsigned();
            $table->integer('num_members_4')->unsigned();
            $table->integer('num_members_5')->unsigned();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('voting_matrices');
        Schema::dropIfExists('voting_matrix_rows');
    }
};
