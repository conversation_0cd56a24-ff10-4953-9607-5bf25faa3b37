<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('group_invites', function (Blueprint $table) {
            $table->id();
            $table->string('token', 40)->unique()->nullable();
            $table->string('email')->nullable();
            $table->foreignId('user_id')->nullable();
            $table->foreignId('group_id');
            $table->foreignId('inviter_id');
            $table->timestamps();
        });

        Schema::create('join_requests', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id');
            $table->foreignId('group_id');
            $table->text('message');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('group_invites');
        Schema::dropIfExists('join_requests');
    }
};
