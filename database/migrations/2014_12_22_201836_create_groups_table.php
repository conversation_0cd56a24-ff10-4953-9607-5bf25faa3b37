<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('groups', function (Blueprint $table) {
            $table->id();
            $table->string('name')->unique();
            $table->string('shortname')->unique()->nullable();
            $table->string('url')->unique()->nullable();
            $table->text('description');
            $table->text('covenant');
            $table->string('city');
            $table->string('state');
            $table->string('postcode');
            $table->string('country');
            $table->string('profile_pic')->default('/images/logo-cc-blue.png');
            $table->enum('type', ['public', 'private', 'secret'])->default('secret');
            $table->enum('inviter', ['owner', 'admin', 'member'])->default('owner');
            $table->integer('min_donation')->default(0);
            $table->enum('donation_frequency',
                ['weekly', 'biweekly', 'monthly', 'one-time',
                    'annually', 'member-choice']
            )->default('member-choice');
            $table->foreignId('voting_matrix_id')->default(1);
            $table->decimal('current_funds', 8, 2)->default(0);
            $table->decimal('shared_funds', 8, 2)->default(0);
            $table->boolean('suspended')->default(false);
            $table->timestamps();
            $table->softDeletes();
        });

        Schema::create('group_user', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id');
            $table->foreignId('group_id');
            $table->enum('role', ['owner', 'admin', 'member'])->default('member');
            $table->enum('email_notification',
                ['use_default', 'daily_digest', 'daily_per_discussion',
                    'per_comment', 'none']
            )->default('use_default');
            $table->boolean('is_absent')->default(false);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('groups');
        Schema::dropIfExists('group_user');
    }
};
