<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('proposals', function (Blueprint $table) {
            $table->id();
            $table->foreignId('creator_id');
            $table->foreignId('issue_id');
            $table->foreignId('payment_info_id');
            $table->decimal('amount', 8, 2);
            $table->dateTime('deadline');
            $table->boolean('deadline_notification')->default(false);
            $table->boolean('vote_passed')->default(false);
            $table->boolean('voting_closed')->default(false);
            $table->enum('payment_status',
                ['awaiting_approval', 'under_review', 'need_payment_info',
                    'processing', 'payment_sent', 'payment_received', 'cancelled']
            )->default('awaiting_approval');
            $table->timestamps();
            $table->softDeletes();
        });

        Schema::create('proposal_votes', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id');
            $table->foreignId('proposal_id');
            $table->enum('vote', ['concur', 'open', 'wonder', 'disfavor']);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('proposals');
        Schema::dropIfExists('proposal_votes');
    }
};
