#!/bin/bash

# Script to build Vite assets for GitHub-based deployment

echo "Installing npm dependencies..."
npm install

echo "Building assets with Vite..."
npm run build

echo "Checking if build was successful..."
if [ -f "public/build/manifest.json" ]; then
    echo "Build successful! Assets are ready for deployment."
    echo "The 'public/build' directory will be included in your GitHub deployment."
else
    echo "Build failed or manifest.json not found."
    echo "Check for errors in the build process."
    exit 1
fi

echo "Done!"
