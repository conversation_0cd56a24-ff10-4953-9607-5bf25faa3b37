/*
Theme Name: Common Change
Theme URI: www.commonchange.com
Description: Theme for the Common Change web application, by Relational Tithe, Inc.
Version: 1.0
Author: <PERSON>
Author URI: www.isaachopper.com
*/

/***************************************************************************************/
/* General body, text, and link styles */
/***************************************************************************************/
body {
	font-family: "Helvetica", helvetica, sans-serif;
	font-size: 100%;
	color: #333;
	background: #F0F0F0 url("/images/bg-body.jpg") repeat-y center top;
	/*background: url("images/dust.png") repeat;*/
	padding: 0px;
	margin: 0px;
}
p, .paragraph {
	text-align: justify;
	vertical-align: text-top;
}
a {
	font-size: 10pt;
	font-weight: bold;
	color: #21351B; /*#9DCA40;*/
	text-decoration: none;	
}
a:hover {text-decoration: underline}

h1 {font-size: 16pt; font-weight: bold;}
h2 {font-size: 12pt; font-weight: bold;}
h3 {font-size: 10pt; font-weight: bold;}

img {border: none;}

.clear {clear:both;}
.noclear {clear:none;}

.inset { text-shadow:#fff 0px 1px 0, #000 0 -1px 0;}

.main {
	width: 300px;
	height: 200px;
	position: absolute;
	left: 50%;
	top: 50%;
	margin-left: -150px;
	margin-top: -100px;
}

#header {
	background: none repeat scroll 0 0 #3f4c6b;
	margin-top: 0;
	min-height: 100px;
	width: 100%;
	font-family: "Helvetica",helvetica,sans-serif;
	color: #fff;
}

#header a{
	color: #fff;
	font-weight: normal;
}

#logo {
	background: url("/images/logo-cc-blue.png") no-repeat scroll 0 0 rgba(0, 0, 0, 0);
	float: left;
	height: 100px;
	margin-left: 10px;
	margin-top: 4px;
	width: 113px;
}

/***************************************************************************************/
/* Menu Styles */
/***************************************************************************************/
#menu-wrapper {
        width: 100%;
        margin-top: 0px;
}

#menu {
        width: 800px;
        margin: 0px auto;
}

#menu li, li a {
        list-style-type: none;
        list-style-position: inside;
        cursor: pointer;
}

#menu p {
        line-height: 30px;
}

#menu li:hover {
        color: #fff;
        background: #3f4c6b; /* Old browsers */
        background: -moz-linear-gradient(top,  #3f4c6b 0%, #6377aa 100%); /* FF3.6+ */
        background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#3f4c6b), color-stop(100%,#6377aa)); /* Chrome,Safari4+ */
        background: -webkit-linear-gradient(top,  #3f4c6b 0%,#6377aa 100%); /* Chrome10+,Safari5.1+ */
        background: -o-linear-gradient(top,  #3f4c6b 0%,#6377aa 100%); /* Opera 11.10+ */
        background: -ms-linear-gradient(top,  #3f4c6b 0%,#6377aa 100%); /* IE10+ */
        background: linear-gradient(to bottom,  #3f4c6b 0%,#6377aa 100%); /* W3C */
        filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#3f4c6b', endColorstr='#6377aa',GradientType=0 ); /* IE6-8 */
}

.menu-tab {
        margin-top: 10px;
}

.menu-tab-off {
        float: left;
        min-width: 90px;
        height: 30px;
        /*background: #3F6231 url("images/bg-tab.jpg") repeat-x;*/
        /*background-color: #334F28;*/
        padding: 1px 2px 2px 0px;
        margin: 0px 1px;
        color: #fff;
        font-size: 10pt;
        font-weight: normal;
        text-align: center;

        /* Gradient */
        background: #6377aa; /* Old browsers */
        background: -moz-linear-gradient(top,  #6377aa 0%, #3f4c6b 100%); /* FF3.6+ */
        background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#6377aa), color-stop(100%,#3f4c6b)); /* Chrome,Safari4+ */
        background: -webkit-linear-gradient(top,  #6377aa 0%,#3f4c6b 100%); /* Chrome10+,Safari5.1+ */
        background: -o-linear-gradient(top,  #6377aa 0%,#3f4c6b 100%); /* Opera 11.10+ */
        background: -ms-linear-gradient(top,  #6377aa 0%,#3f4c6b 100%); /* IE10+ */
        background: linear-gradient(to bottom,  #6377aa 0%,#3f4c6b 100%); /* W3C */
        filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#6377aa', endColorstr='#3f4c6b',GradientType=0 ); /* IE6-8 */

        /* Box Shadow */
        -moz-box-shadow: 0px -2px 3px 0px #333333;
        -webkit-box-shadow: 0px -2px 3px 0px #333333;
        box-shadow: 0px -2px 3px 0px #333333;

        /* Rounded Corners */
        -moz-border-radius: 4px 4px 0px 0px;
        -webkit-border-radius: 4px 4px 0px 0px;
        border-radius: 4px 4px 0px 0px;
}
.menu-tab-off a {color: #fff; font-weight: normal; text-decoration: none;}
.menu-tab-off a:hover {text-decoration: underline;}

.menu-tab-on {
        float: left;
        min-width: 90px;
        height: 30px;
        background: #fff;
        padding: 1px 4px 0px 4px;
        margin: 0px 1px;
        color: #3f4c6b; /*#21351B;*/
        font-size: 10pt;
        font-weight: bold;
        text-align: center;

        /* Box Shadow */
        -moz-box-shadow: 0px -1px 1px 0px #333333;
        -webkit-box-shadow: 0px -1px 1px 0px #333333;
        box-shadow: 0px -1px 1px 0px #333333;

        /* Rounded Corners */
        -moz-border-radius: 4px 4px 0px 0px;
        -webkit-border-radius: 4px 4px 0px 0px;
        border-radius: 4px 4px 0px 0px;
}
.menu-tab-on a {color: #21351B; font-weight: normal; text-decoration: none;}
.menu-tab-on a:hover {text-decoration: underline;}

#menu-account {
        float: left;
        width: 125px;
        margin-top: 8px;
        margin-left: 75px;
        color: #fff;
        font-size: 10pt;
        font-weight: normal;
        text-align: center;
}
#menu-account a, #menu-account a:visited {color: #fff; font-weight: normal; text-decoration: underline;}


#menu-welcome {
        float: right;
        margin-top: 5px;
        margin-right: 100px;
        color: #fff;
        font-size: 10pt;
        font-weight: normal;
        text-align: right;
}
#menu-welcome a {color: #fff; font-weight: normal;}

.link-account {text-decoration: underline;}
.link-account:hover {text-decoration: none;}


/**********************/
#bg {
        width: 100%;
        /*background: url("images/subtle_dots.png") repeat-xy;*/
        margin-top: 0px;
        margin-bottom: 0px;
}

#main {
        width:800px;
        min-height: 400px;
        margin: 0px auto;
        clear: both;
}

/***************************************************************************************/
/* Band List & Font Styles */
/***************************************************************************************/
@font-face {
	font-family: Yanone;
	src: url('fonts/YanoneKaffeesatz-Regular.ttf');
}

.font-dark {}
.font-light {}
.text-tiny {font-size: 7pt;}
.text-small {font-size: 8pt;}
.text-med {font-size: 10pt;}
.text-big {font-size: 12pt;}
.text-huge {font-size: 16pt;}
.text-lime {color: #3f4c6b;}
.text-blue {color: #3f4c6b;}
.underline {text-decoration: underline;}
.bold {font-weight: bold;}
.normal {font-weight: normal;}

.text-footer {
	color: #999;
	text-shadow: 0px -1px 1px #fff;
	filter: dropshadow(color=#fff, offx=-1, offy=-1); 
}
