/* V2 Account Pages Styling */
:root {
    --primary-purple: #5144A1;
    --primary-navy: #4D5E80;
    --light-white: #FFFFFF;
    --light-lavender: #F0F0F9;
    --light-gray: #F8F9FA;
    --border-gray: #E5E7EB;
    --text-gray: #6B7280;
    --success-green: #10B981;
    --error-red: #EF4444;
}

/* Account Container */
.v2-account-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Account Header */
.v2-account-header {
    margin-bottom: 30px;
}

.v2-account-title {
    font-size: 28px;
    font-weight: 700;
    color: var(--primary-navy);
    margin: 0;
}

/* Content Area */
.v2-account-content {
    background: var(--light-white);
    border-radius: 8px;
    padding: 30px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Form Styling */
.v2-form-group {
    margin-bottom: 25px;
}

.v2-form-label {
    display: block;
    font-weight: 600;
    color: var(--primary-navy);
    margin-bottom: 8px;
    font-size: 14px;
}

.v2-form-input {
    width: 100%;
    max-width: 400px;
    padding: 12px 16px;
    border: 1px solid var(--border-gray);
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.2s ease;
}

.v2-form-input:focus {
    outline: none;
    border-color: var(--primary-purple);
    box-shadow: 0 0 0 3px rgba(81, 68, 161, 0.1);
}

.v2-form-select {
    width: 100%;
    max-width: 400px;
    padding: 12px 16px;
    border: 1px solid var(--border-gray);
    border-radius: 6px;
    font-size: 14px;
    background-color: var(--light-white);
    transition: border-color 0.2s ease;
}

.v2-form-select:focus {
    outline: none;
    border-color: var(--primary-purple);
    box-shadow: 0 0 0 3px rgba(81, 68, 161, 0.1);
}

/* Checkbox Styling */
.v2-checkbox-group {
    display: flex;
    align-items: center;
    gap: 10px;
}

.v2-checkbox {
    width: 18px;
    height: 18px;
    accent-color: var(--primary-purple);
}

.v2-checkbox-label {
    font-weight: 500;
    color: var(--primary-navy);
    margin: 0;
    cursor: pointer;
}

/* Button Styling */
.v2-btn {
    display: inline-block;
    padding: 12px 24px;
    border-radius: 6px;
    font-weight: 600;
    text-decoration: none;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 14px;
}

.v2-btn-primary {
    background-color: var(--primary-purple);
    color: var(--light-white);
}

.v2-btn-primary:hover {
    background-color: #4338A1;
    color: var(--light-white);
    text-decoration: none;
}

.v2-btn-secondary {
    background-color: var(--light-gray);
    color: var(--primary-navy);
    border: 1px solid var(--border-gray);
}

.v2-btn-secondary:hover {
    background-color: var(--border-gray);
    color: var(--primary-navy);
    text-decoration: none;
}

/* Profile Picture Section */
.v2-profile-pic-section {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 25px;
}

.v2-profile-pic-current {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid var(--border-gray);
}

.v2-profile-pic-placeholder {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background-color: var(--light-gray);
    border: 3px solid var(--border-gray);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-gray);
    font-size: 24px;
}

/* Alert Messages */
.v2-alert {
    padding: 12px 16px;
    border-radius: 6px;
    margin-bottom: 20px;
    font-weight: 500;
}

.v2-alert-success {
    background-color: #D1FAE5;
    color: #065F46;
    border: 1px solid #A7F3D0;
}

.v2-alert-error {
    background-color: #FEE2E2;
    color: #991B1B;
    border: 1px solid #FECACA;
}

/* Error Messages */
.v2-error-message {
    color: var(--error-red);
    font-size: 12px;
    margin-top: 4px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .v2-account-content {
        padding: 20px;
    }

    .v2-form-input,
    .v2-form-select {
        max-width: 100%;
    }
}
