/* v2 Requests Page Styles - Figma inspired */

.v2-requests-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  background: #fff;
  border-radius: 8px;
  padding: 0;
  max-width: 1200px;
  margin: 0 auto;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.v2-requests-header {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 25px 30px;
  border-bottom: 2px solid #F5F6F7;
  background: #fff;
}

.v2-requests-title {
  font-family: 'Inter', sans-serif;
  font-weight: 600;
  font-size: 24px;
  color: #5144A1;
}

.v2-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px 24px;
  font-family: 'Inter', sans-serif;
  font-weight: 700;
  font-size: 14px;
  border-radius: 8px;
  text-decoration: none;
  cursor: pointer;
  transition: background 0.2s, color 0.2s;
}
.v2-btn-primary {
  background: #5144A1;
  color: #fff;
  border: none;
}
.v2-btn-primary:hover {
  background: #3d3380;
}
.v2-btn-outline {
  background: #fff;
  color: #5144A1;
  border: 1px solid #5144A1;
  margin-top: 16px;
}
.v2-btn-outline:hover {
  background: #f5f6f7;
}

.v2-requests-list {
  width: 100%;
  padding: 36px;
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.v2-request-card {
  background: #fff;
  border: 2px solid #F5F6F7;
  box-shadow: 0 2px 5px rgba(38, 51, 77, 0.03);
  border-radius: 5px;
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  /* max-width: 740px; */
  width: 100%;
}

.v2-request-card-header {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 20px;
}
.v2-request-avatar img {
  width: 50px;
  height: 50px;
  border-radius: 5px;
  object-fit: cover;
}
.v2-request-meta {
  display: flex;
  flex-direction: column;
  gap: 6px;
  flex: 1;
}
.v2-request-title {
  font-family: 'Inter', sans-serif;
  font-weight: 600;
  font-size: 20px;
  color: #4D5E80;
}
.v2-request-by {
  font-family: 'Inter', sans-serif;
  font-weight: 700;
  font-size: 14px;
  color: #8347CC;
  display: flex;
  gap: 8px;
  align-items: center;
}
.v2-request-author {
  color: #4D5E80;
  font-weight: 700;
}
.v2-request-group {
  color: #4D5E80;
  font-weight: 400;
}
.v2-request-time {
  color: #7D8FB3;
  font-weight: 500;
  font-size: 14px;
}
.v2-request-fav {
  margin-left: auto;
  color: #9ACB48;
  font-size: 24px;
  cursor: pointer;
}

.v2-request-card-body {
  display: flex;
  flex-direction: column;
  gap: 16px;
}
.v2-request-amount {
  font-family: 'Inter', sans-serif;
  font-weight: 700;
  font-size: 16px;
  color: #7D8FB3;
  margin-bottom: 8px;
}
.v2-request-description {
  font-family: 'Inter', sans-serif;
  font-weight: 400;
  font-size: 16px;
  color: #4D5E80;
  margin-bottom: 8px;
}
.v2-request-actions {
  display: flex;
  flex-direction: row;
  gap: 15px;
  margin-bottom: 8px;
}
.v2-action-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: 8px;
  border: 1px solid #F7F8FD;
  background: #fff;
  color: #7D8FB3;
  font-family: 'Inter', sans-serif;
  font-weight: 700;
  font-size: 14px;
  cursor: pointer;
  transition: background 0.2s, color 0.2s;
}
.v2-action-btn.active, .v2-action-btn:hover {
  background: rgba(154, 203, 72, 0.05);
  color: #9ACB48;
  border: 1px solid #9ACB48;
}
.v2-action-concur.active, .v2-action-concur:hover {
  background: rgba(154, 203, 72, 0.05);
  color: #9ACB48;
  border: 1px solid #9ACB48;
}

.v2-request-comments {
  background: rgba(43, 62, 80, 0.05);
  border-radius: 5px;
  padding: 20px;
  margin-top: 20px;
  width: 100%;
  /* max-width: 739px; */
}
.v2-comment-form {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 10px;
  background: #fff;
  border: 2px solid #F5F6F7;
  border-radius: 5px;
  padding: 10px 20px;
  margin-bottom: 15px;
}
.v2-comment-input {
  flex: 1;
  border: none;
  outline: none;
  font-family: 'Inter', sans-serif;
  font-size: 16px;
  color: #7D8FB3;
  background: transparent;
}
.v2-comment-send {
  background: #5144A1;
  color: #fff;
  border: none;
  border-radius: 5px;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}
.v2-comment-item {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  gap: 15px;
  margin-bottom: 10px;
}
.v2-comment-avatar img {
  width: 30px;
  height: 30px;
  border-radius: 5px;
  object-fit: cover;
}
.v2-comment-content {
  display: flex;
  flex-direction: column;
  gap: 2px;
}
.v2-comment-author {
  font-family: 'Inter', sans-serif;
  font-weight: 700;
  font-size: 12px;
  color: #4D5E80;
}
.v2-comment-time {
  font-family: 'Inter', sans-serif;
  font-weight: 400;
  font-size: 12px;
  color: #7D8FB3;
}
.v2-comment-text {
  font-family: 'Inter', sans-serif;
  font-weight: 400;
  font-size: 14px;
  color: #4D5E80;
}

.v2-empty-state {
  text-align: center;
  color: #7D8FB3;
  font-family: 'Inter', sans-serif;
  font-size: 18px;
  margin: 60px auto;
}

.v2-pagination {
  margin-top: 32px;
  display: flex;
  justify-content: center;
}

.v2-requests-topbar {
  width: 100%;
  max-width: 100%;
  background: #F7F8FA;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  padding: 24px 30px;
  gap: 32px;
  border-radius: 8px;
  min-height: 60px;
  position: relative;
  margin-bottom: 20px;
  border: 1px solid #E5E7EB;
}
.v2-requests-tabs {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 16px;
}
.v2-requests-tab {
  font-family: 'Inter', sans-serif;
  font-weight: 600;
  font-size: 18px;
  color: #4D5E80;
  text-decoration: none;
  opacity: 0.7;
  transition: color 0.2s, opacity 0.2s;
}
.v2-requests-tab.active {
  color: #5144A1;
  opacity: 1;
}
.v2-requests-tab-sep {
  color: #7D8FB3;
  font-size: 18px;
  margin: 0 8px;
  opacity: 0.5;
}
.v2-requests-topbar .v2-btn {
  margin-left: auto;
  margin-right: 0;
  font-size: 14px;
  padding: 10px 20px;
  background: #5144A1;
  color: #fff;
  border-radius: 6px;
  font-weight: 600;
  box-shadow: none;
  border: none;
}
.v2-requests-topbar .v2-btn:hover {
  background: #3d3380;
}
.v2-requests-topbar .v2-btn.v2-btn-primary {
  background: #5144A1;
  border-radius: 6px;
  border: none;
  color: #fff;
  font-family: 'Inter', sans-serif;
  font-weight: 600;
  font-size: 14px;
  padding: 10px 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  text-decoration: none;
  box-shadow: none;
  margin-left: auto;
  margin-right: 0;
  cursor: pointer;
  transition: background 0.2s, color 0.2s;
}
.v2-requests-topbar .v2-btn.v2-btn-primary:hover,
.v2-requests-topbar .v2-btn.v2-btn-primary:focus {
  background: #3d3380;
  color: #fff;
  text-decoration: none;
  outline: none;
}

@media (max-width: 900px) {
  .v2-requests-container, .v2-requests-list, .v2-request-card {
    max-width: 100%;
    padding: 12px;
  }
  .v2-request-card {
    padding: 12px;
  }
  .v2-requests-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
    padding: 16px 12px;
  }
  .v2-requests-topbar {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
    padding: 16px 20px;
  }
  .v2-requests-topbar .v2-btn {
    margin: 16px 0 0 0;
    width: auto;
    font-size: 14px;
    padding: 10px 20px;
  }
  .v2-requests-topbar .v2-btn.v2-btn-primary {
    width: auto;
    margin: 16px 0 0 0;
    font-size: 14px;
    padding: 10px 20px;
  }
}

@media (max-width: 600px) {
  .v2-requests-header, .v2-requests-list, .v2-request-card, .v2-request-comments {
    padding: 8px !important;
  }
  .v2-request-card {
    gap: 8px;
  }
  .v2-request-card-header {
    gap: 8px;
  }
  .v2-request-title {
    font-size: 16px;
  }
  .v2-request-amount, .v2-request-description {
    font-size: 14px;
  }
  .v2-btn, .v2-btn-primary, .v2-btn-outline {
    font-size: 12px;
    padding: 8px 12px;
  }
  .v2-requests-topbar {
    padding: 12px 16px;
    min-height: 50px;
  }
  .v2-requests-tabs {
    gap: 8px;
  }
  .v2-requests-tab, .v2-requests-tab-sep {
    font-size: 14px;
  }
  .v2-requests-topbar .v2-btn {
    font-size: 12px;
    padding: 8px 12px;
    margin-right: 0;
  }
  .v2-requests-topbar .v2-btn.v2-btn-primary {
    font-size: 12px;
    padding: 8px 12px;
    margin-right: 0;
  }
}