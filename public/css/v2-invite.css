/* V2 Invite Friends Styles */

/* Override account container for invite page */
.v2-account-container .v2-account-content {
    max-width: 800px;
}

/* <PERSON>ert Styles */
.v2-alert {
    padding: 16px 20px;
    border-radius: 8px;
    margin-bottom: 24px;
    font-size: 14px;
    line-height: 1.5;
}

.v2-alert-success {
    background: #d1fae5;
    border: 1px solid #a7f3d0;
    color: #065f46;
}

.v2-alert-error {
    background: #fee2e2;
    border: 1px solid #fca5a5;
    color: #991b1b;
}

.v2-error-list {
    margin: 0;
    padding: 0;
    list-style: none;
}

.v2-error-list li {
    margin-bottom: 4px;
}

.v2-error-list li:last-child {
    margin-bottom: 0;
}

/* Form Styles */
.v2-invite-form {
    display: flex;
    flex-direction: column;
    gap: 32px;
}

/* Radio Button Section */
.v2-invite-type-section {
    margin-bottom: 8px;
}

.v2-radio-group {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.v2-radio-option {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    padding: 16px;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    background: #ffffff;
}

.v2-radio-option:hover {
    border-color: #d1d5db;
    background: #f9fafb;
}

.v2-radio-option.checked {
    border-color: #10b981;
    background: #ecfdf5;
}

.v2-radio-input {
    display: none;
}

.v2-radio-label {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    cursor: pointer;
    flex: 1;
    margin: 0;
    font-weight: 400;
}

.v2-radio-circle {
    width: 20px;
    height: 20px;
    border: 2px solid #d1d5db;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    transition: all 0.2s ease;
    margin-top: 2px;
}

.v2-radio-option.checked .v2-radio-circle {
    border-color: #10b981;
    background: #10b981;
}

.v2-radio-option.checked .v2-radio-circle::after {
    content: '';
    width: 8px;
    height: 8px;
    background: white;
    border-radius: 50%;
}

.v2-radio-text {
    font-size: 16px;
    color: #374151;
    line-height: 1.5;
}

.v2-group-name {
    font-size: 14px;
    color: #6b7280;
    margin-top: 4px;
    margin-left: 32px;
}

/* People Section */
.v2-people-section {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.v2-emails-container {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.v2-email-row {
    display: flex;
    flex-direction: column;
}

.v2-input-group {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr auto;
    gap: 12px;
    align-items: center;
}

.v2-input {
    padding: 12px 16px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 14px;
    color: #374151;
    background: #ffffff;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.v2-input:focus {
    outline: none;
    border-color: #10b981;
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

.v2-input::placeholder {
    color: #9ca3af;
}

.v2-remove-btn {
    width: 36px;
    height: 36px;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    background: #ffffff;
    color: #6b7280;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    flex-shrink: 0;
}

.v2-remove-btn:hover {
    background: #fee2e2;
    border-color: #fca5a5;
    color: #dc2626;
}

.v2-add-more-btn {
    background: none;
    border: none;
    color: #7c3aed;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    padding: 8px 0;
    text-align: left;
    transition: color 0.2s ease;
}

.v2-add-more-btn:hover {
    color: #5b21b6;
}

/* Message Section */
.v2-message-section {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.v2-message-label {
    font-size: 14px;
    color: #374151;
    line-height: 1.5;
    margin: 0;
}

.v2-message-textarea {
    padding: 12px 16px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 14px;
    color: #374151;
    background: #ffffff;
    resize: vertical;
    min-height: 120px;
    font-family: inherit;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.v2-message-textarea:focus {
    outline: none;
    border-color: #10b981;
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

.v2-message-textarea::placeholder {
    color: #9ca3af;
}

/* Action Buttons */
.v2-actions-section {
    display: flex;
    justify-content: flex-start;
    gap: 12px;
    padding-top: 8px;
}

.v2-btn {
    padding: 12px 24px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    border: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
}

.v2-btn-primary {
    background: #7c3aed;
    color: white;
}

.v2-btn-primary:hover {
    background: #6d28d9;
}

.v2-btn-primary:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(124, 58, 237, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
    .v2-invite-container {
        padding: 20px 16px;
    }

    .v2-invite-title {
        font-size: 24px;
        margin-bottom: 32px;
    }

    .v2-input-group {
        grid-template-columns: 1fr;
        gap: 8px;
    }

    .v2-remove-btn {
        justify-self: end;
        margin-top: -8px;
    }

    .v2-radio-option {
        padding: 12px;
    }

    .v2-group-name {
        margin-left: 32px;
    }
}

@media (max-width: 480px) {
    .v2-invite-container {
        padding: 16px 12px;
    }

    .v2-invite-title {
        font-size: 20px;
        margin-bottom: 24px;
    }

    .v2-invite-form {
        gap: 24px;
    }
}
