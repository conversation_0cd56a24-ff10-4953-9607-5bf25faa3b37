.combobox-container {
  margin-bottom: 5px;
  *zoom: 1;
}
.combobox-container:before,
.combobox-container:after {
  display: table;
  content: "";
}
.combobox-container:after {
  clear: both;
}
.combobox-container input,
.combobox-container .uneditable-input {
  -webkit-border-radius: 0 3px 3px 0;
  -moz-border-radius: 0 3px 3px 0;
  border-radius: 0 3px 3px 0;
}
.combobox-container input:focus,
.combobox-container .uneditable-input:focus {
  position: relative;
  z-index: 2;
}
.combobox-container .uneditable-input {
  border-left-color: #ccc;
}
.combobox-container .add-on {
  float: left;
  display: inline-block;
  width: auto;
  min-width: 16px;
  height: inherit !important;
  margin-right: -1px;
  padding: 4px 5px;
  font-weight: normal;
  color: #999999;
  text-align: center;
  text-shadow: 0 1px 0 #ffffff;
  background-color: #f5f5f5;
  border: 1px solid #ccc;
  -webkit-border-radius: 3px 0 0 3px;
  -moz-border-radius: 3px 0 0 3px;
  border-radius: 3px 0 0 3px;
  
}
.combobox-container .active {
  background-color: #a9dba9;
  border-color: #46a546;
}
.combobox-container input,
.combobox-container .uneditable-input {
  float: left;
  -webkit-border-radius: 3px 0 0 3px;
  -moz-border-radius: 3px 0 0 3px;
  border-radius: 3px 0 0 3px;
}
.combobox-container .uneditable-input {
  border-left-color: #eee;
  border-right-color: #ccc;
}
.combobox-container .add-on {
  margin-right: 0;
  margin-left: -1px;
  -webkit-border-radius: 0 3px 3px 0;
  -moz-border-radius: 0 3px 3px 0;
  border-radius: 0 3px 3px 0;
}
.combobox-container input:first-child {
  *margin-left: -160px;
}
.combobox-container input:first-child + .add-on {
  *margin-left: -21px;
}
.combobox-container select {
  display: inline-block;
  width: 0;
  height: 0;
  border: 0;
  padding: 0;
  margin: 0;
  text-indent: -99999px;
  *text-indent: 0;
}
.form-search .combobox-container,
.form-inline .combobox-container {
  display: inline-block;
  margin-bottom: 0;
  vertical-align: top;
}
.form-search .combobox-container .add-on,
.form-inline .combobox-container .add-on {
  vertical-align: middle;
}
.combobox-selected .combobox-clear {
  display: inline-block;
}
.combobox-selected .caret {
  display: none;
}
.combobox-clear {
  display: none;
  width: 14px;
  height: 14px;
  line-height: 14px;
  vertical-align: top;
  opacity: 0.3;
  filter: alpha(opacity=30);
}
.dropdown:hover .combobox-clear,
.open.dropdown .combobox-clear {
  opacity: 1;
  filter: alpha(opacity=100);
}
.btn .combobox-clear {
  margin-top: 1px;
  margin-left: 1px;
}
.btn:hover .combobox-clear,
.open.btn-group .combobox-clear {
  opacity: 1;
  filter: alpha(opacity=100);
}
.typeahead-long {
  max-height: 300px;
  overflow-y: auto;
}
.control-group.error .combobox-container .add-on {
  color: #B94A48;
  border-color: #B94A48;
}
.control-group.error .combobox-container .caret {
  border-top-color: #B94A48;
}
.control-group.warning .combobox-container .add-on {
  color: #C09853;
  border-color: #C09853;
}
.control-group.warning .combobox-container .caret {
  border-top-color: #C09853;
}
.control-group.success .combobox-container .add-on {
  color: #468847;
  border-color: #468847;
}
.control-group.success .combobox-container .caret {
  border-top-color: #468847;
}
.btn .combobox-clear [class^="icon-"] {
  line-height: 1.4em;
}