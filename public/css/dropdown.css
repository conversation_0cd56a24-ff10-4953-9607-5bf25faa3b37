@font-face {
	font-family: 'OpenSans-Bold';
	src: url('../fonts/OpenSans-Bold.eot'); /* IE9 Compat Modes */
	src: url('../fonts/OpenSans-Bold.eot?#iefix') format('embedded-opentype'), /* IE6-IE8 */  url('../fonts/OpenSans-Bold.woff2') format('woff2'), /* Super Modern Browsers */  url('../fonts/OpenSans-Bold.woff') format('woff'), /* Pretty Modern Browsers */  url('../fonts/OpenSans-Bold.ttf') format('truetype'), /* Safari, Android, iOS */  url('../fonts/OpenSans-Bold.svg#svgFontName') format('svg'); /* Legacy iOS */
}
@font-face {
	font-family: 'OpenSans-Regular';
	src: url('../fonts/OpenSans-Regular.eot'); /* IE9 Compat Modes */
	src: url('../fonts/OpenSans-Regular.eot?#iefix') format('embedded-opentype'), /* IE6-IE8 */  url('../fonts/OpenSans-Regular.woff2') format('woff2'), /* Super Modern Browsers */  url('../fonts/OpenSans-Regular.woff') format('woff'), /* Pretty Modern Browsers */  url('../fonts/OpenSans-Regular.ttf') format('truetype'), /* Safari, Android, iOS */  url('../fonts/OpenSans-Regular.svg#svgFontName') format('svg'); /* Legacy iOS */
}
@font-face {
	font-family: 'OpenSans-Semibold';
	src: url('../fonts/OpenSans-Semibold.eot'); /* IE9 Compat Modes */
	src: url('../fonts/OpenSans-Semibold.eot?#iefix') format('embedded-opentype'), /* IE6-IE8 */  url('../fonts/OpenSans-Semibold.woff2') format('woff2'), /* Super Modern Browsers */  url('../fonts/OpenSans-Semibold.woff') format('woff'), /* Pretty Modern Browsers */  url('../fonts/OpenSans-Semibold.ttf') format('truetype'), /* Safari, Android, iOS */  url('../fonts/OpenSans-Semibold.svg#svgFontName') format('svg'); /* Legacy iOS */
}
@font-face {
	font-family: 'OpenSans-Light';
	src: url('../fonts/OpenSans-Light.eot'); /* IE9 Compat Modes */
	src: url('../fonts/OpenSans-Light.eot?#iefix') format('embedded-opentype'), /* IE6-IE8 */  url('../fonts/OpenSans-Light.woff2') format('woff2'), /* Super Modern Browsers */  url('../fonts/OpenSans-Light.woff') format('woff'), /* Pretty Modern Browsers */  url('../fonts/OpenSans-Light.ttf') format('truetype'), /* Safari, Android, iOS */  url('../fonts/OpenSans-Light.svg#svgFontName') format('svg'); /* Legacy iOS */
}
.heapBox {
	clear: both;
	display: inline-block;
	line-height: 35px;
	position: relative;
}
.heapBox * {
	margin: 0;
	padding: 0;
	outline: none;
}
.heapBox.disabled .holder {
	color: #989898;
}
.heapBox.disabled .handler {
	background: url(../gfx/heapbox_bg.jpg) repeat-x;
}
.heapBox a {
	text-decoration: none;
	color: #000;
}

headbox2 .holder{	width: 210px !important;}
.heapBox .holder {
	width: 130px;
	overflow: hidden;
	background: #fff;
	text-indent: 20px;
	border: 1px solid #d3d3d3;
	border-right: none;
	height: 34px;
	color: #ccc;
	border-top-left-radius: 13px;
	border-bottom-left-radius: 13px;
}
.heapBox .handler {
	width: 37px;
	background: url(../images/arrow1.png);
	height: 34px;
	border-top-right-radius: 13px;
	border-bottom-right-radius: 13px;
	border-left: none;
}
.heapBox .holder, .heapBox .handler {
	float: left;
	position: relative;
	z-index: 10;
	font-family: 'OpenSans-Regular';
}
.heapBox div.heap {
	display: none;
	position: absolute;
	list-style-type: none;
	left: 0;
	width: 166px;
	background: #01cbb5 !important;
	top: 36px;
	z-index: 999;
	border: 1px solid #ccc;
	color: fff;
	overflow: hidden;
	border-radius: 13px;
	border-top: none;
}
.heapBox .heap .heapOptions {
	display: block;
	overflow: hidden;
	z-index: 20;
	position: relative;
}
.heapBox .heap a.sliderUp {
	display: block;
	width: 100%;
	height: 15px;
	background: #333 url(../gfx/slider_arrow_up.png);
	background-repeat: no-repeat;
	background-position: center center;
	border-bottom: 1px solid #111;
	position: absolute;
	top: 0px;
	z-index: 30;
}
.heapBox .heap a.sliderDown {
	display: block;
	width: 100%;
	height: 15px;
	background: #333 url(../gfx/slider_arrow_down.png);
	background-repeat: no-repeat;
	background-position: center center;
	border-top: 1px solid #111;
	position: absolute;
	bottom: 0px;
	z-index: 30;
	font-family: 'OpenSans-Regular';
}
.heapBox .heap .heapOptions .heapOption {
	z-index: 20;
	position: relative;
	display: block;
	height: 35px;
}
.heapBox .heap .heapOptions .heapOption a {
	width: 100%;
	color:#fff;
	display: block;
	border-bottom: 1px solid #029d8c;
	text-indent: 20px;
	font-family: 'OpenSans-Regular';
}
.heapBox .heap .heapOptions .heapOption a:hover {
	background: url(../gfx/heapbox_heapitem_hover_bg.jpg) repeat-x;
}
.heapBox .heap .heapOptions .heapOption a.selected {
	background: url(../gfx/heapbox_heapitem_hover_bg.jpg) repeat-x;
}
.heapBox .heap .heapOptions .heapOption a.disabled {
	color: #777;
}
.heapBox .heap .heapOptions .heapOption a.disabled:hover {
	background: url(../gfx/heapbox_heapitem_bg.jpg) repeat-x;
}
@media all and (max-width:1024px){
	.heapBox .holder {
width: 125px !important;
}
.heapBox div.heap {
width: 161px!important;
}
}
@media all and (max-width:320px){
.heapBox div.heap {width: 162px!important;}

}