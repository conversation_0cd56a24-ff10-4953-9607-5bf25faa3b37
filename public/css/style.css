body{overflow-x:hidden;}
@font-face {
  font-family:'OpenSans-Bold';
  src: url('../fonts/OpenSans-Bold.eot'); /* IE9 Compat Modes */
  src: url('../fonts/OpenSans-Bold.eot?#iefix') format('embedded-opentype'), /* IE6-IE8 */
       url('../fonts/OpenSans-Bold.woff2') format('woff2'), /* Super Modern Browsers */
       url('../fonts/OpenSans-Bold.woff') format('woff'), /* Pretty Modern Browsers */
       url('../fonts/OpenSans-Bold.ttf')  format('truetype'), /* Safari, Android, iOS */
       url('../fonts/OpenSans-Bold.svg#svgFontName') format('svg'); /* Legacy iOS */
}
@font-face {
  font-family:'OpenSans-Regular';
  src: url('../fonts/OpenSans-Regular.eot'); /* IE9 Compat Modes */
  src: url('../fonts/OpenSans-Regular.eot?#iefix') format('embedded-opentype'), /* IE6-IE8 */
       url('../fonts/OpenSans-Regular.woff2') format('woff2'), /* Super Modern Browsers */
       url('../fonts/OpenSans-Regular.woff') format('woff'), /* Pretty Modern Browsers */
       url('../fonts/OpenSans-Regular.ttf')  format('truetype'), /* Safari, Android, iOS */
       url('../fonts/OpenSans-Regular.svg#svgFontName') format('svg'); /* Legacy iOS */
}

@font-face {
  font-family:'OpenSans-Semibold';
  src: url('../fonts/OpenSans-Semibold.eot'); /* IE9 Compat Modes */
  src: url('../fonts/OpenSans-Semibold.eot?#iefix') format('embedded-opentype'), /* IE6-IE8 */
       url('../fonts/OpenSans-Semibold.woff2') format('woff2'), /* Super Modern Browsers */
       url('../fonts/OpenSans-Semibold.woff') format('woff'), /* Pretty Modern Browsers */
       url('../fonts/OpenSans-Semibold.ttf')  format('truetype'), /* Safari, Android, iOS */
       url('../fonts/OpenSans-Semibold.svg#svgFontName') format('svg'); /* Legacy iOS */
}
@font-face {
  font-family:'OpenSans-Light';
  src: url('../fonts/OpenSans-Light.eot'); /* IE9 Compat Modes */
  src: url('../fonts/OpenSans-Light.eot?#iefix') format('embedded-opentype'), /* IE6-IE8 */
       url('../fonts/OpenSans-Light.woff2') format('woff2'), /* Super Modern Browsers */
       url('../fonts/OpenSans-Light.woff') format('woff'), /* Pretty Modern Browsers */
       url('../fonts/OpenSans-Light.ttf')  format('truetype'), /* Safari, Android, iOS */
       url('../fonts/OpenSans-Light.svg#svgFontName') format('svg'); /* Legacy iOS */
}

.datetimepicker-hours { display:none !important;}
.datetimepicker-minutes{ display:none !important;}
.container{max-width:1020px;}
.no-padding{padding:0;}
.no-margin{margin:0;}
.no-paddingleft{padding-left:0;}
.no-paddingright{padding-right:0;}
hr{border-top: 1px solid #cccccc;margin-top: 10px;margin-bottom: 10px;}

/*====Header Css Start=====*/
#header{background-color:#2b3e50; border-color:#2b3e50;}
.signout-content{padding:20px 0 0; text-align:right;}
.signout-content span{color:#fff; font-size:14px; font-family:'OpenSans-Regular'; padding-right:20px;}
.signout-content a{color:#01cbb5; font-size:14px; font-family:'OpenSans-Bold';}
/*====Header Css End=====*/


/*====Menu Css Start=====*/
.topmenu-bg{background-color:#fff; border-color:#fff; margin:0;}
.topmenu-bg .cust-nav ul li a{color:#2b3e50 !important; background-color:#fff !important; font-size:14px; padding:10px 17px; text-transform:uppercase; font-family:'OpenSans-Bold';}
.topmenu-bg .cust-nav ul li a:hover,
.topmenu-bg .cust-nav ul li a:active,
.topmenu-bg .cust-nav ul li a:focus
{border-bottom:3px solid #2b3e50;}
.topmenu-bg .cust-nav ul li.active a
{border-bottom:3px solid #2b3e50;}

.topmenu-bg .cust-nav ul li a span{display:inline-block; padding:0 5px 0 0; height:20px;}
.topmenu-bg .cust-nav ul li a cite{color:#ff0000; font-style:normal;}
/*====Menu Css End=====*/
/*====HOME PAGE=====*/
.home-content blockquote{border-left: 5px solid #63BDCC;font-family:'OpenSans-Regular'}
.sign-border{border:1px solid #63BDCC;}
.sign-border span{ margin-bottom:5px;}
.welcome-content blockquote{ padding:0px 20px; font-size:16px; border:none;font-family:'OpenSans-Regular'}
.sign-text {margin-bottom: 8px; margin-left: 10px; margin-top: 5px;}
.sign-text a{ font-size:14px; font-family:'OpenSans-Regular'; color:#000; }
.sign-text a:hover{color:#63BDCC;}
.sign-text span{ font-size:14px; font-family:'OpenSans-Regular';}
.larg-text p{ font-size:48px;font-family:'OpenSans-Regular'; text-align:center; padding:12px 0;}
.footer-text{text-align:center; margin-bottom:5px;}
.footer-text span a{ font-size:12px;font-family:'OpenSans-Regular'; padding:0 5px; color:#63BDCC;}
.footer-text span{ font-size:12px;font-family:'OpenSans-Regular';}

/*==== End OF HOME PAGE=====*/
/*==== Start thank you====*/
.thank-content span{font-size: 24px;color: #21507c;font-family: 'OpenSans-Semibold';display: block;margin: 60px 0 10px 0;}
.thank-content p{font-family:'OpenSans-Regular'; }
.bgthankyou-pedding{padding: 0 0 740px 0;}
/*====End of thankyou page=====*/
/*====Main Content Css Start=====*/
#main-bg{background-color:#ececec;}
.bg-pedding{ padding:0 0 79px 0;}
.home-content span{font-size:24px; color:#21507c; font-family:'OpenSans-Semibold'; display:block; margin:20px 0;}
.input-margin{ margin-bottom:15px;}
.but-margin { margin:0 10px 0 0;}
.but-margin2 {margin: 0 -45px 0px -29px;}
.welcome-color h4{color:#21507c !important; margin-bottom:10px !important;}

/* ------- BG ADDITIONS ----------- */

.btn{font-size:14px; font-family:'OpenSans-Bold'; border-radius:10px;}

/* ------- End BG ADDITIONS ----------- */

/*====Welcome Content Css Start=====*/
.welcome-content{background-color:#fff; border-radius:10px; padding:15px;border-top: 2px solid #d3d3d3;}
.welcome-content h2{font-size:24px; color:#000000; font-family:'OpenSans-Bold'; margin:0;}
.welcome-content h3{font-size:18px; color:#000000; font-family:'OpenSans-Bold'; margin:0; padding-bottom: 25px;}
.welcome-content h4{font-size:18px; color:#000000; font-family:'OpenSans-Bold'; margin:0; }
.welcome-content span.welcome-txt{font-size:14px; color:#000; font-family:'OpenSans-Regular'; display:block; padding:20px 0 10px;}
/*
.welcome-content .btn{font-size:14px; font-family:'OpenSans-Bold'; border-radius:10px;}
.welcome-content .btn-success{background-color:#01cbb5; border-color:#01cbb5;}
.welcome-content .btn-cancel{background-color:#cccccc; border-color:#cccccc; color:#FFF;}
*/
.welcome-content .small-button{padding:6px 15px;}
.welcome-content .big-button{padding:6px 20px;}
.welcome-content .big2-button{padding:6px 55px 6px 15px;}
.welcome-content .small-button span{padding-right:5px;}
.welcome-content .big-button span{padding-right:5px;}
.welcome-content .big2-button span{padding-right:5px;}
.welcone-pedding{ padding:15px 15px 300px 15px;}
.form-control{box-shadow: inset 0px 2px 0px rgba(0, 0, 0, .075);}
.heapBox .holder{box-shadow: inset 0px 2px 0px rgba(0, 0, 0, .075);}
.readme{ padding:10px 0 ;}


/*====Welcome Content Css End=====*/

/*====Group Content Css Start=====*/
.group-content{background-color:#fff; border-radius:10px; padding:15px 15px 300px; margin:15px 0 0;border-top: 2px solid #d3d3d3}
.group-content h2{font-size:24px; color:#959595; font-family:'OpenSans-Regular'; margin:0; display:inline-block; padding-bottom:10px;}
.group-content cite{font-size:24px; color:#000; font-family:'OpenSans-Bold'; font-style:normal;}
.group-content h6{font-size:14px; color:#000; font-family:'OpenSans-Bold'; margin:0;}
.group-content span{color:#959595; font-size:14px; font-family:'OpenSans-Regular'; display:block;}
.group-content .date-content{}
.group-content .date-content li{font-size:14px; color:#000; font-family:'OpenSans-Bold';}
.group-content .date-content li span{display:inline-block; padding-left:5px;}
.group-content p{color:#959595; font-size:14px; font-family:'OpenSans-Regular';}
.group-description .padd-one{padding:0 5px 0 0;}
.group-description .padd-three{padding:0 0 0 5px;}
.group-description .shared-content{background-color:#f4f5f6; border-bottom:5px solid #fc6e51; padding:18px 15px 12px; text-align:center;}
.group-description .shared-content img{margin:0 auto 10px;}
.group-description .shared-content h2{color:#959595; font-size:24px; display:inline-block; font-family:'OpenSans-Light'; padding:0;}
.group-description .shared-content span{display:inline-block; font-size:24px; color:#000; font-family:'OpenSans-Semibold';}
.group-description .available-content{border-bottom:5px solid #01cbb5;}
.group-description .pending-content{border-bottom:5px solid #ac92ec;}
.member{background-color:#f4f5f6; margin:20px 0 0 0; border-bottom:5px solid #01cbb5; padding:10px 15px;}
.member h2{color:#959595; font-size:24px; font-family:'OpenSans-Light'; display:inline-block; padding-bottom:10px;}
/* .member span{font-size:24px; font-family:'OpenSans-Bold'; color:#000; display:inline-block;}  don't think we need this anymore, and it's interfering with dropdown */
.member img{margin:0 auto;}
.members-two{margin:10px 0 0 0;}

/*====Welcome Submit Need Confirmation =====*/
.cust-control .control-label{ text-align:left!important; font-family:'OpenSans-Regular';}

.cust-group{ margin-bottom:10px ;}
.cust-form{border-radius: 10px;}
.text-padding p { padding-bottom:30px; color:#000; margin-top:10px; font-family:'OpenSans-Regular';}
.button-padding { padding-left:7px;}
/*====submitneedrequest=====*/
.text-padding1 p { padding-bottom:12px; color:#000; font-family:'OpenSans-Regular';}
/*====trashhomenogroup page=====*/
.bg2-pedding {padding: 0 0 499px 0;}
.bg3-padding{padding: 0 0 260px 0;}
.bg5-padding{padding: 0 0 106px 0;}
.infoimg img{ margin-left:10px;}

/*====people page=====*/
.group-description .shared-people{padding: 15px 15px 12px; background:#FFF;border-radius: 10px; border-top: 2px solid #d3d3d3}
.group-description .padd-four{padding: 0 10px 0 0;}
.group-description .padd-five{padding: 0 0 0 10px;}
.cust-name span {color:#000; font-family:'OpenSans-Regular'; font-size:14px;}
.shared-people h2 ,.social{ padding-bottom:15px !important;}
.social-padd{ padding-top:15px !important;}
.extrapadding5{ padding-left:15px;}
.social-padd2{ padding-top:22px !important;}
.people-margindbottom { margin-bottom:20px;}
.font{ font-family:'OpenSans-regular'; font-weight:normal; font-size:14px !important;}
.font3{ font-family:'OpenSans-bold' !important; color:#000 !important; display: initial !important; margin-right:10px;}
.font1{ font-family:'OpenSans-regular'; font-weight:normal; font-size:14px !important; line-height: 39px !important;}
.cust-dropdown{ float:left; margin: 0 2px; }
.cust-menu{ padding:10px; border: 2px solid #01cbb5; border-radius:12px; top: 117%; -moz-box-shadow:1px 1px 1px 3px #ccc;-webkit-box-shadow:  1px 1px 1px 3px #ccc;box-shadow:1px 1px 1px 3px #ccc; background:#FFF;}
.cust-menu span{color:#000; font-family:'OpenSans-Regular'; font-size:14px;}
.cust-menu a { margin:5px 0;}
.cust-menu .btn-cancel{ padding:6px 55px;}
.arrow{position:absolute; top:-20px; left:16px;}
.arrow1{position:absolute; top:-20px; left:51px;}
.arrow2{position:absolute; top:-20px; left:88px;}
.round img{ border-radius:50%;}
.datepadd{ padding:0 15px !important;}
.addonradious{border-top-right-radius:10px!important ; border-bottom-right-radius:10px!important;}

/*====welcome commen change page=====*/
.welcome-contentpart span{font-size: 24px;color: #21507c;font-family: 'OpenSans-Semibold';display: block;margin: 15px 0 6px 0;}
.welcome-contentpart p{font-family: 'OpenSans-regular';}
.profile {margin:5px 0 0 0;}
.profile span{font-size:14px; font-family:'OpenSans-regular';}
.but-padding{ padding-top:40px;}
.file-text p{  font-size:10px;margin-left: 15px; font-family:'OpenSans-regular';}
.chackbox-margin{ margin-left:-71px;}
#bc2{padding: 20px 0px;margin-left: 2px;}
.position-but{position: absolute;right: -38%;}
.help-block{font-family:'OpenSans-regular'; font-size:10px;}
.cust-breadcrumb .btn-default:hover, .btn-default:focus, .btn-default.focus, .btn-default:active, .btn-default.active, .open > .dropdown-toggle.btn-default{background-color:#f2f2f2;}
#bc2 a{background-color:#f2f2f2;}
/*==== end of welcome commen change page=====*/
.people-content span {font-size: 24px;color: #21507c;font-family: 'OpenSans-Semibold';display: block;margin: 11px 0; }

/*====  start group page====*/

.mygroup-content span {font-size: 24px;color: #21507c;font-family: 'OpenSans-Semibold';display: block;margin:16px 0;}
.mygroup-content p {color: #000;font-family:'OpenSans-regular';}
.group-text p {font-family:'OpenSans-regular';}
.group-margin{ margin-top:15px;}
.group-margin span{font-size:14px; color:#000; font-family:'OpenSans-Bold';}
.group-margin1 span{font-size:14px; color:#000; font-family:'OpenSans-Bold';}
.privet-group {background-color:#cccccc !important;border-color:#cccccc !important;color: #FFF !important; font-size: 14px;font-family: 'OpenSans-Bold';}
.contain-margin{ margin-bottom:15px;}
     /*==== my group inner page=====*/
	 a:focus{ outline:0;}
	   .member-covenant p{ color:#000;}
	  .btn-success1 {background-color: #01cbb5;border-color: #01cbb5; color:#fff;font-size: 14px;font-family: 'OpenSans-Bold';border-radius: 10px;}
      .btn-success1:hover{background-color: #01cbb5;border-color: #01cbb5; color:#fff;font-size: 14px;font-family: 'OpenSans-Bold';border-radius: 10px;}
	    .btn-success1:focus {background-color: #01cbb5;border-color: #01cbb5; color:#fff;font-size: 14px;font-family: 'OpenSans-Bold';border-radius: 10px;}
	  .img-margin span img{ margin-right:9px;}
	    .img-margin img{ margin-right:9px;}
		.mygrup-content span{font-size: 24px;color: #21507c;font-family: 'OpenSans-Semibold';display: block; margin: 20px 0;}
	  .cust-block{ margin:10px 0 20px -15px;border-left: 5px solid #01cbb5; padding: 0px 10px;}
	  .member-covenant{background-color: #f4f5f6;margin: 20px 0 0 0;padding: 10px 15px;}
	  input[type=checkbox].css-checkbox {position:absolute; z-index:-1000; left:-1000px; overflow: hidden; clip: rect(0 0 0 0); height:1px; width:1px; margin:-1px; padding:0; border:0;}
      input[type=checkbox].css-checkbox + label.css-label { font-family:'OpenSans-regular';padding-left:48px;height:43px; display:inline-block;line-height:30px;background-repeat:no-repeat;   background-position: 0 0;font-size:10px;vertical-align:middle;cursor:pointer;}
     input[type=checkbox].css-checkbox:checked + label.css-label {background-position: 0 -43px;}
     input[type=radio].css-checkbox:checked + label.css-label {background-position: 0 -43px;}
	 label.css-label {background-image:url(../images/csscheckbox.png);-webkit-touch-callout: none;-webkit-user-select: none;
				-khtml-user-select: none;-moz-user-select: none;-ms-user-select: none;user-select: none;}
				.cust-marginimg a{ margin-bottom:10px;}
/*==== strat of my group inner page=====*/
 /*==== my group inner page=====*/
.group-font h4{ font-size:24px;color:#000; font-family:'OpenSans-Bold'; margin-bottom:10px;}
.group-font p{ font-size:14px;color:#959595; font-family:'OpenSans-regular';}
.text-inner{ border:1px solid #000; padding:10px; margin-bottom:20px;}
.text-inner span{font-size:14px;color:#000; font-family:'OpenSans-Bold'; }
.text-inner p{font-size:14px;color:#000; font-family:'OpenSans-regular'; }
.text-inner label.css-label {background-image:url(../images/check.png);-webkit-touch-callout: none;-webkit-user-select: none;
				-khtml-user-select: none;-moz-user-select: none;-ms-user-select: none;user-select: none;}
.privancy-text p{font-size:14px;color:#000; font-family:'OpenSans-regular'; }
.privancy-text p span{font-size:14px;color:#000; font-family:'OpenSans-Bold'; }			
     /*==== end of my group inner page=====*/
.donation-content span {font-size: 24px;color: #21507c;font-family: 'OpenSans-Semibold';display: block;margin: 5px 0;}
.donationimg{ margin-top:35px;} 
.color p{ color:#000;}
.color span{ color:#333; font-family:'OpenSans-Bold';}
.cust-control .control-label1 span{font-family:'OpenSans-regular'; }
.cust-donation span{ color:#333; font-family:'OpenSans-Bold'; }
.cust-donation p { color:#000; font-size:14px;}
.donationpadding{ padding-top:20px;}
.donationmargin{ margin-bottom:30px;}
.inner-padding{ padding:10px;}
.donation-content-tabs { background: white; border: 1px solid #d5d5d5; border-top: 0; padding-left: 10px; padding-top: 20px; padding-bottom: 10px; margin-bottom: 10px;  }
.donation-uk-panel {padding: 30px; padding-top: 10px}
.embed-responsive.embed-responsive-16by9 {padding-bottom: 23%}
.hr-soft {background-color: #dadada; border-width: 0; height: 1px;}
.nav-tabs.nav-justified > .active > a, .nav-tabs.nav-justified > .active > a:hover, .nav-tabs.nav-justified > .active > a:focus {background: #01cbb5; border-bottom: 1px solid #01cbb5; color: white; font-weight: 600}
/*==== end of group page=====*/

/*==== table all requst=====*/
.tablebg{background:#f4f5f6; padding-bottom:254px;}
.tdimg img{ padding-left:35px;}	 

/*==== end of table all requst=====*/
/*====strat All Requestse=====*/
.tdclass{ background:#ececec; color:#959595; font-family:'OpenSans-regular'; font-size:14px;}
.table {font-family:'OpenSans-regular'; font-size:14px;}
.worning{ font-family:'OpenSans-Bold'; color:#ff0000;}
.btn-success2 {background-color: #01cbb5; padding: 7px 34px;border-color: #01cbb5;color: #fff !important;font-size: 14px;font-family: 'OpenSans-Bold';border-top-left-radius: 10px;border-top-right-radius: 10px;
 border-bottom-left-radius: 0px;border-bottom-right-radius: 0px;}
.discution {
color: #fff !important;font-size: 14px;padding: 7px 34px;font-family: 'OpenSans-Bold';border-top-left-radius: 10px;border-top-right-radius: 10px; background:#ccc;border-bottom-left-radius: 0px;border-bottom-right-radius: 0px;}
.discution:hover, .discution:active, .discution:focus {background-color:#a1a1a1; border-color:#a1a1a1;}
.red a cite{ color:red;font-style: normal; margin:0 5px;}
.cust-width{ width:18% !important;}
.datemargin{ margin:10px 0;}
.btn-two{background:url(../images/sendimg.png) no-repeat 7px 3px scroll; padding:6px 45px !important;}
/*====end All Requestse=====*/
  input[type=checkbox].css-checkbox3 {position:absolute; z-index:-1000; left:-1000px; overflow: hidden; clip: rect(0 0 0 0); height:1px; width:1px; margin:-1px; padding:0; border:0;}
      input[type=checkbox].css-checkbox3 + label.css-label3 { font-family:'OpenSans-regular';padding-left:65px;height:43px; display:inline-block;line-height:30px;background-repeat:no-repeat;   background-position: 0 0;font-size:13px;vertical-align:middle;cursor:pointer;}
     input[type=checkbox].css-checkbox3:checked + label.css-label3 {background-position: 0 -41px;}
	 label.css-label3 {background-image:url(../images/csscheckbox2.png);-webkit-touch-callout: none;-webkit-user-select: none;
				-khtml-user-select: none;-moz-user-select: none;-ms-user-select: none;user-select: none;}
/*====start need req css=====*/

.needbtn cita  { margin:0 5px;}
.needs-content {background-color:#f4f5f6; border-bottom: 5px solid #01cbb5;}
.needs-content span ,h6{font-size:14px;font-family: 'OpenSans-Bold'; padding:0; margin:0; }
.needs-content h2{ margin:18px 0 0 0;}
.needs-content p{ font-family:'OpenSans-regular'; font-size:14px;}
.help-block{ margin-top:0;font-family:'OpenSans-regular'; font-size:14px; color:#000;}
.needs-group .form-group{ margin:0 0 0 0 !important;}
.needs-group .form-group p img{ padding-right:15px;}
.needs-group{ margin-top:20px;}
.cast{ padding:0px 10px 0 25px;}
.need-top{ margin-top:10px;}
.need-but{ padding:10px 0;}
.butvote{ background:#fff; color:#959595; font-family: 'OpenSans-Bold'; border: 1px solid #959595; }
.butvote:hover{ background:#fff; color:#959595; font-family: 'OpenSans-Bold'; border: 1px solid #959595; }
.butvote:focas{ background:#fff; color:#959595; font-family: 'OpenSans-Bold'; border: 1px solid #959595; outline:none !important; }
.need-but2{ padding-bottom:10px;}
.cast h3{ color:#ff0000; font-size:24px; padding: 10px 0;}
.cast p{ color:#ff0000; font-size:14px; font-family:'OpenSans-regular';}
.voting{padding:0px 10px 0 25px;}
.voting h3{ color:#000; font-size:24px; padding: 10px 0;}
.btn1{ padding: 6px 36px 6px 17px;}
.btn2{ padding:6px 15px 6px 15px;}
.btn3{ padding: 6px 15px 6px 15px;}
.btn4{ padding: 6px 36px 6px 17px;}
.requests-contain span{font-size: 24px; color: #21507c;font-family: 'OpenSans-Semibold';display: block;margin:0;}
.need-bg{ background-color:#f4f5f6;}
.need-bg h2{font-size: 24px;color: #000000;font-family: 'OpenSans-Bold'; padding:15px 0;}
.comment-contain { text-decoration:none; list-style:none; margin:0; padding:0;}
.comment1 h2{ font-size:14px; font-family: 'OpenSans-Bold'; padding:0; }
.comment1 span { font-size:14px; font-family:'OpenSans-regular'; font-style:italic; }
.comment1 p{ font-family:'OpenSans-regular'; font-size:14px; margin-top:20px; color:#000; }
.progress-bar2{ background:#7cc517 !important ;float: left;width: 0;height: 100%;font-size: 12px;line-height: 20px;color: #fff;text-align: center;}
.progress-bar3{ background:#919191 !important; float: left;width: 0;height: 100%;font-size: 12px;line-height: 20px;color: #fff;text-align: center;}
.progress-bar4{ background:#eda9aa !important;float: left;width: 0;height: 100%;font-size: 12px;line-height: 20px;color: #fff;text-align: center;}
.progress-bar5{ background:#fe4242 !important; float: left;width: 0;height: 100%;font-size: 12px;line-height: 20px;color: #fff;text-align: center;}
.cust-progress2{max-width: 70%;background: #fff;margin: 20px 0 5px 0; height: 15px;border-radius: 20px;}
.votinglist{ list-style:none; padding:0; margin:0;}
.text-watting{ font-family: 'OpenSans-Semibold' !important; font-size:14px !important; margin-left:15px !important;}
.votinglist li img{ margin-right:5px;}
.requests-padding{ padding:20px 0;}
/*====end of need req css=====*/

/*=== Start all group=====*/ 
.allgroup-contain span{font-size: 24px;color: #21507c;font-family: 'OpenSans-Semibold';display: block;}
.margin-padding{ padding:20px 0;}
.btn-success3{background-color: #01cbb5;border-color: #01cbb5; border-radius: 10px;}
.btn-success3:hover{background-color: #01cbb5;border-color: #01cbb5;}
.btn-success3:focus{background-color: #01cbb5;border-color: #01cbb5;}
.new-radius .heapBox .holder{ border-top-left-radius: 4px;border-bottom-left-radius: 4px;}
.new-radius .heapBox .handler{ border-top-right-radius:4px;border-bottom-right-radius: 4px;}
.new-radius .heapBox div.heap{border-radius: 4px;}
.cust-form1{border-radius: 4px;}
.heapBox .heap .heapOptions .heapOption:first-child{ display:none;}
.cust-head .heapBox .heap .heapOptions .heapOption:first-child{ display:block !important;}

.myaccount-margin{ margin:15px 0;}

/*=== Start my account page =====*/ 
.butaccount-margin{ margin:20px 0;}
.myaccount-cantain{ background-color: #f4f5f6;}
.myaccount-cantain h2 { margin:10px 0;}
 input[type=checkbox].css-checkbox1 {position:absolute; z-index:-1000; left:-1000px; overflow: hidden; clip: rect(0 0 0 0); height:1px; width:1px; margin:-1px; padding:0; border:0;}
 input[type=checkbox].css-checkbox1 + label.css-label1 { font-family:'OpenSans-regular';padding-left:48px;height:43px; display:inline-block;line-height:40px;background-repeat:no-repeat;   background-position: 0 0;font-size:13px;vertical-align:middle;cursor:pointer;}
input[type=checkbox].css-checkbox1:checked + label.css-label1 {background-position: 0 -43px;}
 input[type=radio].css-checkbox1 {position:absolute; z-index:-1000; left:-1000px; overflow: hidden; clip: rect(0 0 0 0); height:1px; width:1px; margin:-1px; padding:0; border:0;}
 input[type=radio].css-checkbox1 + label.css-label1 { font-family:'OpenSans-regular';padding-left:48px;height:43px; display:inline-block;line-height:40px;background-repeat:no-repeat;   background-position: 0 0;font-size:13px;vertical-align:middle;cursor:pointer;}
input[type=radio].css-checkbox1:checked + label.css-label1 {background-position: 0 -43px;}
 label.css-label1 {background-image:url(../images/csscheckbox1.png);-webkit-touch-callout: none;-webkit-user-select: none;-khtml-user-select: none;-moz-user-select: none;-ms-user-select: none;user-select: none;}
/*=== End of my accountpage=====*/ 

/*====My Finaces start css=====*/
.finaces-padding{ padding: 0 30px;}
.finaces-text{ background:#ececec; border-radius:10px; border:1px solid #d3d3d3;}
.finaces-text h3{ font-size:14px;font-family: 'OpenSans-Bold'; padding:18px 0 !important; }
.finaces-text p{ color:#000; font-size:14px; font-family:'OpenSans-regular' }
.finaces-content{ background-color: #f4f5f6;border-bottom: 5px solid #01cbb5;}
.finaces-font h4{ font-size: 24px;color: #000;font-family: 'OpenSans-Bold';margin: 10px 0;}
.finaces-margin{ margin-top:-44px;}
.finaces-top{ margin:15px 0 0 0;}
.finaces-but{ padding-left:12px;}
.myfinaces-content span{font-size: 24px;color: #21507c;font-family: 'OpenSans-Semibold';display: block;margin: 25px 0;}
.finaces-cust h2{ margin-top:23px;}
.cust-progress { max-width:70%; background:#fff; margin:20px 0; height:15px; border-radius: 20px;}
.progress-bar{ background-color: #02ceb9;}
/*==== My Finaces End css=====*/

/*====Media Query Start=====*/ 
@media all and (max-width:1024px){
.topmenu-bg .cust-nav ul li a{padding:10px 14px;}
.welcome-content .small-button{padding:6px 12px;}
.welcome-content .big-button{padding:6px 16px;}
.welcome-content .big2-button{padding:6px 47px 6px 15px;}
.cust-control .control-label span{ font-size:12px !important;}
.cust-control .control-label { font-size:12px !important;} 
.group-description .shared-people{padding: 15px 10px 12px;}
.larg-text p{ font-size:35px;}

input[type=checkbox].css-checkbox + label.css-label{ font-size:14px;}
.privancy-text p{ text-align:left;}
}

@media all and (max-width:960px){
.logo-content{}
.logo-content img{margin:0 auto;}
.signout-content{padding:0 0 10px 0; text-align:center;}
.signout-content span{font-size:12px; padding-right:5px;}
.signout-content a{font-size:12px;}
.topmenu-bg .navbar-toggle{float:none; margin:15px auto;}
.topmenu-bg .navbar-header{text-align:center;  margin:0!important;}
.topmenu-bg .navbar-toggle{background-color:#000;}
.home-content span{text-align:center;}
.welcome-content{text-align:center;}
.welcome-content a{ }
.welcome-content .visa a{ } 
.welcome-content .btn{margin:5px 1px; font-size:12px;}
.group-description .padd-one, .group-description .padd-three{padding:0;}
.topmenu-bg .cust-nav ul li a:hover{border-bottom:none;}
.topmenu-bg #navbar{text-align:center;}
.members-two{margin:20px 0 0;}
.group-description .shared-content{margin-bottom:5px;}
.navbar-header { float: none;}
.navbar-left,.navbar-right { float: none !important;}
.navbar-toggle {  display: block;}
.navbar-collapse {   border-top: 1px solid transparent;  box-shadow: inset 0 1px 0 rgba(255,255,255,0.1);}
.navbar-fixed-top {top: 0;border-width: 0 0 1px;}
.navbar-collapse.collapse {  display: none!important;}
.navbar-collapse.collapse.in { display: block !important;}
.navbar-nav { float: none!important;margin-top: 7.5px;}
.navbar-nav>li { float: none;}
.navbar-nav>li>a { padding-top: 10px; padding-bottom: 10px;}
.collapse.in{display:block !important;}
.group-description .shared-content h2{ font-size:21px;}
.heapBox .holder {width: 180px !important;}
.profile{ text-align:left;}
.imgresize img { width:100%;}
.position-but {position: absolute;right: -44%;top: -4%;}
.toggle-group .btn{ margin: 0px 1px;}
.toggle-handle{ left:-37%;}
.help-text .help-block { font-size:10px;}
.but-margin3 .btn{ margin:0;}
input[type=checkbox].css-checkbox + label.css-label{ font-size:12px; line-height:20px; padding-top:10px;}
.radio-inline { display: -webkit-box;}
.but-margin2{ margin: 0 0 0 0;}
.but1-margin{ margin:0 0 !important;}
.welcome-content{ text-align:left;}
.tdimg img {padding-left: 79px;}
.cust-head2 .heapBox .holder{width: 151px !important;}
.finaces-margin {margin-top: -65px;}
.cust-head2 .heapBox .heap {width: 185px!important;}
.chackbox-margin {margin-left: -25px;}
.color span{ font-size:12px;}
.cust-control .control-label1 span{ font-size:12px;}
}


@media all and (max-width:320px){

.cust-control .control-label{ text-align:center !important;}
.signup .padd-four{ padding:10px !important;}
.signup .padd-five{ padding:10px !important;}
.larg-text p{ font-size:20px;}
.thank-content p{ padding:15px;}
.thank-content span{ padding:15px;}
.heapBox .holder {width: 223px !important;}
.toggle-group .btn{ margin:0!important;}
.chackbox-margin{ margin:0;}
.position-but{right: 34%;top: 110%;}
input[type=checkbox].css-checkbox + label.css-label{ font-size:12px; line-height:21px; padding-top:0;}
input[type=checkbox].css-checkbox1 + label.css-label1{ font-size:12px; line-height:21px;}
.heapBox div.heap {width: 266px!important;}
.radio-inline input[type="radio"]{margin-top: 3px;}
.but-margin2 {margin: 0 0 0 0;}
.margin-mobile{ margin-bottom:10px;}
.cust-width {width: 100% !important;}
.mobilescroll{ overflow-x:scroll;}
.centerposition{ text-align:center !important;}
.finaces-margin {margin-top: 0px;}
.votinglist li { text-align:left;}ss
.mobile-padd{ padding:0 10px !important;}
.chakbox-margin{ margin-top:10px;}
.privancy-text p{ text-align: center;}
.radio-inline {display: inline-block;}
.donationimg{ margin-top:0; text-align:center;}
.tdimg img {padding-left: 20px;}
.needs-group .form-group p img {padding-right: 5px;}
.needs-content span{ font-size:13px;}
.finaces-padding{ padding:0 0 !important;}
.chackbox-margin {margin-left:0px;}
.padd-shearpeople{ padding:15px !important;}
}
/*====Media Query End=====*/

/* BG Additions: */

.btn-success{color:#fff !important; background-color:#01cbb5; border-color:#01cbb5;}
.btn-success:hover, .btn-success:active, .btn-success:focus{background-color:#1cab9b; border-color:#1cab9b;}
.btn-cancel{color:#fff; background-color:#cccccc; border-color:#cccccc;}
.btn-cancel:hover, .btn-cancel:active, .btn-cancel:focus{color:#fff; background-color:#a1a1a1; border-color:#a1a1a1;}

label.file-upload-bg input[type="file"] {position:fixed; top: -1000px;}

.group-description .shared-people, .welcome-content{
	border-radius:10px;
	border: 0px none;
	-webkit-box-shadow:inset 0px 2px 2px 0px rgba(0,0,0,0.25);
	-moz-box-shadow:inset 0px 2px 2px 0px rgba(0,0,0,0.25);
	box-shadow:inset 0px 2px 2px 0px rgba(0,0,0,0.25);
}

.input-group-addon { border-radius:10px }

.form-group{margin-bottom: 12px;}
.form-control, .input-group-addon, .input-group-btn button {
	height:32px;
	border: 1px solid #e0e0e0;}
input.form-control, textarea.form-control{
	-webkit-box-shadow:inset 0px 1px 1px 0px rgba(0,0,0,0.25);
	-moz-box-shadow:inset 0px 1px 1px 0px rgba(0,0,0,0.25);
	box-shadow:inset 0px 1px 1px 0px rgba(0,0,0,0.25);}
select.form-control {border: 1px solid #e0e0e0;border-bottom-left-radius:10px;border-top-left-radius:10px;}
input.form-control, textarea.form-control {border-radius:10px;}

/* End BG additions */

span.success {
  color: green;
}

span.error {
  color: red;
}

.group-list .row { margin-left:-10px; margin-right:-10px; }
.group-list .padding { padding-left:10px; padding-right:10px; }
.group-list .no-padding { padding-left:0px; padding-right:0px; }
.group-pane { margin-bottom:20px; }

.footer{padding-top:13px;background-color:#ececec;}

.admin-panel {background-color:#f4f5f6; margin:20px 0 0 0; border-bottom:5px solid #01cbb5; padding:10px 15px;}
.admin-panel h3{color:#959595; font-size:24px; font-family:'OpenSans-Light'; display:inline-block; padding-bottom:10px;}
div.member-admin { padding: 5px; }
div.owner { background-color:#C0F2E6; }
div.admin { background-color:#ADC2EB; }
div.membr { background-color:#e0e0e0; }

/*=========== Admin Pages ===========*/
tr.deleted td,
tr.deleted a:link,
tr.deleted a:visited,
tr.deleted a:hover,
tr.deleted a:active
 { color:#AAA; }

/*===== Member photos ========*/
.memberpix span {margin-bottom:5px;margin-right:5px; position:relative;}
span.tinypix, span.mediumpix, span.largepix {
	background-repeat:no-repeat;
	background-position:50% top;
	background-size:cover;
	border-radius:50%;
	-webkit-border-radius: 50%;
	-moz-border-radius: 50%;
	border:1px solid #aaaaaa;
	display: inline-block;
}
.mediumpix div {
	position: absolute;
	top: 17px;
	left: 4px;
	bottom: 0;
	right: 0;
	z-index: 99;
	overflow: hidden;
}
span.tinypix {width:24px;height:24px;}
span.mediumpix {width:63px;height:63px;}
span.largepix {width:83px;height:83px;}

.combobox-fix span{display:table;width:100%;}
.combobox-fix input{border-top-left-radius:10px;border-bottom-left-radius:10px;width:100%;display:table-cell;float:left;overflow:hidden;}
.combobox-fix a{border-top-right-radius:10px;border-bottom-right-radius:10px;display:table-cell;vertical-align:middle;}

.discuss-container{background:#f4f5f6;}
.home-content-bg{padding-bottom: 25px !important; margin-top: 0px !important;}

/* ====== Custom CSS for selectBoxIt ======= */

.selectboxit {
	border-color:#e0e0e0;
	-webkit-box-shadow:inset 0px 1px 1px 0px rgba(0,0,0,0.25) !important;
	-moz-box-shadow:inset 0px 1px 1px 0px rgba(0,0,0,0.25) !important;
	box-shadow:inset 0px 1px 1px 0px rgba(0,0,0,0.25) !important;
}
span.selected-text {
}
.selectboxit-option-icon-container{float:left;}
.selectboxit-arrow-container{
	z-index:-1000;
	float:right;
	background-color:#01cbb5;
	-webkit-box-shadow:-1px 1px 1px 0px rgba(0,0,0,0.25) !important;
	-moz-box-shadow:-1px 1px 1px 0px rgba(0,0,0,0.25) !important;
	box-shadow:-1px 1px 1px 0px rgba(0,0,0,0.25) !important;
}
.selectboxit-arrow-container:hover{background-color:#1cab9b;}
.selectboxit-default-arrow {
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-top: 4px solid #ffffff;
    height: 0;
    width: 0;
}
.selectboxit-text{width:100%;right-margin:30px;}
.selectboxit-container {width:100%;}
.selected-text{overflow: hidden;padding-right:30px;}
.selectboxit-container .selectboxit {
	white-space: nowrap;
	border-radius:10px;
	width:100%;
	height:32px;
	background-image:none;
	background-color:#fff;
	}
.selectboxit-container .selectboxit-options {
	border-radius:10px;
	-webkit-box-shadow:0px 2px 2px 0px rgba(0,0,0,0.25);
	-moz-box-shadow:0px 2px 2px 0px rgba(0,0,0,0.25);
	box-shadow:0px 2px 2px 0px rgba(0,0,0,0.25);
	}
.selectboxit-container .selectboxit:hover, .selectboxit-container .selectboxit:focus {background-color:#fff;}
.selectboxit-list > .selectboxit-focus > .selectboxit-option-anchor {background-color:#01cbb5;background-image:none;}

/* Custom Alerts styling */
#cc_alerts {position:absolute;left:0px;right:0px;z-index:1000;margin-top:5px;}

.alert-info td.alert-symbol{background-color:#ac92ec;}
.alert-info td.alert-text{color:#31708f;}
.alert-success td.alert-symbol{background-color:#01cbb5;}
.alert-success td.alert-text{color:#1cab9b;}
.alert-warning td.alert-symbol{background-color:#FEEFB3;}
.alert-warning td.alert-text{color:#9F6000;}
.alert-danger td.alert-symbol{background-color:#fc6e51;}

td.alert-symbol { vertical-align:middle;padding:0px 6px 0px 7px;font-size:22px;color:white;}
td.alert-symbol i { position:relative;top:3px; }
td.alert-text { width:100%; height:50px; vertical-align:middle; padding:6px 0px 6px 10px;}
td.alert-text p { margin:0px; }
td.close-button { vertical-align:middle; padding:0px 15px;}
td.close-button button { right:0px !important;top:0px !important; }
tr.alert-row { width:100%;}
.alert table {width:100%;}
.alert {
	position:relative;
	width:100%;
	padding: 0px;
	border-radius:25px;
	border: 0px none;
	background-color:white;
	overflow:hidden;
	-webkit-box-shadow:0px 2px 2px 0px rgba(0,0,0,0.25);
	-moz-box-shadow:0px 2px 2px 0px rgba(0,0,0,0.25);
	box-shadow:0px 2px 2px 0px rgba(0,0,0,0.25);
	animation-name:alertanimation;
	animation-duration:.6s;
	/* animation-timing-function: ease-out; */
	}
@keyframes alertanimation{
	from {opacity:.25;top:20px;right:5px}
	to {opacity:1;top:0px;right:0px;}
}

.group_tab {display:inline-block;}

/*===== Button stuff =====*/
.good_spacing{margin: 0 10px 10px 0;}

/* End BG Additions: */

