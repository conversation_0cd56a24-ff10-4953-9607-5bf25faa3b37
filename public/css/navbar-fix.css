/* Navbar Toggle Fix */
/* This file fixes the navbar toggle functionality that was broken by style.css */

/* Reset problematic navbar styles */
.navbar-toggle {
    display: none !important;
}

.navbar-collapse.collapse {
    display: block !important;
}

/* Apply proper responsive behavior */
@media (max-width: 767px) {
    .navbar-toggle {
        display: block !important;
    }
    
    .navbar-collapse.collapse {
        display: none !important;
    }
    
    .navbar-collapse.collapse.in {
        display: block !important;
    }
}

@media (min-width: 768px) {
    .navbar-toggle {
        display: none !important;
    }
    
    .navbar-collapse.collapse {
        display: block !important;
    }
}

/* Ensure proper navbar behavior */
.navbar-collapse {
    border-top: 1px solid transparent;
    box-shadow: inset 0 1px 0 rgba(255,255,255,0.1);
}

.navbar-nav {
    float: none !important;
    margin-top: 7.5px;
}

.navbar-nav > li {
    float: none;
}

.navbar-nav > li > a {
    padding-top: 10px;
    padding-bottom: 10px;
} 