/* Core styles for signup landing page */
:root {
    /* Primary colors */
    --primary-green: #9ACB48;
    --primary-purple: #8347CC;
    --primary-grey: #CECECE;
    --primary-navy: #5144A1;
    --primary-black: #1A1A1A;

    /* Light colors */
    --light-cream: #F9F9F0;
    --light-lavender: #F0F0F9;
    --light-blue: #E9EFF2;
    --light-gray: #808080;
    --light-white: #FFFFFF;

    /* Font */
    --font-family: 'Inter', sans-serif;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: var(--font-family);
}

body {
    background-color: var(--light-white);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Signup Page */
.signup-page {
    display: flex;
    height: 100vh;
}

.signup-image {
    flex: 1;
    background-size: cover;
    background-position: center;
    background-image: url('/images/landing-bg-1.png');
}

.signin-image {
    background-image: url('/images/landing-bg-2.jpg');
}


.signup-section {
    flex: 1;
    padding: 40px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center; /* Center align the content */
    width: 100%;
}

.signup-section form {
    width: 60%;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
}

/* Container for form elements - 60% of parent width */
.signup-section-content {
    width: 100%;
}

.signup-section .signup-logo {
    margin-bottom: 50px;
    width: 60%;
}

.signup-section .signup-logo img {
    height: 89px;
}

/* Typography styles */
.signup-section h1 {
    font-family: 'Inter', sans-serif;
    font-weight: 700;
    font-size: 24px;
    line-height: 36px;
    letter-spacing: 0px;
    vertical-align: middle;
    color: var(--primary-navy);
    margin-bottom: 20px;
    width: 60%;
}

.signup-section .signup-instructions {
    font-size: 18px;
    margin-bottom: 30px;
    width: 60%;
}

.signup-section .signup-instructions b {
    font-family: 'Poppins', sans-serif;
    font-weight: 600;
    font-size: 20px;
    line-height: 30px;
    letter-spacing: 0px;
    vertical-align: middle;
    display: block;
    margin-bottom: 5px;
}

.signup-section .signup-instructions br + span,
.signup-section .signup-instructions br + text {
    font-family: 'Poppins', sans-serif;
    font-weight: 600;
    font-size: 20px;
    line-height: 30px;
    letter-spacing: 0px;
    vertical-align: middle;
    display: block;
}

/* Input styles with placeholder color */
.signup-section input[type="email"],
.signup-section input[type="text"],
.signup-section input[type="password"],
.signup-section input[type="number"],
.signup-section textarea,
.signup-section select {
    width: 100%;
    padding: 15px;
    margin-bottom: 20px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 16px;
    box-shadow: 0px 0px 4px 2px rgba(0, 0, 0, 0.1);
}

/* Placeholder color for all browsers */
.signup-section input::placeholder,
.signup-section textarea::placeholder {
    color: var(--light-gray);
    opacity: 1; /* Firefox */
}

/* Internet Explorer 10-11 */
.signup-section input:-ms-input-placeholder,
.signup-section textarea:-ms-input-placeholder {
    color: var(--light-gray);
}

/* Microsoft Edge */
.signup-section input::-ms-input-placeholder,
.signup-section textarea::-ms-input-placeholder {
    color: var(--light-gray);
}

.signup-section .btn-primary {
    background-color: var(--primary-navy);
    color: white;
    border: none;
    padding: 15px;
    width: 100%;
    border-radius: 5px;
    font-size: 16px;
    cursor: pointer;
    margin-bottom: 20px;
    transition: background-color 0.3s ease;
}

.signup-section .btn-primary:hover {
    background-color: var(--primary-purple);
    color: white;
}

.signup-section .divider {
    text-align: center;
    margin: 20px 0;
    color: #777;
    width: 60%;
}

.signup-section .social-signup {
    display: flex;
    flex-direction: column;
    gap: 10px;
    width: 60%;
}

.signup-section .social-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: white;
    border: 1px solid var(--primary-grey);
    padding: 12px;
    border-radius: 5px;
    font-size: 12px;
    cursor: pointer;
    gap: 10px;
    width: 100%;
    transition: all 0.3s ease;
    color: var(--primary-black);
    text-decoration: none;
}

.signup-section .social-btn:hover {
    background-color: var(--light-lavender);
    border-color: var(--primary-purple);
}

.signup-section .social-btn img {
    height: 20px;
}

.signup-section .signup-footer {
    text-align: center;
    margin-top: 30px;
    width: 60%;
}

.signup-footer a {
    color: var(--primary-purple);
    text-decoration: none;
}

.signup-footer a:hover {
    text-decoration: underline;
}

/* Remember me checkbox and forgot password link */
.remember-me {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    margin-bottom: 20px;
    font-size: 14px;
}

.remember-me label {
    display: flex;
    align-items: center;
    cursor: pointer;
}

.remember-me input[type="checkbox"] {
    margin-right: 8px;
}

.forgot-password {
    color: var(--primary-purple);
    text-decoration: none;
}

.forgot-password:hover {
    text-decoration: underline;
}

/* Input group for validation */
.input-group {
    width: 100%;
    margin-bottom: 20px;
}

.input-group input {
    width: 100%;
    margin-bottom: 0;
}

/* Error message */
.error-message {
    color: #d9534f;
    margin-bottom: 15px;
    text-align: center;
    display: block; /* Show by default when content exists */
}

.error-message:empty {
    display: none; /* Hide when empty */
}

/* Field-specific error message */
.field-error {
    color: #d9534f;
    font-size: 12px;
    margin-top: 5px;
    text-align: left;
}

/* Invalid input styling */
.is-invalid {
    border: 1px solid #d9534f !important;
    background-color: rgba(213, 83, 79, 0.05);
}

/* Responsive styles */
@media (max-width: 768px) {
    .signup-page {
        flex-direction: column;
        height: auto;
    }

    .signup-image {
        height: 200px;
    }

    .signup-section {
        padding: 20px;
    }

    .signup-section form,
    .signup-section .social-signup,
    .signup-section .divider,
    .signup-section .signup-footer,
    .signup-section h1,
    .signup-section .signup-instructions {
        width: 90%;
    }
}
