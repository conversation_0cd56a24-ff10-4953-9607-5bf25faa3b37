@charset "utf-8";
/* CSS Document */

@font-face {
	font-family: 'OpenSans-Bold';
	src: url('../fonts/OpenSans-Bold.eot'); /* IE9 Compat Modes */
	src: url('../fonts/OpenSans-Bold.eot?#iefix') format('embedded-opentype'), /* IE6-IE8 */  url('../fonts/OpenSans-Bold.woff2') format('woff2'), /* Super Modern Browsers */  url('../fonts/OpenSans-Bold.woff') format('woff'), /* Pretty Modern Browsers */  url('../fonts/OpenSans-Bold.ttf') format('truetype'), /* Safari, Android, iOS */  url('../fonts/OpenSans-Bold.svg#svgFontName') format('svg'); /* Legacy iOS */
}
@font-face {
	font-family: 'OpenSans-Regular';
	src: url('../fonts/OpenSans-Regular.eot'); /* IE9 Compat Modes */
	src: url('../fonts/OpenSans-Regular.eot?#iefix') format('embedded-opentype'), /* IE6-IE8 */  url('../fonts/OpenSans-Regular.woff2') format('woff2'), /* Super Modern Browsers */  url('../fonts/OpenSans-Regular.woff') format('woff'), /* Pretty Modern Browsers */  url('../fonts/OpenSans-Regular.ttf') format('truetype'), /* Safari, Android, iOS */  url('../fonts/OpenSans-Regular.svg#svgFontName') format('svg'); /* Legacy iOS */
}
/* Breadcrumbs from http://bootsnipp.com/snippets/featured/triangle-breadcrumbs-arrows */
.btn-breadcrumb .btn:not(:last-child):after {
	content: " ";
	display: block;
	width: 0;
	height: 0;
	border-top: 29px solid transparent;
	border-bottom: 29px solid transparent;
	border-left: 10px solid white;
	position: absolute;
	top: 50%;
	margin-top: -29px;
	left: 100%;
	z-index: 3;
}
.btn-breadcrumb .btn:not(:last-child):before {
	content: " ";
	display: block;
	width: 0;
	height: 0;
	border-top: 29px solid transparent;
	border-bottom: 29px solid transparent;
	border-left: 10px solid rgb(173, 173, 173);
	position: absolute;
	top: 50%;
	margin-top: -29px;
	margin-left: 1px;
	left: 100%;
	z-index: 3;
}
.btn-breadcrumb .btn {
padding: 18px 12px 18px 55px;
}
.extra1{
	background:#d8d9d9!important;
}
.extra2{
	background:#d8d9d9!important;
}
.extra3{
	background:#d8d9d9!important;
}
.extra4{
	background:#d8d9d9!important;
}
.btn-breadcrumb .btn:first-child {
padding: 18px 11px 18px 25px;;
}
.btn-breadcrumb .btn:last-child {
padding: 18px 11px 18px 50px;
}
/** Default button1 **/
.btn-breadcrumb .btn.btn-default.extra1:not(:last-child):before
{
		border-left: 30px solid #d8d9d9 !important;
				
}
.btn-breadcrumb .btn.btn-default.extra1:not(:last-child):after
{
		border-left: 30px solid #d8d9d9 !important;
}

/** Default button2 **/
.btn-breadcrumb .btn.btn-default.extra2:not(:last-child):before
{
		border-left: 30px solid #d8d9d9 !important;
}
.btn-breadcrumb .btn.btn-default.extra2:not(:last-child):after
{
		border-left: 30px solid #d8d9d9 !important;
}
/** Default button3 **/
.btn-breadcrumb .btn.btn-default.extra3:not(:last-child):before
{
		border-left: 30px solid #d8d9d9 !important;
}
.btn-breadcrumb .btn.btn-default.extra3:not(:last-child):after
{
		border-left: 30px solid #d8d9d9 !important;
}
/** Default button 4**/
.btn-breadcrumb .btn.btn-default.extra4:not(:last-child):before
{
		border-left: 30px solid #d8d9d9 !important;

}
.btn-breadcrumb .btn.btn-default.extra4:not(:last-child):after
{
		border-left: 30px solid #d8d9d9 !important;
}


.btn-breadcrumb .btn.btn-default:not(:last-child):after {
	border-left: 30px solid #f2f2f2;
	background:url(../images/arrow11.png);
			background-position-y: 62px;
background-position-x: 3px;
}

.btn-breadcrumb .btn.btn-default:not(:last-child):before {
	border-left: 30px solid #ccc;
	background:url(../images/arrow11.png);
		background-position-y: 62px;
background-position-x: 3px;
}
.btn-breadcrumb .btn.btn-default:hover:not(:last-child):after {
	border-left: 30px solid #f2f2f2;
	
	background:url(../images/arrow11.png);
		background-position-y: 62px;
background-position-x: 3px;
}
.btn-breadcrumb .btn.btn-default:hover:not(:last-child):before {
	border-left: 30px solid #ccc;
	background:url(../images/arrow11.png);
		background-position-y: 62px;
background-position-x: 3px;
}
/* The responsive part */
.active {
	color: #21507c !important;
	
}
.btn-breadcrumb > * > div {
	/* With less: .text-overflow(); */
	white-space: nowrap;
	overflow: hidden;
	color:#959595;
	text-overflow: ellipsis;
	font-family: 'OpenSans-Bold';
	
}
.btn-breadcrumb > *:nth-child(n+2) {
}
.btn-file {
 
  overflow: hidden;
}
.btn-file input[type=file] {
  position: absolute;
  top: 0;
  right: 0;
  min-width: 100%;
  min-height: 100%;
  font-size: 100px;
  text-align: right;
  filter: alpha(opacity=0);
  opacity: 0;
  background: red;
  cursor: inherit;
  display: block;
}
input[readonly] {
  background-color: white;
  cursor: text;
}
/* === For phones =================================== */
@media (max-width: 767px) {
.btn-breadcrumb > *:nth-last-child(-n+2) {
	display: block;
}
.btn-breadcrumb > * div {
	max-width: 60px;
}
}

/* === For tablets ================================== */
@media (min-width: 768px) and (max-width:991px) {
.btn-breadcrumb > *:nth-last-child(-n+4) {
}
.btn-breadcrumb > * div {
	max-width: 100px;
}
}

/* === For desktops ================================== */
@media (min-width: 992px) {
.btn-breadcrumb > *:nth-last-child(-n+6) {
	display: block;
}
.btn-breadcrumb > * div {
	max-width: 170px;
}
}
@media (max-width: 320px) {
.btn-breadcrumb > * div {
	max-width: 129px;
}
.btn-breadcrumb .btn:first-child {
padding: 18px 26px 18px 25px;
}
}
